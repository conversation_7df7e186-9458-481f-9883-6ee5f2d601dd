export enum Permission {
  // -----------------General-----------------------
  createOrder                 = 0b100000,
  order                       = 0b1000000,
  // Khách hàng
  customer                    = 0b1000000000,
  product                     = 0b10000000000,
  warehouse                   = 0b100000000000, // quản lý tồn kho | Kho bên FFM
  import                      = 0b1000000000000, // nhập kho
  export                      = 0b10000000000000, // xuất kho
  stockTaking                 = 0b100000000000000, // kiểm kê hàng hóa
  // Quản lý nhân viên
  manageUsers                 = 0b100000000000000000000,
  manageCountries             = 0b10000000000000000000000,
  manageRoles                 = 0b10000000000000000000000000,
  configShipping              = 0b100000000000000000000000000000000,
  configBilling               = 0b10000000000000000000000000000000000,
  createWarehouse             = 0b10000000000000000000000000000000000000,

  // IMPORTANT !! Tách quyền AG vs FFM.

  // -------------------FFM--------------------------
  odz                         = 0b1, // ODZ
  reImports                   = 0b10,
  editOder                    = 0b100,
  dashboardPTPerformance      = 0b1000,
  bulkRaiseTicket             = 0b10000,
  bulkChangeAssignee          = 0b100000000,
  bulkAddTags                 = 0b1000000000000000,
  bulkExportTicket            = 0b10000000000000000,
  bulkCloseTicket             = 0b100000000000000000,
  dashboardOrderTracker       = 0b1000000000000000000,
  bulkTag                     = 0b10000000000000000000,
  tag                         = 0b1000000000000000000000,
  reconciliation              = 0b100000000000000000000000,
  orderSource                 = 0b1000000000000000000000000,
  revertOrder                 = 0b100000000000000000000000000,
  bulkResolveMultiReconciled  = 0b1000000000000000000000000000,
  stockTransferWithdrawal     = 0b10000000000000000000000000000,
  dashboardFluctuation        = 0b100000000000000000000000000000,
  // systemSetting               = 0b1000000000000000000000000000000,
  duplicateOrders             = 0b1000000000000000000000000000000,
  bulkUpdateOrderTotalWeight  = 0b10000000000000000000000000000000,
  extendedUpdate              = 0b1000000000000000000000000000000000,
  expeditedReconciliation     = 0b100000000000000000000000000000000000,
  inventory                   = 0b1000000000000000000000000000000000000, // quản lý tồn kho
  createProduct               = 0b100000000000000000000000000000000000000,
  bulkUpdateOrderType         = 0b1000000000000000000000000000000000000000,
  importOrderReturn           = 0b10000000000000000000000000000000000000000,
  pickList                    = 0b100000000000000000000000000000000000000000,
  packingList                 = 0b1000000000000000000000000000000000000000000,
  handOver                    = 0b10000000000000000000000000000000000000000000,
  packageCode                 = 0b100000000000000000000000000000000000000000000,
  bulkWarehouseTag            = 0b1000000000000000000000000000000000000000000000,
  bulkUpdateOrderMessage      = 0b10000000000000000000000000000000000000000000000,
  bulkUpdateOrderInternalNote = 0b100000000000000000000000000000000000000000000000,
  bulk                        = 0b1000000000000000000000000000000000000000000000000,
  bulkExport                  = 0b10000000000000000000000000000000000000000000000000,
  bulkStatus                  = 0b100000000000000000000000000000000000000000000000000,
  bulk3PL                     = 0b1000000000000000000000000000000000000000000000000000, // Bulk request 3PL
  bulkPrint                   = 0b10000000000000000000000000000000000000000000000000000, // Bulk in đơn hàng
  bulkRequest3pl              = 0b100000000000000000000000000000000000000000000000000000, // Bulk Request Outbound Delivery
  pancakeSyncOrders           = 0b1000000000000000000000000000000000000000000000000000000, // Sync order from pancake
  exportProduct               = 0b10000000000000000000000000000000000000000000000000000000,
  orderAllocationRule         = 0b100000000000000000000000000000000000000000000000000000000,
  multiWarehouse              = 0b1000000000000000000000000000000000000000000000000000000000, // Multi warehouse FFM
  carrier3pl                  = 0b10000000000000000000000000000000000000000000000000000000000, // Carrier account page
  reason                      = 0b1000000000000000000000000000000000000000000000000000000000000, // Reason Cancel
  ptView                      = 0b10000000000000000000000000000000000000000000000000000000000000, // Parcel Tracker View
  ptManager                   = 0b100000000000000000000000000000000000000000000000000000000000000,
}
