import {
  BadGatewayException,
  BadRequestException,
  Headers,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { instanceToPlain, plainToInstance } from 'class-transformer';
import { catalogConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { concat, difference, filter, find, isEmpty, remove, uniq } from 'lodash';
import { ILike, In, Not, Repository } from 'typeorm';
import { UpdateWarehouseUser, WarehouseDto } from '../../dtos/warehouse.dto';
import { SlotWarehouses } from '../../entities/warehouses';
import { WarehouseStatus, WarehouseType } from '../enum/warehouse-type.enum';
import { FilterWarehouse } from '../filters/warehouse.filter';
import {
  AmqpConnection,
  defaultNackError<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  RabbitRPC,
} from '@golevelup/nestjs-rabbitmq';
import { WAREHOUSE } from '../../../../../core/cache/constants/prefix.constant';
import { RedisCacheService } from '../../../../../core/cache/services/redisCache.service';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { WarehouseClientAllocation } from '../../entities/warehouse-client-allocation.entity';
import { Users } from '../../read-entities/identity-entities/Users';
import { UserType } from 'core/enums/user-type.enum';
import { WarehouseClientAllocationDto } from '../../dtos/warehouse-client-allowcation.dto';
import { WarehouseSession } from '../../entities/warehouse-session.entity';
import { WarehouseSessionDto } from '../../dtos/warehouse-session.dto';
import { session } from 'passport';
import { CountryID } from 'core/enums/carrier-code.enum';
import { User } from 'core/entities/identity/user.entity';

@Injectable()
export class WarehouseService {
  constructor(
    @InjectRepository(SlotWarehouses, catalogConnection)
    private whRepository: Repository<SlotWarehouses>,
    @InjectRepository(WarehouseClientAllocation, catalogConnection)
    private wclRepository: Repository<WarehouseClientAllocation>,
    @InjectRepository(WarehouseSession, catalogConnection)
    private wsRepository: Repository<WarehouseSession>,
    private redisCache: RedisCacheService,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  @RabbitRPC({
    exchange: 'ffm-catalog-service-warehouses',
    routingKey: 'get-ffm-warehouse',
    queue: 'ffm-catalog-get-warehouse',
    errorHandler: defaultNackErrorHandler,
  })
  async getWarehouse({ id }) {
    if (!id) {
      return null;
    }
    const key = `${WAREHOUSE}.${id}.`;
    const wh = await this.whRepository.findOne(id, {
      relations: ['senderInformations'],
    });
    await this.redisCache.set(key, wh);
    return wh;
  }

  @RabbitRPC({
    exchange: 'ffm-catalog-service-warehouses',
    routingKey: 'get-ffm-warehouse-client-allocation',
    queue: 'ffm-catalog-get-warehouse-client-allocation',
    errorHandler: defaultNackErrorHandler,
  })
  async getWarehouseClientAllocation({ clientId, countryId, companyId }) {
    if (!clientId || !countryId || !companyId) {
      return null;
    }
    const whAllocation = await this.wclRepository
      .createQueryBuilder('wha')
      .where('wha.id_client = :clientId', { clientId })
      .andWhere('wha.id_country = :countryId', { countryId })
      .andWhere('wha.biz_id = :companyId', { companyId })
      .getOne();
    return whAllocation;
  }

  @RabbitRPC({
    exchange: 'ffm-catalog-service-warehouses',
    routingKey: 'get-main-ffm-warehouse',
    queue: 'ffm-catalog-get-main-ffm-warehouse',
    errorHandler: defaultNackErrorHandler,
  })
  async getMainWarehouse({ countryId, companyId, clientId }) {
    if (!countryId || !companyId || !clientId) {
      return null;
    }
    const wh = await this.whRepository
      .createQueryBuilder('wh')
      .andWhere('clients.id_country = :countryId', { countryId })
      .andWhere('clients.biz_id = :companyId', { companyId })
      .andWhere('clients.id_client = :clientId', { clientId })
      .andWhere('wh.status = :status', { status: WarehouseStatus.active })
      .leftJoinAndSelect('wh.clients', 'clients')
      .getOne();
    return wh;
  }

  @RabbitRPC({
    exchange: 'ffm-catalog-service-warehouses',
    routingKey: 'update-ffm-warehouse-allocation',
    queue: 'ffm-catalog-update-warehouse-allocation',
    errorHandler: rmqErrorsHandler,
  })
  async updateWarehouseAllocation({ creatorId, countryIds, companyId, clientId }) {
    if (!creatorId || !clientId || !companyId || !countryIds || countryIds?.length == 0) {
      return new Nack();
    }
    const [warehouseAllocations, whs] = await Promise.all([
      this.wclRepository
        .createQueryBuilder()
        .where('id_client = :clientId', { clientId })
        .andWhere('biz_id = :companyId', { companyId })
        .andWhere('id_country in (:...countryIds)', {
          countryIds: countryIds?.map((item: any) => Number(item)),
        })
        .getMany(),
      this.whRepository
        .createQueryBuilder('wh')
        .andWhere({
          bizId: companyId,
        })
        .andWhere('country_code in (:...countryIds)', {
          countryIds: countryIds?.map((item: any) => Number(item)),
        })
        .andWhere('wh.status = :status', { status: WarehouseStatus.active })
        .getMany(),
    ]);

    if (whs?.length == 0) return new Nack();

    await this.wclRepository
      .update(
        {
          clientId,
          bizId: companyId,
          countryId: Not(In(countryIds?.map((item: any) => Number(item)))),
        },
        { lastEditorId: creatorId },
      )
      .catch(err => {
        console.log(err);
        return new Nack();
      });

    await this.wclRepository
      .createQueryBuilder()
      .delete()
      .from(WarehouseClientAllocation)
      .where('id_client = :clientId', { clientId })
      .andWhere('biz_id = :companyId', { companyId })
      .andWhere('id_country not in (:...countryIds)', {
        countryIds: countryIds?.map((item: any) => Number(item)),
      })
      .execute()
      .catch(err => {
        console.log(err);
        return new Nack();
      });

    const params: WarehouseClientAllocation[] = [];
    countryIds.forEach((id: number) => {
      const warehouseAllocation = find(warehouseAllocations, { clientId, countryId: id });
      if (!!warehouseAllocation) {
        params.push(warehouseAllocation);
      } else {
        let mainWarehouse = find(whs, { type: WarehouseType.main, countryCode: id });
        if (!mainWarehouse?.id)
          mainWarehouse = find(whs, { type: WarehouseType.sub, countryCode: id });
        if (!!mainWarehouse?.id) {
          const item = new WarehouseClientAllocation();
          item.bizId = companyId;
          item.countryId = id;
          item.warehouseId = Number(mainWarehouse?.id);
          item.clientId = clientId;
          item.creatorId = creatorId;
          item.lastEditorId = creatorId;
          params.push(item);
        }
      }
    });
    if (params?.length > 0)
      await this.wclRepository.save(params).catch(err => {
        console.log(err);
        return new Nack();
      });

    return new Nack();
  }

  async detail(id: number, header): Promise<SlotWarehouses> {
    return this.whRepository.findOne(id, {
      where: qb => {
        if (!!header['country-ids']) {
          qb.andWhere({
            countryCode: header['country-ids'],
          });
        }
      },
      relations: ['senderInformations', 'clients'],
    });
  }

  async detailAllocation(request, header, id: number): Promise<WarehouseClientAllocation> {
    if (!header['country-ids'] || !id) throw new BadRequestException('');

    return this.wclRepository
      .createQueryBuilder()
      .where('id_client = :clientId', { clientId: id })
      .andWhere('biz_id = :companyId', { companyId: request?.user?.companyId })
      .andWhere('id_country = :countryId', { countryId: Number(header['country-ids']) })
      .getOneOrFail();
  }

  async findAll(
    pagination: PaginationOptions,
    query: FilterWarehouse,
    request,
    @Headers() header,
  ): Promise<[SlotWarehouses[], number]> {
    const { type, name, ids, countryId, getAll, countryIds, displayIds, status } = query;

    if (getAll) {
      return this.whRepository.findAndCount({
        where: qb => {
          qb.andWhere({
            bizId: request?.user?.companyId,
          });

          if (countryIds)
            qb.andWhere({
              countryCode: In(concat(countryIds)),
            });

          if (!!header['country-ids']) {
            qb.andWhere({
              countryCode: header['country-ids'],
            });
          }

          if (ids)
            qb.andWhere({
              id: In(ids),
            });
          if (displayIds)
            qb.andWhere({
              displayId: In(displayIds),
            });
          if (status) {
            qb.andWhere({ status });
          }
        },
        relations: ['clients', 'senderInformations', 'warehouseSessions'],
        order: {
          insertedAt: 'DESC',
        },
      });
    }

    const data = await this.whRepository.findAndCount({
      take: pagination?.limit,
      skip: pagination?.skip,
      where: qb => {
        if (ids)
          qb.andWhere({
            id: In(ids),
          });

        if (countryIds)
          qb.andWhere({
            countryCode: In(concat(countryIds)),
          });

        if (countryId)
          qb.andWhere({
            countryCode: countryId,
          });

        if (!!type)
          qb.andWhere({
            type,
          });

        if (!!status)
          qb.andWhere({
            status,
          });

        if (!!header['country-ids']) {
          qb.andWhere({
            countryCode: header['country-ids'],
          });
        }

        if (displayIds)
          qb.andWhere({
            displayId: In(displayIds),
          });

        qb.andWhere({
          bizId: request?.user?.companyId,
        });

        if (name) {
          qb.andWhere([
            {
              name: ILike(`%${name}%`),
            },
            {
              phoneNumber: ILike(`%${name}%`),
            },
            {
              displayId: ILike(`%${name}%`),
            },
          ]);
        }
        // qb.andWhere({status: WarehouseStatus.active});
      },
      relations: ['clients', 'senderInformations', 'warehouseSessions'],
      order: {
        insertedAt: 'DESC',
      },
    });
    return data;
  }
  async getAllCountry(
    pagination: PaginationOptions,
    query: FilterWarehouse,
    request,
    @Headers() header,
  ): Promise<SlotWarehouses[]> {
    const { type, name, ids, countryId, getAll, countryIds, displayIds, status, getOriginWh } = query;
    const { id, companyId } = request?.user;
    const { data: user } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'get-user',
      payload: { id },
      timeout: 5000,
    });
    const userWh = user?.warehouses;
    const userCountry = user?.countries;

    const qb = this.whRepository.createQueryBuilder('wh');

    if (ids) {
      qb.andWhere('wh.id IN (:...ids)', { ids });
    }

    if (countryIds) {
      qb.andWhere('wh.countryCode IN (:...countryIds)', { countryIds });
    }

    if (countryId) {
      qb.andWhere('wh.countryCode = :countryId', { countryId });
    }

    if (type) {
      qb.andWhere('wh.type = :type', { type });
    }

    if (status) {
      qb.andWhere('wh.status = :status', { status });
    }

    if (displayIds) {
      qb.andWhere('wh.displayId IN (:...displayIds)', { displayIds });
    }

    if (name) {
      qb.andWhere(
        `(wh.name ILIKE :name OR wh.phoneNumber ILIKE :name OR wh.displayId ILIKE :name)`,
        { name: `%${name}%` },
      );
    }

    if(!getAll){
      if (!isEmpty(userCountry)) {
        qb.andWhere('wh.country_code IN (:...userCountry)', { userCountry });
      }
      if(getOriginWh && !isEmpty(userWh)){
        qb.andWhere('wh.id IN (:...userWh)', { userWh });
      }
    }
    qb.andWhere('wh.bizId = :companyId', { companyId });
    if(!getOriginWh) qb.orWhere('wh.is_return_default = true');
    qb.orderBy('wh.name', 'ASC').addOrderBy('wh.countryCode', 'ASC');

    const data = await qb.getMany();

    for (const item of data) {
      item.prefix = CountryID[`${item?.countryCode}`];
    }
    return data;
  }

  async removeWarehouseAllocation(request, data: WarehouseClientAllocationDto): Promise<any> {
    await this.wclRepository
      .update(
        {
          clientId: data?.clientId,
          bizId: request?.user?.companyId,
          warehouseId: data?.warehouseId,
          countryId: data?.countryId,
        },
        { lastEditorId: request?.user?.id },
      )
      .catch(err => {
        console.log(err);
        throw new BadRequestException('');
      });

    await this.wclRepository
      .createQueryBuilder()
      .delete()
      .from(WarehouseClientAllocation)
      .where('id_client = :clientId', { clientId: data?.clientId })
      .andWhere('biz_id = :companyId', { companyId: request?.user?.companyId })
      .andWhere('id_country = :countryId', { countryId: data?.countryId })
      .andWhere('id_warehouse = :warehouseId', { warehouseId: data?.warehouseId })
      .execute()
      .catch(err => {
        console.log(err);
        throw new BadRequestException('');
      });

    return true;
  }

  async createWarehouseAllocation(
    request,
    data: WarehouseClientAllocationDto,
  ): Promise<WarehouseClientAllocation> {
    const wcl = await this.wclRepository
      .createQueryBuilder()
      .where('id_client = :clientId', { clientId: data?.clientId })
      .andWhere('biz_id = :companyId', { companyId: request?.user?.companyId })
      .andWhere('id_country = :countryId', { countryId: data?.countryId })
      .andWhere('id_warehouse = :warehouseId', { warehouseId: data?.warehouseId })
      .getOne();
    if (!!wcl) return wcl;

    await this.wclRepository
      .update(
        {
          clientId: data?.clientId,
          bizId: request?.user?.companyId,
          warehouseId: data?.warehouseId,
          countryId: data?.countryId,
        },
        { lastEditorId: request?.user?.id },
      )
      .catch(err => {
        console.log(err);
        throw new BadRequestException('');
      });

    await this.wclRepository
      .createQueryBuilder()
      .delete()
      .from(WarehouseClientAllocation)
      .where('id_client = :clientId', { clientId: data?.clientId })
      .andWhere('biz_id = :companyId', { companyId: request?.user?.companyId })
      .andWhere('id_country = :countryId', { countryId: data?.countryId })
      .execute()
      .catch(err => {
        console.log(err);
        throw new BadRequestException('');
      });

    const item = new WarehouseClientAllocation();
    item.bizId = request?.user?.companyId;
    item.countryId = data?.countryId;
    item.warehouseId = data?.warehouseId;
    item.clientId = data?.clientId;
    item.creatorId = request?.user?.id;
    item.lastEditorId = request?.user?.id;

    await this.wclRepository.save(item).catch(err => {
      console.log(err);
      throw new BadRequestException('');
    });

    return item;
  }

  async createWh(request, data: WarehouseDto): Promise<SlotWarehouses> {
    if (!!!request?.user?.companyId) {
      throw new UnauthorizedException();
    }

    const countryId = data?.countryCode;

    const whs = await this.whRepository
      .createQueryBuilder('wh')
      .andWhere({
        countryCode: countryId,
        bizId: `${request?.user?.companyId}`,
      })
      .getMany();

    let users: Users[];
    if (whs?.length == 0) {
      try {
        const { data } = await this.amqpConnection.request({
          exchange: 'identity-service-users',
          routingKey: 'find-user',
          payload: {
            filters: { type: UserType.customer, countries: countryId },
            pagination: { limit: 1000 },
            header: {},
            request: { user: { companyId: request?.user?.companyId } },
          },
          timeout: 10000,
        });
        users = data as Users[];
      } catch (error) {
        console.log(error);
        throw new BadRequestException();
      }
    }

    if (data.type === WarehouseType.main && whs?.length == 0) {
      const main = find(whs, { type: WarehouseType.main });

      if (!!main?.id) throw new BadRequestException('Main warehouse of country already exists');
    }
    const { senderInformations } = data;
    if (senderInformations.length > 0) {
      for (const item of senderInformations) {
        item.creatorId = request?.user?.id;
      }
    }
    if (data.isReturnDefault) {
      const whsId = whs.map(wh => wh.id);
      await this.whRepository.update({ id: In(whsId) }, { isReturnDefault: false });
    }

    const param = plainToInstance(SlotWarehouses, {
      ...data,
      updatedAt: new Date(),
      insertedAt: new Date(),
      creatorId: request?.user?.id,
      lastEditorId: request?.user?.id,
      bizId: request?.user?.companyId,
    });

    const result = await this.whRepository.save(param).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });

    const newWhSessions: WarehouseSession[] = [];
    if (data?.whSessions?.length > 0) {
      data?.whSessions.forEach((s: WarehouseSessionDto) => {
        const item = new WarehouseSession();
        item.name = s.name;
        item.sessionStart = s.sessionStart;
        item.sessionEnd = s.sessionEnd;
        item.remark = s.remark;
        item.warehouseId = result?.id;
        item.updatedAt = new Date();
        item.createdAt = new Date();
        newWhSessions.push(item);
      });
      // check data duplicate
      const newWhSessionsMap = {};
      newWhSessions.forEach(session => {
        const startTimeInMinutes = this.toMinutes(session.sessionStart);
        const endTimeInMinutes = this.toMinutes(session.sessionEnd);
        // if (startTimeInMinutes >= endTimeInMinutes)
        //   throw new BadRequestException('Start time must be smaller than end time');
        for (const existingTime in newWhSessionsMap) {
          const existingStartTimeInMinutes = Number(existingTime);
          const existingEndTimeInMinutes = newWhSessionsMap[existingTime];
          if (
            startTimeInMinutes <= existingEndTimeInMinutes &&
            endTimeInMinutes >= existingStartTimeInMinutes
          ) {
            throw new BadRequestException('Cannot create sessions with overlapping time.');
          }
        }
        newWhSessionsMap[startTimeInMinutes] = endTimeInMinutes;
      });
      await this.wsRepository.save(newWhSessions).catch(err => {
        if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
        return err;
      });
    }

    if (!data?.returnWarehouseId) {
      await this.whRepository
        .update({ id: result?.id }, { returnWarehouseId: result?.id })
        .catch(err => {
          if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
          return err;
        });
    }

    if (whs?.length == 0 && users?.length > 0 && !!result?.id) {
      const params: WarehouseClientAllocation[] = [];
      users.forEach((u: Users) => {
        const item = new WarehouseClientAllocation();
        item.bizId = request?.user?.companyId;
        item.countryId = Number(countryId);
        item.warehouseId = Number(result?.id);
        item.clientId = Number(u?.id);
        item.creatorId = request?.user?.id;
        item.lastEditorId = request?.user?.id;
        params.push(item);
      });
      await this.wclRepository.save(params).catch(err => {
        console.log(err);
        return err;
      });
    }

    return result;
  }

  async updateUserWh(request, data: UpdateWarehouseUser): Promise<SlotWarehouses> {
    const needRemoveIds = difference(data?.oldIds, data?.newIds);
    const needAddIds = difference(data?.newIds, data?.oldIds);

    const mergeIds = concat(needRemoveIds, needAddIds);
    if (mergeIds.length > 0) {
      const whs = await this.whRepository.find({
        where: {
          id: In(mergeIds),
          bizId: request?.user?.companyId,
          status: WarehouseStatus.active,
        },
      });
      const newData = whs.map(el => {
        el.userIds = el?.userIds ?? [];
        if (needRemoveIds.includes(el?.id?.toString())) {
          remove(el?.userIds, function(n: any) {
            return n == data?.id?.toString();
          });
        } else {
          el.userIds.push(data?.id?.toString());
        }

        el.userIds = uniq(el?.userIds ?? []);
        delete el.insertedAt;
        delete el.updatedAt;

        el.lastEditorId = request?.user?.id;

        return el;
      });
      const res = await this.whRepository.save(newData).catch(err => {
        if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
        return err;
      });
      return res;
    }
    throw new Error('');
  }

  async updateWh(id: number, data: WarehouseDto, request): Promise<SlotWarehouses> {
    const wh = await this.whRepository.findOne(id);

    if (!wh) throw new BadGatewayException('Not found warehouse');
    // if (!!main?.id && data?.type == WarehouseType.main)
    //   throw new BadRequestException('Main warehouse of country already exists');
    if (data?.senderInformations.length > 0) {
      for (const item of data?.senderInformations) {
        if (!item?.creatorId) item.creatorId = request?.user?.id;
        item.lastUpdatedBy = request?.user?.id;
      }
    }
    const param = plainToInstance(SlotWarehouses, {
      ...wh,
      updatedAt: new Date(),
      creatorId: request?.user?.id,
      lastEditorId: request?.user?.id,
      name: data?.name,
      phoneNumber: data?.phoneNumber,
      fullAddress: data?.fullAddress,
      provinceId: data?.provinceId,
      districtId: data?.districtId,
      communeId: data?.communeId,
      postCode: data?.postCode,
      countryCode: data?.countryCode,
      type: data?.type,
      lat: data?.lat,
      long: data?.long,
      userIds: data?.userIds,
      displayId: data?.displayId,
      gstNo: data?.gstNo ? data?.gstNo : wh?.gstNo,
      isOnSender: data?.isOnSender == true ? true : false,
      senderInformations: data?.senderInformations,
      status: data?.status,
      returnWarehouseId: data?.returnWarehouseId ?? wh?.returnWarehouseId,
      handoverWorkflow: data?.handoverWorkflow,
      isReturnDefault: data?.isReturnDefault,
    });
    let whs: SlotWarehouses[];
    if (data?.status == WarehouseStatus.inactive) {
      whs = await this.whRepository
        .createQueryBuilder('wh')
        .where('wh.return_warehouse_id = :id', { id })
        .andWhere('wh.biz_id = :companyId', { companyId: request.user.companyId })
        .getMany();

      for (const wh of whs) {
        wh.returnWarehouseId = Number(wh.id);
      }
    }
    if (data?.isReturnDefault) {
      const existedWhs = await this.whRepository
        .createQueryBuilder('wh')
        .where('wh.is_return_default = true')
        .andWhere('wh.biz_id = :companyId', { companyId: request.user.companyId })
        .andWhere('wh.country_code = :countryCode', { countryCode: data?.countryCode })
        .getMany();
      if (existedWhs?.length > 0) {
        const existedWhsId = existedWhs.map(wh => wh.id);
        await this.whRepository.update({ id: In(existedWhsId) }, { isReturnDefault: false });
      }
    }

    const params = !isEmpty(whs) ? [...whs, param] : [param];
    const warehouse = await this.whRepository.save(params).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });
    const key = `${WAREHOUSE}.${id}.`;
    await this.redisCache.set(key, warehouse);
    return warehouse;
  }

  toMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

  async createWhSession(
    warehouseId: number,
    data: WarehouseSessionDto,
    request,
    header,
  ): Promise<WarehouseSession> {
    if (!!!request?.user?.companyId) {
      throw new UnauthorizedException();
    }

    const wh = await this.whRepository.findOne(warehouseId);
    if (!wh) throw new BadGatewayException('Not found warehouse');

    const newWhSession = plainToInstance(WarehouseSession, {
      ...data,
      updatedAt: new Date(),
      createddAt: new Date(),
      warehouseId,
    });

    const existedWhSession = await this.wsRepository.find({
      where: {
        warehouseId,
      },
    });

    if (existedWhSession?.length > 0) {
      const existedWhSessionsMap = existedWhSession.reduce((map, session) => {
        const startTimeInMinutes = this.toMinutes(session.sessionStart);
        const endTimeInMinutes = this.toMinutes(session.sessionEnd);
        map[startTimeInMinutes] = endTimeInMinutes;
        return map;
      }, {});
      console.log('1', existedWhSessionsMap);
      const newStartTimeInMinutes = this.toMinutes(newWhSession.sessionStart);
      const newEndTimeInMinutes = this.toMinutes(newWhSession.sessionEnd);
      // if (newStartTimeInMinutes >= newEndTimeInMinutes)
      //   throw new BadRequestException('Start time must be less than end time');
      for (const existingTime in existedWhSessionsMap) {
        const existingEndTimeInMinutes = existedWhSessionsMap[existingTime];
        if (
          newStartTimeInMinutes <= existingEndTimeInMinutes &&
          newEndTimeInMinutes >= Number(existingTime)
        ) {
          throw new BadRequestException(
            'Cannot be created session that overlaps in time with a previously created session.',
          );
        }
      }
    }

    const result = await this.wsRepository.save(newWhSession).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });

    return result;
  }

  async updateWhSession(
    warehouseId: number,
    sessionId: number,
    data: WarehouseSessionDto,
    request,
  ): Promise<WarehouseSession> {
    if (!!!request?.user?.companyId) {
      throw new UnauthorizedException();
    }

    const wh = await this.whRepository.findOne(warehouseId);
    if (!wh) throw new BadGatewayException('Not found warehouse');

    const warehouseSession = await this.wsRepository.findOne(sessionId);
    if (!warehouseSession) throw new BadGatewayException('Not found warehouse session');

    const params = plainToInstance(WarehouseSession, {
      ...warehouseSession,
      updatedAt: new Date(),
      name: data?.name,
      sessionStart: data?.sessionStart,
      sessionEnd: data?.sessionEnd,
      remark: data?.remark,
    });

    const existedWhSession = await this.wsRepository.find({
      where: {
        warehouseId: warehouseSession.warehouseId,
        id: Not(warehouseSession.id),
      },
    });
    if (existedWhSession?.length > 0) {
      const existedWhSessionsMap = existedWhSession.reduce((map, session) => {
        const startTimeInMinutes = this.toMinutes(session.sessionStart);
        const endTimeInMinutes = this.toMinutes(session.sessionEnd);
        map[startTimeInMinutes] = endTimeInMinutes;
        return map;
      }, {});
      const newStartTimeInMinutes = this.toMinutes(params.sessionStart);
      const newEndTimeInMinutes = this.toMinutes(params.sessionEnd);
      if (newStartTimeInMinutes >= newEndTimeInMinutes)
        throw new BadRequestException('Start time must be less than end time');
      for (const existingTime in existedWhSessionsMap) {
        const existingEndTimeInMinutes = existedWhSessionsMap[existingTime];
        if (
          Number(existingTime) !== this.toMinutes(params.sessionStart) &&
          newStartTimeInMinutes <= existingEndTimeInMinutes &&
          newEndTimeInMinutes >= Number(existingTime)
        ) {
          throw new BadRequestException(
            'Cannot be updated session that overlaps in time with a previously created session.',
          );
        }
      }
    }

    const result = await this.wsRepository.save(params).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });

    return result;
  }

  async deleteWhSession(
    warehouseId: number,
    sessionId: number,
    request,
  ): Promise<WarehouseSession> {
    if (!!!request?.user?.companyId) {
      throw new UnauthorizedException();
    }

    const wh = await this.whRepository.findOne(warehouseId);
    if (!wh) throw new BadGatewayException('Not found warehouse');

    const warehouseSession = await this.wsRepository.findOne(sessionId);
    if (!warehouseSession) throw new BadGatewayException('Not found warehouse session');

    await this.wsRepository.delete(sessionId).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });

    return warehouseSession;
  }

  async getAllWhSession(warehouseId: number): Promise<WarehouseSession[]> {
    if (!warehouseId) return null;
    const whSession = await this.wsRepository.find({
      where: { warehouseId },
      order: { sessionStart: 'ASC' },
    });
    return whSession;
  }

  async getWhSession(warehouseId: number, sessionId: number): Promise<WarehouseSession> {
    if (!warehouseId) return null;
    if (!sessionId) return null;
    const whSession = await this.wsRepository.findOne({
      where: { warehouseId, id: sessionId },
    });
    return whSession;
  }

  @RabbitRPC({
    exchange: 'ffm-catalog-service-warehouses',
    routingKey: 'get-ffm-warehouse-season',
    queue: 'ffm-catalog-get-warehouse-season',
    errorHandler: defaultNackErrorHandler,
  })
  async getWarehouseSS({ ids, whIds }) {
    if (isEmpty(ids) && isEmpty(whIds)) {
      return null;
    }
    const query = this.wsRepository.createQueryBuilder('ws');
    if (ids) {
      query.andWhere('ws.id IN (:...ids)', { ids });
    }
    if (whIds) {
      query.andWhere('ws.id_warehouse IN (:...whIds)', { whIds });
    }
    const whss = await query.getMany();
    return whss;
  }
}
