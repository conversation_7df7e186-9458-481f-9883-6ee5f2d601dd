import { MenuNavigation } from '../model/navigation.model';
import * as _ from 'lodash';
import { plainToInstance } from 'class-transformer';
import { Permission } from 'core/enums/permission.enum';
import * as FFM from 'core/enums/permission-ffm.enum';
import { TitleChildrenMenu } from 'apps/identity-api/src/enums/menu.enum';

export const getMenu = (roles: Record<string, string[]>, data: MenuNavigation[]) => {
  const menu = _.cloneDeep(data);

  if (_.isEmpty(menu)) {
    return undefined;
  }
  const value = plainToInstance(
    MenuNavigation,
    menu.filter(i => {
      i.children = getMenu(roles, i?.children || []);
      i.authorized = Object.keys(roles).filter(
        key => !i.authority || !_.isEmpty(_.intersection(i.authority, roles[key])),
      );
      if (!!i.authority && i.authority?.length == 0) return true;
      return !_.isEmpty(i.authorized);
    }),
  );
  return value;
};

export const navigationMenu: MenuNavigation[] = [
  {
    path: '/dashboard',
    name: '<PERSON><PERSON><PERSON> c<PERSON><PERSON>',
    translateKey: 'dashboard',
    icon: 'dashboard',
    authority: [
      Permission.ordersReport,
      Permission.carePageManagerReport,
      Permission.carePageStaffReport,
      Permission.telesalesManagerReport,
      Permission.telesalesReport,
      Permission.marketing,
      Permission.marketingManagerReport,
    ],
    children: [
      {
        path: '/dashboard/order-shipment-overview',
        name: 'Tổng quan',
        translateKey: 'order_shipment_overview',
        exact: true,
        authority: [Permission.ordersReport],
      },
      {
        path: '/dashboard/care-page-manager/overview',
        name: 'Quản lý CarePage',
        translateKey: 'care_page_manager',
        exact: true,
        authority: [
          Permission.carePageManagerReport,
          // Permission.marketing
        ],
      },
      {
        path: '/dashboard/care-page-staff/overview',
        name: 'Nhân viên CarePage',
        translateKey: 'care_page_staff',
        exact: true,
        authority: [Permission.carePageStaffReport],
      },
      {
        path: '/dashboard/telesales-manager/overview',
        name: 'Quản lý Telesales',
        translateKey: 'telesales_manager',
        exact: true,
        authority: [
          Permission.telesalesManagerReport,
          // Permission.marketing,
        ],
      },
      {
        path: '/dashboard/telesales-staff/overview',
        name: 'Nhân viên Telesales',
        translateKey: 'telesales_staff',
        exact: true,
        authority: [Permission.telesalesReport],
      },
      {
        path: '/marketing-dashboard/adsmanager',
        name: 'marketing',
        translateKey: 'marketing',
        exact: true,
        authority: [Permission.marketingManagerReport],
      },
    ],
    group: 'system',
  },
  {
    path: '/orders',
    name: 'Đơn hàng',
    translateKey: 'order',
    icon: 'order',
    group: 'system',
    authority: [Permission.createOrder, Permission.order, Permission.orderDraft],
    children: [
      {
        path: '/orders/manager',
        name: 'Quản lý đơn hàng',
        translateKey: 'order_manager',
        exact: true,
        authority: [Permission.order],
      },
      {
        path: '/orders/create',
        name: 'Tạo đơn',
        translateKey: 'order_create',
        exact: true,
        authority: [Permission.createOrder],
      },
      {
        path: '/orders/update/bulk',
        name: 'Cập nhật đơn',
        translateKey: 'bulk_update',
        exact: true,
        authority: [Permission.order],
      },
      {
        path: '/orders/draft',
        name: 'Đơn nháp',
        translateKey: 'order_draft',
        exact: true,
        authority: [Permission.orderDraft],
      },
    ],
  },
  {
    path: '/customers/manager',
    name: 'Quản lý khách hàng',
    translateKey: 'custormer_manager',
    icon: 'customer',
    authority: [Permission.customer],
    group: 'system',
    // children: [
    //   {
    //     path: '/customers/manager',
    //     name: 'Quản lý khách hàng',
    //     translateKey: 'custormer_manager',
    //     authority: [Permission.customer],
    //     exact: true,
    //   },
    // ],
  },

  {
    path: '/products',
    name: 'Sản phẩm',
    translateKey: 'product',
    icon: 'product',
    authority: [Permission.product],
    group: 'system',
    children: [
      {
        path: '/products/manager',
        name: 'Quản lý sản phẩm',
        translateKey: 'product_manager',
        exact: true,
        authority: [Permission.product],
      },
      {
        path: '/products/suppliers',
        name: 'Quản lý nhà cung cấp',
        translateKey: 'product_supplier',
        exact: true,
        authority: [Permission.product],
      },
    ],
  },
  {
    path: '/warehouses',
    name: 'Tồn kho',
    translateKey: 'warehouse',
    icon: 'warehouse',
    group: 'system',
    authority: [
      Permission.warehouse,
      Permission.import,
      Permission.export,
      Permission.stockTaking,
      Permission.returningChecking,
      Permission.createWarehouse,
    ],
    children: [
      {
        path: '/products/warehouses',
        name: 'Quản lý kho',
        translateKey: 'warehouse_manager',
        exact: true,
        authority: [Permission.warehouse],
      },
      {
        path: '/stock-move/import',
        name: 'Nhập hàng',
        translateKey: 'stock_move_import',
        exact: true,
        authority: [Permission.import],
      },
      {
        path: '/stock-move/export',
        name: 'Xuất hàng',
        translateKey: 'stock_move_export',
        exact: true,
        authority: [Permission.export],
      },
      {
        path: '/stock-move/inventory',
        name: 'Kiểm kê hàng hóa',
        translateKey: 'stock_move_manager',
        exact: true,
        authority: [Permission.stockTaking],
      },
      {
        path: '/orders/check-goods',
        name: 'Kiểm hàng hoàn',
        translateKey: 'check_goods',
        exact: true,
        authority: [Permission.returningChecking],
      },
    ],
  },
  {
    path: '/telesales',
    name: 'Telesales',
    translateKey: 'telesales',
    icon: 'telesales',
    group: 'sale',
    authority: [
      Permission.telesalesLeads,
      Permission.telesalesManualDistribution,
      Permission.telesalesAutoDistribution,
      Permission.telesalesConfigurations,
    ],
    children: [
      {
        path: '/telesales/leads-management',
        name: 'Quản lý leads',
        translateKey: 'leads_management',
        exact: true,
        authority: [Permission.telesalesLeads],
      },
      {
        path: '/telesales/data-manual-distribution',
        name: 'Chia thủ công',
        translateKey: 'data_manual_distribution',
        exact: true,
        authority: [Permission.telesalesManualDistribution],
      },
      {
        path: '/telesales/data-auto-distribution',
        name: 'Chia tự động',
        translateKey: 'data_auto_distribution',
        exact: true,
        authority: [Permission.telesalesAutoDistribution],
      },
      {
        path: '/telesales/configurations',
        name: 'Cấu hình định mức',
        translateKey: 'telesales_configurations',
        exact: true,
        authority: [Permission.telesalesConfigurations],
      },
    ],
  },
  {
    path: '/sale-care-page',
    name: 'Care Page',
    translateKey: 'sale_care_page',
    icon: 'sale_care_page',
    group: 'sale',
    authority: [Permission.chat, Permission.automationConfig, Permission.carePageSettings],
    children: [
      {
        path: '/sale-care-page',
        name: 'Care Page',
        translateKey: 'sale_care_page_group',
        icon: 'sale_care_page',
        group: 'sale',
        exact: true,
        authority: [Permission.chat],
      },
      {
        path: '/marketing-automation/automation-configs',
        name: 'Automation',
        translateKey: 'automation_config',
        icon: 'marketing_automation_configs',
        group: 'sale',
        exact: true,
        authority: [Permission.automationConfig],
      },
      {
        path: '/sale-care-page/limitation-settings',
        name: 'Cấu hình định mức',
        translateKey: 'care_page_limitation_settings',
        exact: true,
        authority: [Permission.carePageSettings],
      },
      {
        path: '/sale-care-page/message-classification',
        name: 'Phân loại tin nhắn',
        translateKey: 'care_page_message_classification',
        exact: true,
        authority: [],
      },
    ],
  },
  {
    path: '/marketing-automation/manage-bots',
    name: 'Chat Bot',
    translateKey: 'manage_bots',
    icon: 'marketing_automation_manage_bots',
    exact: true,
    group: 'marketing',
    authority: [Permission.manageBots],
  },
  {
    path: '/marketing-automation/manage-pages',
    name: 'Fanpage',
    translateKey: 'manage_pages',
    icon: 'marketing_automation_manage_pages',
    exact: true,
    group: 'marketing',
    authority: [Permission.managePages],
  },
  {
    path: '/marketing-automation/manage-campaigns',
    name: 'Chiến dịch',
    translateKey: 'manage_campaigns',
    icon: 'marketing_automation_manage_campains',
    exact: true,
    group: 'marketing',
    authority: [Permission.manageCampaigns],
  },
  // {
  //   path: '/transactions',
  //   name: 'Giao dịch',
  //   translateKey: 'transactions',
  //   icon: 'transactions',
  //   authority: [Permission.manageTransactions],
  //   children: [
  //     {
  //       path: '/transactions/manager',
  //       name: 'Quản lý giao dịch',
  //       translateKey: 'transactions_manager',
  //       authority: [Permission.manageTransactions],
  //       exact: true,
  //     },
  //   ],
  // },

  {
    path: '/setting',
    name: 'Cấu hình',
    translateKey: 'setting',
    icon: 'setting',
    includesGroup: true,
    group: 'setting',
    authority: [
      Permission.manageUsers,
      Permission.manageShifts,
      Permission.manageCountries,
      Permission.manageProjects,
      Permission.manageTeams,
      Permission.manageRoles,

      //cấu hình
      Permission.configReportReasons,
      Permission.configCancelReasons,
      Permission.configOrderSources,
      Permission.configTags,
      Permission.configCareFailReasons,
      Permission.configPrintNotes,

      //cài đặt
      Permission.configShipping,
      Permission.configPartners,
      Permission.configBilling,

      Permission.integrateServices,
      Permission.configLandingPage,

      // Cấu hình Facebook Pixel
      // Permission.marketing
    ],
    children: [
      {
        path: '/setting/projects',
        name: 'Dự án',
        translateKey: 'projects',
        icon: 'project',
        exact: true,
        authority: [Permission.manageProjects],
        group: 'configurations',
      },
      {
        path: '/setting/countries',
        name: 'Thị trường',
        icon: 'country',
        translateKey: 'countries',
        exact: true,
        authority: [Permission.manageCountries],
        group: 'configurations',
      },
      {
        path: '/setting/employees',
        name: 'Nhân viên',
        icon: 'employee',
        translateKey: 'employees',
        exact: true,
        authority: [Permission.manageUsers],
        group: 'configurations',
      },
      {
        path: '/setting/shifts',
        name: 'Ca làm việc',
        icon: 'shift',
        translateKey: 'shift',
        exact: true,
        authority: [Permission.manageShifts],
        group: 'configurations',
        children: [
          {
            path: '/setting/shifts',
            name: 'Ca làm việc',
            translateKey: 'shift',
            exact: true,
            authority: [Permission.manageShifts],
            group: 'config_shifts',
          },
          {
            path: '/setting/employee-shifts',
            name: 'Phân công ca làm việc',
            translateKey: 'employee_shifts',
            exact: true,
            authority: [Permission.manageShifts],
            group: 'config_shifts',
          },
        ],
      },
      {
        path: '/setting/teams',
        name: 'Nhóm/ Phòng ban',
        icon: 'team',
        translateKey: 'teams',
        exact: true,
        authority: [Permission.manageTeams],
        group: 'configurations',
      },
      {
        path: '/setting/roles',
        name: 'Vai trò/ Nhóm quyền',
        icon: 'role',
        translateKey: 'roles',
        exact: true,
        authority: [Permission.manageRoles],
        group: 'configurations',
      },
      {
        path: '/setting/tags',
        name: 'Nhóm thẻ/ thẻ đơn hàng',
        translateKey: 'tags',
        exact: true,
        icon: 'tag',
        authority: [Permission.configTags],
        group: 'configurations',
      },
      {
        path: 'setting/order',
        name: 'Đơn hàng',
        translateKey: 'order',
        icon: 'setting_order',
        exact: true,
        authority: [
          Permission.configOrderSources,
          Permission.configReportReasons,
          Permission.configCancelReasons,
          Permission.configCareFailReasons,
          Permission.configPrintNotes,
        ],
        group: 'configurations',
        children: [
          {
            path: '/setting/order-sources',
            name: 'Nguồn hàng',
            translateKey: 'order_sources',
            exact: true,
            authority: [Permission.configOrderSources],
            group: 'config_order',
          },
          {
            path: '/setting/report-reasons',
            name: 'Lý do báo xấu',
            translateKey: 'report_reasons',
            authority: [Permission.configReportReasons],
            exact: true,
            group: 'config_order',
          },
          {
            path: '/setting/cancel-reasons',
            name: 'Lý do hoàn/ hủy',
            translateKey: 'cancel_reasons',
            exact: true,
            authority: [Permission.configCancelReasons],
            group: 'config_order',
          },
          {
            path: '/setting/care-fail-reasons',
            name: 'Lý do từ chối chăm sóc',
            translateKey: 'care_fail_reasons',
            exact: true,
            authority: [Permission.configCareFailReasons],
            group: 'config_order',
          },
          {
            path: '/setting/print-notes',
            name: 'Ghi chú in đơn',
            translateKey: 'print_notes',
            exact: true,
            authority: [Permission.configPrintNotes],
            group: 'config_order',
          },
        ],
      },
      {
        path: '/setting/translations',
        name: 'Language Translation',
        translateKey: 'setting_translation',
        authority: [Permission.manageCountries],
        group: 'configurations',
        icon: 'translation',
        exact: true,
      },
      {
        path: '/setting/landing-page',
        name: 'Cấu hình ứng dụng',
        translateKey: 'landing-page',
        icon: 'landing_source',
        authority: [Permission.configLandingPage],
        group: 'integrate',
      },

      {
        path: '/integrate-services/ffm-services',
        name: 'Tích hợp fulfillment',
        translateKey: 'integrate_ffm_services',
        authority: [Permission.integrateServices],
        group: 'integrate',
        icon: 'fulfillment',
        exact: true,
      },

      ////
      // {
      //   path: '/setting/shipping',
      //   name: 'Vận chuyển',
      //   translateKey: 'shipping',
      //   exact: true,
      //   authority: [Permission.configShipping],
      //   group: 'preferences',
      // },
      // {
      //   path: '/setting/partners',
      //   name: 'Nhà cung cấp',
      //   translateKey: 'partners',
      //   exact: true,
      //   authority: [Permission.configPartners],
      //   group: 'preferences',
      // },
      // {
      //   path: '/setting/billing',
      //   name: 'In bill',
      //   translateKey: 'billing',
      //   exact: true,
      //   authority: [Permission.configBilling],
      //   group: 'preferences',
      // },

      // {
      //   path: '/setting/facebook-pixel',
      //   name: 'Facebook Pixel',
      //   translateKey: 'facebook_pixel',
      //   exact: true,
      //   authority: [Permission.marketing],
      //   group: 'setting',
      // },
    ],
  },
];

export const navigationMenuFFM: MenuNavigation[] = [
  {
    path: '/ffm/dashboard',
    name: 'Dashboard',
    translateKey: 'dashboard',
    icon: 'dashboard',
    authority: [
      FFM.Permission.dashboardPTPerformance,
      FFM.Permission.dashboardOrderTracker,
      FFM.Permission.dashboardFluctuation,
    ],
    children: [
      {
        path: '/ffm/dashboard/order-tracker',
        name: 'Dashboard Order Tracker',
        translateKey: 'order_tracker',
        exact: false,
        authority: [FFM.Permission.dashboardOrderTracker],
      },
      {
        path: '/ffm/dashboard/inventory/fluctuation',
        name: 'Fluctuation Inventory',
        translateKey: 'fluctuation_inventory',
        exact: false,
        authority: [FFM.Permission.dashboardFluctuation],
      },
      {
        path: '/ffm/dashboard/inventory/logs',
        name: 'Log Inventory',
        translateKey: 'logs_inventory',
        exact: false,
        authority: [FFM.Permission.dashboardFluctuation],
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/ffm/orders',
    name: 'Đơn hàng',
    translateKey: 'order',
    icon: 'order',
    authority: [
      FFM.Permission.order,
      FFM.Permission.createOrder,
      FFM.Permission.ptManager,
      FFM.Permission.ptView,
    ],
    children: [
      {
        path: '/ffm/orders/manager',
        name: 'Quản lý đơn hàng',
        translateKey: 'order_manager',
        exact: false,
        authority: [FFM.Permission.order],
      },
      {
        path: '/ffm/orders/view',
        name: 'Đơn hàng',
        translateKey: 'order_manager',
        exact: false,
        hideInMenu: true,
        authority: [FFM.Permission.order],
      },
      {
        path: '/ffm/orders/ticket',
        name: 'Ticket',
        translateKey: 'order_ticket',
        exact: false,
        authority: [FFM.Permission.ptManager, FFM.Permission.ptView],
      },
      {
        path: '/ffm/orders/create',
        name: 'Tạo đơn hàng',
        translateKey: 'order_create',
        exact: true,
        authority: [FFM.Permission.createOrder],
      },
    ],
  },
  {
    path: '/ffm/bulk-action',
    name: 'Bulk',
    translateKey: 'order_bulk',
    icon: 'bulk',
    authority: [
      FFM.Permission.bulkExport,
      FFM.Permission.bulkStatus,
      FFM.Permission.bulk3PL,
      FFM.Permission.bulkRequest3pl,
      FFM.Permission.bulkPrint,
      FFM.Permission.multiWarehouse,
      FFM.Permission.bulkRaiseTicket,
      FFM.Permission.bulkChangeAssignee,
      FFM.Permission.bulkAddTags,
      FFM.Permission.bulkExportTicket,
      FFM.Permission.bulkCloseTicket,
      FFM.Permission.bulkResolveMultiReconciled,
      FFM.Permission.bulkUpdateOrderTotalWeight,
      FFM.Permission.bulkUpdateOrderMessage,
      FFM.Permission.bulkUpdateOrderInternalNote,
      FFM.Permission.bulkUpdateOrderType,
    ],
    children: [
      {
        path: '',
        name: 'Order',
        translateKey: '',
        exact: true,
        authority: [],
      },
      {
        path: '/ffm/bulk-action/export',
        name: 'Bulk Export',
        translateKey: 'bulk_export',
        exact: true,
        authority: [FFM.Permission.bulkExport],
        title: TitleChildrenMenu.order,
      },
      {
        path: '/ffm/bulk-action/status',
        name: 'Bulk Update Status',
        translateKey: 'bulk_update_status',
        exact: true,
        authority: [FFM.Permission.bulkStatus],
        title: TitleChildrenMenu.order,
      },
      // {
      //   path: '/ffm/bulk-action/3pl',
      //   name: 'Bulk Import 3PL',
      //   translateKey: 'bulk_import_3pl',
      //   exact: true,
      //   authority: [FFM.Permission.bulk3PL],
      //   title: TitleChildrenMenu.order,
      // },
      {
        path: '/ffm/bulk-action/upload-order-information',
        name: 'Bulk Upload Order Information',
        translateKey: 'bulk_upload_order_information',
        exact: true,
        authority: [
          FFM.Permission.bulkUpdateOrderTotalWeight,
          FFM.Permission.bulkUpdateOrderMessage,
          FFM.Permission.bulkUpdateOrderInternalNote,
          FFM.Permission.bulkUpdateOrderType,
          FFM.Permission.bulk3PL,
          FFM.Permission.bulkRequest3pl,
        ],
        title: TitleChildrenMenu.order,
      },
      {
        path: '/ffm/bulk-action/print',
        name: 'Bulk Print',
        translateKey: 'bulk_print',
        exact: true,
        authority: [FFM.Permission.bulkPrint],
        title: TitleChildrenMenu.order,
      },
      // {
      //   path: '/ffm/bulk-action/request3pl',
      //   name: 'Bulk Request Outbound Delivery',
      //   translateKey: 'bulk_request3pl',
      //   exact: true,
      //   authority: [Permission.bulkRequest3pl]
      // },
      {
        path: '/ffm/bulk-action/multi-warehouse',
        name: 'Bulk Multi Warehouse',
        translateKey: 'bulk_multi_warehouse',
        exact: true,
        authority: [FFM.Permission.multiWarehouse],
        title: TitleChildrenMenu.order,
      },
      {
        path: '/ffm/bulk-action/update-order-tag',
        name: 'Update Order Tag',
        translateKey: 'update_order_tag',
        exact: true,
        authority: [FFM.Permission.bulkTag, FFM.Permission.bulkWarehouseTag],
        title: TitleChildrenMenu.order,
      },
      {
        path: '',
        name: 'Ticket',
        translateKey: '',
        exact: true,
        authority: [],
      },
      {
        path: '/ffm/bulk-action/ticket',
        name: 'Ticket',
        translateKey: 'bulk_ticket',
        exact: true,
        authority: [FFM.Permission.bulkRaiseTicket],
      },
      {
        path: '/ffm/bulk-action/assign-ticket',
        name: 'Assign Ticket',
        translateKey: 'assign_ticket',
        exact: true,
        authority: [FFM.Permission.bulkChangeAssignee],
      },
      {
        path: '/ffm/bulk-action/close-ticket',
        name: 'Close Ticket',
        translateKey: 'close_ticket',
        exact: true,
        authority: [FFM.Permission.bulkCloseTicket],
      },
      {
        path: '/ffm/bulk-action/add-tags-ticket',
        name: 'Add Tags',
        translateKey: 'add_tags_ticket',
        exact: true,
        authority: [FFM.Permission.bulkAddTags],
      },
      {
        path: '',
        name: 'Reconciliation',
        translateKey: '',
        exact: true,
        authority: [],
      },
      {
        path: '/ffm/bulk-action/resolve-multi-reconciled',
        name: 'Resolve Multi Reconciled',
        translateKey: 'resolve_multi_reconciled',
        exact: true,
        authority: [FFM.Permission.bulkResolveMultiReconciled],
      },
    ],
  },
  {
    path: '/ffm/products',
    name: 'Sản phẩm',
    translateKey: 'product',
    icon: 'product',
    authority: [FFM.Permission.product, FFM.Permission.createProduct],
    children: [
      {
        path: '/ffm/products/manager',
        name: 'Quản lý sản phẩm',
        translateKey: 'product',
        exact: false,
        authority: [FFM.Permission.product],
      },
      {
        path: '/ffm/products/create',
        name: 'Tạo sản phẩm',
        translateKey: 'product_create',
        exact: true,
        authority: [FFM.Permission.createProduct],
      },
      // {
      //   path: '/ffm/products/create/combo',
      //   name: 'Tạo sản phẩm combo',
      //   translateKey: 'product_create_combo',
      //   exact: true,
      //   authority: [FFM.Permission.createProduct],
      // },
      // {
      //   path: '/ffm/products/export',
      //   name: 'Export sản phẩm',
      //   translateKey: 'product_export',
      //   exact: true,
      //   authority: [FFM.Permission.exportProduct],
      // },
    ],
  },
  {
    path: '/ffm/warehouses',
    name: 'Kho',
    translateKey: 'warehouse',
    icon: 'package',
    authority: [
      FFM.Permission.warehouse,
      FFM.Permission.createWarehouse,
      FFM.Permission.stockTaking,
      FFM.Permission.inventory,
      FFM.Permission.orderAllocationRule,
      FFM.Permission.stockTransferWithdrawal,
    ],
    children: [
      {
        path: '/ffm/warehouses/inventory',
        name: 'Tồn kho',
        translateKey: 'warehouse_inventory',
        exact: false,
        authority: [FFM.Permission.inventory],
      },
      {
        path: '/ffm/warehouses/stock-taking',
        name: 'Kiểm kê hàng hóa',
        translateKey: 'warehouse_stockTaking',
        exact: false,
        authority: [FFM.Permission.stockTaking],
      },
      {
        path: '/ffm/warehouses/stock-transfer',
        name: 'Stock Transfer, Withdrawal',
        translateKey: 'warehouse_stockTransfer',
        exact: false,
        authority: [FFM.Permission.stockTransferWithdrawal],
      },
      {
        path: '/ffm/warehouses/manager',
        name: 'Quản lý kho',
        translateKey: 'warehouse_list',
        exact: false,
        authority: [FFM.Permission.warehouse],
      },
      {
        path: '/ffm/warehouses/create',
        name: 'Tạo kho',
        translateKey: 'warehouse_create',
        exact: true,
        authority: [FFM.Permission.createWarehouse],
      },
      {
        path: '/ffm/warehouses/order-allocation',
        name: 'Phân loại kho - Đơn hàng',
        translateKey: 'order_allocation_rule',
        exact: true,
        authority: [FFM.Permission.orderAllocationRule],
      },
    ],
  },
  {
    path: '/ffm/import',
    name: 'Nhập kho',
    translateKey: 'import',
    icon: 'import',
    authority: [FFM.Permission.import, FFM.Permission.importOrderReturn, FFM.Permission.reImports],
    children: [
      {
        path: '/ffm/import/manager',
        name: 'Quản lý nhập kho',
        translateKey: 'import',
        exact: false,
        authority: [FFM.Permission.import],
      },
      {
        path: '/ffm/import/view',
        name: 'Phiếu nhập',
        translateKey: 'import',
        exact: false,
        authority: [FFM.Permission.import],
        hideInMenu: true,
      },
      {
        path: '/ffm/import/scan',
        name: 'Phiếu nhập',
        translateKey: 'import',
        exact: true,
        authority: [FFM.Permission.import],
        hideInMenu: true,
      },
      {
        path: '/ffm/import/create',
        name: 'Phiếu nhập',
        translateKey: 'import',
        exact: true,
        authority: [FFM.Permission.import],
        hideInMenu: true,
      },
      {
        path: '/ffm/import/return',
        name: 'Tái nhập kho',
        translateKey: 'import_return',
        exact: true,
        authority: [FFM.Permission.reImports],
      },
    ],
  },
  {
    path: '/ffm/export',
    name: 'Xuất kho',
    translateKey: 'export',
    icon: 'export',
    authority: [
      FFM.Permission.export,
      FFM.Permission.pickList,
      FFM.Permission.packingList,
      FFM.Permission.handOver,
    ],
    children: [
      {
        path: '/ffm/export/manager',
        name: 'Quản lý xuất kho',
        translateKey: 'export',
        exact: false,
        authority: [FFM.Permission.export],
      },
      {
        path: '/ffm/export/finish-packing',
        name: 'Quản lý xuất kho',
        translateKey: 'export',
        exact: true,
        hideInMenu: true,
        authority: [FFM.Permission.export],
      },
      /* {
        path: '/ffm/export/pickList',
        name: 'Bảng Kê Hàng Xuất Kho',
        translateKey: 'export_pickList',
        exact: true,
        authority: [FFM.Permission.pickList],
      }, */
      /* {
        path: '/ffm/export/packingList',
        name: 'Phiếu đóng gói',
        translateKey: 'export_packingList',
        exact: true,
        authority: [FFM.Permission.packingList],
      }, */
      {
        path: '/ffm/export/handOver',
        name: 'HandOver',
        translateKey: 'export_handOver',
        exact: false,
        authority: [FFM.Permission.handOver],
      },
    ],
  },
  {
    path: '/ffm/customers',
    name: 'Khách hàng',
    translateKey: 'client',
    exact: false,
    icon: 'customer',
    authority: [FFM.Permission.customer],
  },
  {
    path: '/ffm/3pl/manager',
    name: '3PL',
    translateKey: '3pl',
    exact: false,
    icon: '3pl',
    authority: [FFM.Permission.carrier3pl],
  },
  {
    path: '/ffm/pancake-sync-orders',
    name: 'Pancake sync orders',
    translateKey: 'pancake-sync-orders',
    exact: false,
    icon: 'pancake-sync-orders',
    authority: [FFM.Permission.pancakeSyncOrders],
  },
  // {
  //   path: '/ffm/tags',
  //   name: 'Tag management',
  //   translateKey: 'tag-management',
  //   exact: false,
  //   icon: 'tag-management',
  //   authority: [],
  // },
  {
    path: '/ffm/order-source',
    name: 'Order Source',
    translateKey: 'order-source',
    icon: 'order-source',
    authority: [FFM.Permission.orderSource],
    children: [
      {
        path: '/ffm/order-source',
        name: 'Order Source',
        translateKey: 'order-source',
        exact: false,
        authority: [FFM.Permission.orderSource],
      },
      {
        path: '/ffm/sync-order-source-failed',
        name: 'Order Source Failed',
        translateKey: 'order-source-failed',
        exact: false,
        authority: [FFM.Permission.orderSource],
      },
    ],
  },
  {
    path: '/ffm/3pl-remittance',
    name: 'Reconciliation',
    translateKey: 'reconciliation',
    icon: 'reconciliation',
    authority: [FFM.Permission.reconciliation, FFM.Permission.expeditedReconciliation],
    children: [
      {
        path: '',
        name: 'Carrier/3PL',
        translateKey: '',
        exact: true,
        authority: [],
      },
      {
        path: '/ffm/3pl-remittance/order-view',
        name: 'Order View',
        translateKey: 'order_view',
        exact: false,
        authority: [FFM.Permission.reconciliation],
      },
      {
        path: '/ffm/3pl-remittance/remittance-view',
        name: 'Remittance View',
        translateKey: 'remittance_view',
        exact: false,
        authority: [FFM.Permission.reconciliation],
      },
      {
        path: '/ffm/3pl-remittance/upload',
        name: 'Upload',
        translateKey: 'upload_file',
        exact: false,
        hideInMenu: true,
        authority: [FFM.Permission.reconciliation],
      },
      {
        path: '/ffm/3pl-remittance/tx-detail',
        name: 'Transaction Detail',
        translateKey: 'transaction_detail',
        exact: false,
        hideInMenu: true,
        authority: [FFM.Permission.reconciliation],
      },
      {
        path: '/ffm/3pl-remittance/expedited-reconciliation',
        name: 'Expedited Reconciliation',
        translateKey: 'expedited_reconciliation',
        exact: false,
        authority: [FFM.Permission.expeditedReconciliation],
      },
    ],
  },
  {
    path: '/ffm/configurations',
    name: 'Thiết lập',
    translateKey: 'configurations',
    icon: 'configurations',
    authority: [
      FFM.Permission.manageUsers,
      FFM.Permission.configShipping,
      FFM.Permission.configBilling,
      FFM.Permission.reason,
      FFM.Permission.tag,
      FFM.Permission.orderSource,
      FFM.Permission.duplicateOrders,
      FFM.Permission.packageCode,
    ],
    children: [
      {
        path: '/ffm/configurations/employees',
        name: 'Nhân sự',
        translateKey: 'employees',
        exact: false,
        authority: [FFM.Permission.manageUsers],
      },
      {
        path: '/ffm/configurations/shipping',
        name: 'Vận chuyển',
        translateKey: 'shipping',
        exact: true,
        authority: [FFM.Permission.configShipping],
      },
      {
        path: '/ffm/configurations/bill',
        name: 'In bill',
        translateKey: 'bill',
        exact: true,
        authority: [FFM.Permission.configBilling],
      },
      {
        path: '/ffm/configurations/country',
        name: 'Quốc gia',
        translateKey: 'country',
        exact: false,
        authority: [FFM.Permission.manageCountries],
      },
      {
        path: '/ffm/configurations/roles',
        name: 'Vai trò, nhóm quyền',
        translateKey: 'roles',
        exact: false,
        authority: [FFM.Permission.manageRoles],
      },
      {
        path: '/ffm/configurations/system-setting',
        name: 'Thiết lập hệ thống',
        translateKey: 'system_setting',
        exact: false,
        authority: [FFM.Permission.duplicateOrders, FFM.Permission.packageCode],
      },
      {
        path: '/ffm/configurations/reason',
        name: 'Reason',
        translateKey: 'order_reason',
        exact: false,
        authority: [FFM.Permission.reason],
      },
      {
        path: '/ffm/configurations/odz',
        name: 'Odz',
        translateKey: 'odz',
        exact: true,
        authority: [],
      },
      {
        path: '/ffm/tags',
        name: 'Tag management',
        translateKey: 'tag-management',
        exact: true,
        authority: [FFM.Permission.tag],
      },
    ],
  },
];

export const navigationMenuClientFFM: MenuNavigation[] = [
  {
    path: '/ffm/orders',
    name: 'Đơn hàng',
    translateKey: 'order',
    icon: 'order',
    authority: [FFM.Permission.order, FFM.Permission.createOrder],
    children: [
      {
        path: '/ffm/orders',
        name: 'Quản lý đơn hàng',
        translateKey: 'order_manager',
        exact: true,
        authority: [FFM.Permission.order],
      },
      {
        path: '/ffm/orders/view',
        name: 'Đơn hàng',
        translateKey: 'order_manager',
        exact: false,
        hideInMenu: true,
        authority: [FFM.Permission.order],
      },
      {
        path: '/ffm/orders/create',
        name: 'Tạo đơn hàng',
        translateKey: 'order_create',
        exact: true,
        authority: [FFM.Permission.createOrder],
      },
    ],
  },
  {
    path: '/ffm/bulk-action',
    name: 'Bulk',
    translateKey: 'order_bulk',
    icon: 'bulk',
    authority: [FFM.Permission.order, FFM.Permission.createOrder],
    children: [
      {
        path: '/ffm/bulk-action/export',
        name: 'Bulk Export',
        translateKey: 'bulk_export',
        exact: true,
        authority: [FFM.Permission.bulkExport],
      },
      {
        path: '/ffm/bulk-action/status',
        name: 'Bulk Update Status',
        translateKey: 'bulk_update_status',
        exact: true,
        authority: [FFM.Permission.bulkStatus],
      },
      // {
      //   path: '/ffm/bulk-action/3pl',
      //   name: 'Bulk Import 3PL',
      //   translateKey: 'bulk_import_3pl',
      //   exact: true,
      //   authority: [FFM.Permission.bulk3PL],
      // },
      {
        path: '/ffm/bulk-action/print',
        name: 'Bulk Print',
        translateKey: 'bulk_print',
        exact: true,
        authority: [FFM.Permission.bulkPrint],
      },
      // {
      //   path: '/ffm/bulk-action/request3pl',
      //   name: 'Bulk Request Outbound Delivery',
      //   translateKey: 'bulk_request3pl',
      //   exact: true,
      //   authority: [FFM.Permission.bulkRequest3pl],
      // },
    ],
  },
  {
    path: '/ffm/products',
    name: 'Sản phẩm',
    translateKey: 'product',
    icon: 'product',
    authority: [FFM.Permission.product, FFM.Permission.createProduct],
    children: [
      {
        path: '/ffm/products/manager',
        name: 'Quản lý sản phẩm',
        translateKey: 'product',
        exact: false,
        authority: [FFM.Permission.product],
      },
      {
        path: '/ffm/products/create',
        name: 'Tạo sản phẩm',
        translateKey: 'product_create',
        exact: true,
        authority: [FFM.Permission.createProduct],
      },
      // {
      //   path: '/ffm/products/create/combo',
      //   name: 'Tạo sản phẩm combo',
      //   translateKey: 'product_create_combo',
      //   exact: true,
      //   authority: [FFM.Permission.createProduct],
      // },
    ],
  },
  {
    path: '/ffm/warehouses',
    name: 'Kho',
    translateKey: 'warehouse',
    icon: 'package',
    authority: [
      FFM.Permission.warehouse,
      FFM.Permission.createWarehouse,
      FFM.Permission.stockTaking,
    ],
    children: [
      {
        path: '/ffm/warehouses/inventory',
        name: 'Tồn kho',
        translateKey: 'warehouse_inventory',
        exact: false,
        authority: [FFM.Permission.inventory],
      },
      {
        path: '/ffm/warehouses/stock-taking',
        name: 'Kiểm kê hàng hóa',
        translateKey: 'warehouse_stockTaking',
        exact: false,
        authority: [FFM.Permission.stockTaking],
      },
    ],
  },
  {
    path: '/ffm/import',
    name: 'Nhập kho',
    translateKey: 'import',
    icon: 'import',
    authority: [FFM.Permission.import, FFM.Permission.importOrderReturn],
    children: [
      {
        path: '/ffm/import',
        name: 'Quản lý nhập kho',
        translateKey: 'import',
        exact: false,
        authority: [FFM.Permission.import],
      },
    ],
  },
  {
    path: '/ffm/configurations',
    name: 'Thiết lập',
    translateKey: 'configurations',
    icon: 'configurations',
    authority: [],
    children: [
      {
        path: '/ffm/configurations/api-key',
        name: 'Api key',
        translateKey: 'api_key',
        exact: true,
      },
    ],
  },
];

export const navigationMenuDefault: MenuNavigation[] = [
  {
    path: '/ffm/configurations',
    name: 'Thiết lập',
    translateKey: 'configurations',
    icon: 'configurations',
    authority: [],
    children: [
      {
        path: '/ffm/configurations/api-key',
        name: 'Api key',
        translateKey: 'api_key',
        exact: true,
      },
    ],
  },
];
