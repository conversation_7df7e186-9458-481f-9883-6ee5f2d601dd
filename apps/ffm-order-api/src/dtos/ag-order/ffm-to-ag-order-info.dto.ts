import { TypeOrder } from '../../enums/order-status.enum';

export class ProductDto {
  productSKU?: string;

  productName?: string;

  quantity?: number;

  weight?: number;
}

export class FfmToAgOrderInfoDto {
  displayId?: string;

  creator?: string;

  recipientName?: string;

  recipientPhone?: string;

  recipientAddress?: string;

  recipientAddressNote?: string;

  recipientWardId?: string;

  recipientDistrictId?: string;

  recipientProvinceId?: string;

  recipientPostCode?: string;

  recipientCountryId?: string;

  discount?: number;

  surcharge?: number;

  shippingFee?: number;

  paid?: number;

  totalPrice?: number;

  serviceTLS?: number;

  serviceCS?: number;

  serviceFFM?: number;

  serviceInsurance?: number;

  subTotal?: number;

  waybillNote?: string;

  totalWeight?: string;

  products?: ProductDto[];

  type?: TypeOrder;
}
