/* eslint-disable @typescript-eslint/no-floating-promises */
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ORDER_STATUS_CAN_UPDATE_TICKET_STATUS } from 'apps/ffm-order-api/src/constants/raise-ticket.constants';
import { OrderTag } from 'apps/ffm-order-api/src/entities/order-tag.entity';
import { ReImport } from 'apps/ffm-order-api/src/entities/re-import.entity';
import { TypeOfTagEnum } from 'apps/ffm-order-api/src/enums/tag.enum';
import { plainToInstance } from 'class-transformer';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import {
  catalogFfmConnection,
  catalogFfmWriteConnection,
  orderConnection,
} from 'core/constants/database-connection.constant';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { SystemIdEnum } from 'core/enums/user-type.enum';
import { clone, concat, filter, find, isEmpty, last, orderBy, reduce, uniq } from 'lodash';
import * as moment from 'moment-timezone';
import { $enum } from 'ts-enum-util';
import { In, Repository, SelectQueryBuilder } from 'typeorm';
import {
  BEFORE_ORDER_STATUS_3PL_PICKED_UP,
  NOT_ALLOW_UPDATE_STATUS_BY_3PL_OUT_FOR_DELIVERY,
} from '../../../constants/order.constants';
import { Logs } from '../../../entities/logs.entity';
import { OrderProduct } from '../../../entities/order-product.entity';
import { Order } from '../../../entities/order.entity';
import { Tag } from '../../../entities/tags.entity';
import {
  TypeRevertStatus,
  TypeUpdateOrderStatus,
} from '../../../enums/type-update-order-status.enum';
import { ProductComboVariant } from '../../../read-entities/ffm-catalog/product-combo-variant.entity';
import { IDataVNP } from '../utils/tracking-parser';
import { TagService } from './tags.service';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';
import {
  InventoryType,
  ProductStatus,
  TypeInventory,
  TypePurpose,
} from 'apps/ffm-order-api/src/enums/stock-inventory.enum';
import { InventoryManagement } from 'apps/ffm-order-api/src/ffm-catalog-entities/inventory-management.entity';
import { InventoryLogs } from 'apps/ffm-order-api/src/ffm-catalog-entities/inventory-logs.entity';
import { InventoryLineItem } from 'apps/ffm-order-api/src/ffm-catalog-entities/inventory-line-item.entity';
import { ReImportTypeEnum } from 'apps/ffm-order-api/src/enums/re-import.enum';
import { ProductVariation } from 'apps/ffm-order-api/src/read-entities/ffm-catalog/product-variation.entity';

// const excel = require('node-excel-export');
// eslint-disable-next-line @typescript-eslint/no-var-requires
export interface IWebhookVNPDto {
  data: IDataVNP;
}

export interface ICalcInventory {
  status: 200 | 500;
  message: string;
  constraint?: string;
}
export interface IWarehouse {
  id: string;
  name: string;
  phoneNumber: string;
  address?: any;
  fullAddress: string;
  addressSplit?: string[];
  provinceId: string;
  districtId: string;
  communeId: string;
  status: string;
}

export interface IUpdateStatus3PL {
  id: number;
  isUpdateStatus3PL?: boolean;
  reason?: {
    reasonCode: string;
    reasonNote: string;
  };
  lastUpdateStatus: string;
  typeUpdateStatus: TypeUpdateOrderStatus;
  user: {
    id: number | string;
    companyId: number | string;
  };
  status: OrderFFMStatus;
}
export interface IOrderInventory {
  id: number;
  user: Record<string, any>;
  oldStatus: OrderFFMStatus;
  newStatus: OrderFFMStatus;
  lastUpdateStatus: Date;
  typeUpdateStatus?: any;
  reason?: string;
  revert?: boolean;
  callBackStatus?: boolean;
  isRequestQueue?: boolean;
  rollBackStatus?: boolean;
  revertType?: TypeRevertStatus;
  rmqErrorAttempts?: number;
  updatedAt?: number;
  isSyncToPartner?: boolean;
  saveNewStatus?: boolean;
  isReimport?: boolean;
  isPriorityStockInventory?: boolean;
}

export interface IOrderProduct {
  quantity: number;
  id: number;
  sku?: string;
  productId: number;
}
export interface IWebhookVNPDto {
  data: IDataVNP;
}

export interface ICalcInventory {
  status: 200 | 500;
  message: string;
  constraint?: string;
}
export interface IWarehouse {
  id: string;
  name: string;
  phoneNumber: string;
  address?: any;
  fullAddress: string;
  addressSplit?: string[];
  provinceId: string;
  districtId: string;
  communeId: string;
  status: string;
}

export interface IUpdateStatus3PL {
  id: number;
  isUpdateStatus3PL?: boolean;
  reason?: {
    reasonCode: string;
    reasonNote: string;
  };
  lastUpdateStatus: string;
  typeUpdateStatus: TypeUpdateOrderStatus;
  user: {
    id: number | string;
    companyId: number | string;
  };
  status: OrderFFMStatus;
}
export interface IOrderInventory {
  id: number;
  user: Record<string, any>;
  oldStatus: OrderFFMStatus;
  newStatus: OrderFFMStatus;
  lastUpdateStatus: Date;
  typeUpdateStatus?: any;
  reason?: string;
  revert?: boolean;
  callBackStatus?: boolean;
  isRequestQueue?: boolean;
  rollBackStatus?: boolean;
  revertType?: TypeRevertStatus;
  rmqErrorAttempts?: number;
  updatedAt?: number;
  isSyncToPartner?: boolean;
  saveNewStatus?: boolean;
  isReimport?: boolean;
  isPriorityStockInventory?: boolean;
}

export interface IReImportOrder {
  id?: number;
  countryId: number;
  clientId: number;
  orderId: number;
  warehouseId: number;
  orderDisplayId: string;
  products?: IReImportOrderItem[];
}
export interface IReImportOrderPayload {
  reImport: IReImportOrder;
  user: Record<string, any>;
  currentStatus: OrderFFMStatus;
  nextStatus: OrderFFMStatus;
  type: ReImportTypeEnum;
  productReimports?: IReImportOrderItem[];
  updatedAt: number;
}
export interface IReImportOrderItem {
  id?: number;
  orderId: number;
  itemId: number;
  productId: number;
  productName?: string;
  sku?: string;
  good: number;
  damaged: number;
  damagedBy3pl: number;
  lost: number;
  lostBy3pl: number;
  needed: number;
}
export interface IChangeWarehouse {
  order: {
    id: number;
    clientId?: number;
    countryId?: number;
    displayId: string;
    status: OrderFFMStatus;
    warehouseId: number;
  };
  variantIds: number[];
  variantSkus?: string[];
  user: Record<string, any>;
  newWarehouseId: number;
  newCountryWh?: number;
  products: IOrderProduct[];
  stillChangeWarehouse?: boolean;
  isDifferenceMarket?: boolean;
  updatedAt?: number;
  checkUniq?: boolean;
}
@Injectable()
export class StockInventoryInOrder {
  constructor(
    @InjectRepository(Order, orderConnection)
    private odRepository: Repository<Order>,
    @InjectRepository(OrderTag, orderConnection)
    private otRepository: Repository<OrderTag>,
    @InjectRepository(Logs, orderConnection)
    private logRepository: Repository<Logs>,
    @InjectRepository(ReImport, orderConnection)
    private reImportRepository: Repository<ReImport>,
    @InjectRepository(InventoryManagement, catalogFfmWriteConnection)
    private imRepository: Repository<InventoryManagement>,
    @InjectRepository(InventoryLineItem, catalogFfmWriteConnection)
    private iltRepository: Repository<InventoryLineItem>,
    @InjectRepository(InventoryLogs, catalogFfmWriteConnection)
    private logsRepository: Repository<InventoryLogs>,
    @InjectRepository(ProductComboVariant, catalogFfmConnection)
    private pcvRepository: Repository<ProductComboVariant>,
    @InjectRepository(ProductVariation, catalogFfmConnection)
    private varRepository: Repository<ProductVariation>,
    private readonly amqpConnection: AmqpConnection,
    private tagService: TagService,
    private readonly redisService: RedisCacheService,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async changeStockInventoryByOrder(payload: IOrderInventory) {
    console.log('payload', 'order-changed-stock-in-order-service', payload);

    const {
      id,
      user,
      oldStatus,
      newStatus,
      typeUpdateStatus,
      lastUpdateStatus,
      reason,
      revert,
      revertType,
      updatedAt,
      rollBackStatus,
      isSyncToPartner,
      saveNewStatus,
      isReimport,
      isPriorityStockInventory,
    } = payload;

    if (!oldStatus || !newStatus || !payload?.id) {
      console.log('Not found payload');
      return;
    }
    const { companyId } = user;
    const data = await this.odRepository
      .createQueryBuilder('o')
      .andWhere({
        companyId,
      })
      .andWhere({
        id,
      })
      .leftJoinAndSelect('o.products', 'products')
      .leftJoinAndSelect('o.carriers', 'carriers')
      .leftJoinAndSelect('carriers.carrier', 'carrier')
      .getOne();

    if (
      isPriorityStockInventory &&
      [OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm]?.includes(data?.status)
    ) {
      return {
        status: 200,
        message: 'success',
      };
    }
    if (revert) {
      let dataReImport: any = {};
      try {
        dataReImport = await this.reImportRepository
          .createQueryBuilder('r')
          .andWhere({
            companyId,
          })
          .andWhere({
            id,
          })
          .leftJoinAndSelect('r.products', 'products')
          .getOne();
        console.log('re-import-data', dataReImport);
      } catch (error) {
        console.log(payload?.id, 'amqpConnection failed', error);
        throw new Error('');
      }
      if (dataReImport) {
        console.log(payload?.id, 'Already exits reimport order');
        return {
          status: 500,
          message: 'Already exits reimport order',
        };
      }
    }

    if (!data) {
      console.log(payload?.id, 'Not found order');
      if (
        [OrderFFMStatus.Reconfirm, OrderFFMStatus.Confirmed]?.includes(newStatus) &&
        oldStatus != newStatus
      )
        await this.stockUpdateStatusOrderV2Function(<IUpdateStatus3PL>{
          id,
          status: oldStatus,
          user: {
            companyId: user?.companyId,
            id: SystemIdEnum.system,
          },
          lastUpdateStatus: lastUpdateStatus.toString(),
        });
      return {
        status: 500,
        message: 'Not found order',
      };
    }

    const ids = [],
      varIds = [],
      prodInOd = [];

    data?.products.forEach((item: OrderProduct) => {
      ids.push(item?.id);
      prodInOd[item?.productId] = item;
    });

    const products: IOrderProduct[] = [],
      comboLookups = [];

    // get product in order

    data?.products?.forEach((item: OrderProduct) => {
      // export combo => single product
      if (item?.productDetail.product?.isCombo) {
        item?.productDetail?.product?.combo?.forEach((el: ProductComboVariant) => {
          if (
            el?.status?.toString() ==
            $enum(ProductStatus)
              .getKeyOrDefault(ProductStatus.active, null)
              ?.toString()
          )
            products.push({
              id: item?.id,
              quantity: item?.quantity * el?.qty,
              productId: el?.variantId,
            });
          varIds.push(el?.variantId);
        });
      } else {
        products.push({
          id: item?.id,
          quantity: item?.quantity,
          productId: item?.productId,
        });
        varIds.push(item?.productId);
      }
    });

    let returnStockInReturnWh = false;
    //reimport về kho hoàn
    if (isReimport) returnStockInReturnWh = true;
    //chuyển trạng thái hoàn tồn về kho hoàn
    if (
      ([
        OrderFFMStatus.InReturn,
        OrderFFMStatus.InDelivery,
        OrderFFMStatus.Returned,
        OrderFFMStatus.InTransit,
        OrderFFMStatus.Stocked3PL,
      ]?.includes(oldStatus) &&
      [OrderFFMStatus.LostBy3PL]?.includes(newStatus))
    )
      returnStockInReturnWh = true;
    //revert trạng thái hoàn tồn về kho hoàn
    if (
      revert &&
      (([OrderFFMStatus.Returned, OrderFFMStatus.Delivered]?.includes(Number(newStatus)) &&
        [OrderFFMStatus.ReturnedStocked]?.includes(Number(oldStatus))) 
        ||
        ([OrderFFMStatus.InDelivery, OrderFFMStatus.InReturn]?.includes(newStatus) &&
          [OrderFFMStatus.LostBy3PL, OrderFFMStatus.LostByWH]?.includes(oldStatus)) 
        ||
        ([OrderFFMStatus.InDelivery, OrderFFMStatus.InReturn]?.includes(newStatus) &&
          [OrderFFMStatus.DamagedByWH, OrderFFMStatus.ReturnedDamagedBy3PL]?.includes(oldStatus)))
    )
      returnStockInReturnWh = true;

    if(returnStockInReturnWh){
      const stocks = [];
      for (const item of varIds) {
        const stock = new InventoryManagement();
        stock.companyId = companyId?.toString();
        stock.stockedPhySellable = 0;
        stock.lastEditorId = '-99';
        stock.warehouseId = data?.returnWarehouseId;
        stock.variantId = Number(item);
        stocks.push(stock);
      }

      if(!isEmpty(stocks)){
        await this.imRepository
        .createQueryBuilder()
        .insert()
        .into(InventoryManagement)
        .values(stocks)
        .orIgnore()
        .execute();
      }
    }
    const [inventories, productCombos] = await Promise.all([
      this.imRepository.find({
        where: qb => {
          qb.where({
            variantId: In(uniq(varIds)),
            warehouseId: returnStockInReturnWh ? data?.returnWarehouseId : data?.warehouseId,
            companyId,
          });
        },
      }),
      this.pcvRepository
        .createQueryBuilder('pcv')
        .select('variants.id', 'id')
        .addSelect('pcv.variantId', 'variant_id')
        .leftJoin('pcv.product', 'product')
        .leftJoin('product.variants', 'variants')
        .andWhere('pcv.variantId IN (:...variantIds)', { variantIds: uniq(varIds) })
        .andWhere('pcv.status = :status', { status: ProductStatus.active })
        .getRawMany(),
    ]);

    productCombos?.forEach((item: any) => {
      if (!comboLookups[Number(item?.variant_id)]) comboLookups[item?.variant_id] = [];
      comboLookups[Number(item?.variant_id)].push(item?.id);
    });
    if (uniq(varIds)?.length != inventories?.length) {
      if (
        [OrderFFMStatus.Reconfirm, OrderFFMStatus.Confirmed]?.includes(newStatus) &&
        oldStatus != newStatus
      ) {
        await this.stockUpdateStatusOrderV2Function(<IUpdateStatus3PL>{
          id,
          status: oldStatus,
          user: {
            companyId: user?.companyId,
            id: SystemIdEnum.system,
          },
          lastUpdateStatus: lastUpdateStatus.toString(),
        });
      }
      // console.log(payload?.id, 'Not found inventory', inventories, varIds);
      return {
        status: 500,
        message: 'Not found inventory',
      };
    }

    const params = [],
      totalStock = [];
    let reConfirmOrderAwaitingStock = false;

    for (const it of products) {
      const prod: IOrderProduct = it;
      const stock = find(inventories, function(o: InventoryManagement) {
        return o?.variantId == prod?.productId;
      });
      if (stock?.id) {
        const [currentSellable, currentDamaged] = await this.parseCurrentInventory(stock);

        const stockInventoryItem = new InventoryLineItem();
        stockInventoryItem.originName = `Sales Order ${data?.displayId}`;
        stockInventoryItem.inventoryId = stock?.id;
        stockInventoryItem.originId = data?.id;
        stockInventoryItem.originStatus = newStatus;
        stockInventoryItem.originItemId = prod?.id;
        stockInventoryItem.type = InventoryType.order;
        stockInventoryItem.currentInventory = Number(stock.stockedPhySellable) ?? 0;
        stockInventoryItem.currentSellable = Number(currentSellable) ?? 0;
        stockInventoryItem.currentDamaged = Number(currentDamaged) ?? 0;
        stockInventoryItem.variantId = prod?.productId;
        stockInventoryItem.warehouseId = data?.warehouseId;
        stockInventoryItem.companyId = companyId?.toString();
        stockInventoryItem.originUpdatedAt = updatedAt;
        stockInventoryItem.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
        const logs = [];

        //revert đơn
        if (revert) {
          if (revertType == TypeRevertStatus.api) {
            if ([OrderFFMStatus.New, OrderFFMStatus.AwaitingStock]?.includes(newStatus)) {
              stockInventoryItem.originName = `Revert Order ${data?.displayId}`;
              stockInventoryItem.stockedPhySellable = prod?.quantity;
              console.log(
                'INVENTORY',
                `Revert to status ${$enum(OrderFFMStatus).getKeyOrDefault(
                  oldStatus,
                  null,
                )}: SO code [${data?.displayId}]`,
              );
            } else if (
              [OrderFFMStatus.Returned, OrderFFMStatus.Delivered]?.includes(Number(newStatus)) &&
              [OrderFFMStatus.ReturnedStocked]?.includes(Number(oldStatus))
            ) {
              stockInventoryItem.stockedPhySellable = -prod?.quantity;
              stockInventoryItem.importSellableReimport = -prod?.quantity;
              stockInventoryItem.warehouseId = data?.returnWarehouseId;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: -prod?.quantity,
                inventory: TypeInventory.importSellable,
                purpose: TypePurpose.reimport,
                reference: `Revert to status Returned Stocked: SO code [${data?.displayId}]`,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            }
          } else if (revertType == TypeRevertStatus.bulkRevertStatus) {
            if (
              newStatus == OrderFFMStatus.Awaiting3PLPickup &&
              oldStatus == OrderFFMStatus.PickedUp3PL
            ) {
              // stockInventoryItem.stockedPhySellable = -prod?.quantity;
              stockInventoryItem.exportSellableSales = -prod?.quantity;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.exportSellableSales,
                inventory: TypeInventory.exportSellable,
                purpose: TypePurpose.sale,
                reference: `Revert to status Awaiting Carrier Pickup: SO code [${data?.displayId}]`,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            } else if (
              [OrderFFMStatus.InDelivery, OrderFFMStatus.InReturn]?.includes(newStatus) &&
              [OrderFFMStatus.LostBy3PL, OrderFFMStatus.LostByWH]?.includes(oldStatus)
            ) {
              stockInventoryItem.exportLost = prod?.quantity;
              stockInventoryItem.warehouseId = data?.returnWarehouseId;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.exportLost,
                inventory: TypeInventory.exportLost,
                purpose: TypePurpose.others,
                reference: `Revert to status ${
                  newStatus == OrderFFMStatus.InDelivery ? `InDelivery` : `InReturn`
                }: SO code [${data?.displayId}]`,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            } else if (
              [OrderFFMStatus.InDelivery, OrderFFMStatus.InReturn]?.includes(newStatus) &&
              [OrderFFMStatus.DamagedByWH, OrderFFMStatus.ReturnedDamagedBy3PL]?.includes(oldStatus)
            ) {
              stockInventoryItem.exportDamagedOthers = prod?.quantity;
              stockInventoryItem.warehouseId = data?.returnWarehouseId;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.exportDamagedOthers,
                inventory: TypeInventory.exportDamaged,
                purpose: TypePurpose.others,
                reference: `Revert to status ${
                  newStatus == OrderFFMStatus.InDelivery ? `InDelivery` : `InReturn`
                }: SO code [${data?.displayId}]`,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            }
          }
        } else {
          if (
            [OrderFFMStatus.Reconfirm, OrderFFMStatus.Confirmed]?.includes(newStatus) &&
            oldStatus != newStatus
          ) {
            stockInventoryItem.stockedPhySellable = -prod?.quantity;
            console.log(
              'INVENTORY',
              `${$enum(OrderFFMStatus).getKeyOrDefault(newStatus, null)}: SO code [${
                data?.displayId
              }]`,
            );
          } else if (newStatus == OrderFFMStatus.PickedUp3PL) {
            stockInventoryItem.exportSellableSales = prod?.quantity;
            // stockInventoryItem.originUpdatedAt = 0;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.exportSellableSales,
              inventory: TypeInventory.exportSellable,
              purpose: TypePurpose.sale,
              reference: `Update to status 3PL Pickedup: SO code [${data?.displayId}]`,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          } else if (
            newStatus == OrderFFMStatus.Canceled &&
            [
              OrderFFMStatus.Reconfirm,
              OrderFFMStatus.Confirmed,
              OrderFFMStatus.AwaitingCollection,
            ]?.includes(oldStatus)
          ) {
            stockInventoryItem.stockedPhySellable = prod?.quantity;
            if (!totalStock[stockInventoryItem.variantId])
              totalStock[stockInventoryItem.variantId] = 0;
            totalStock[stockInventoryItem.variantId] += Number(prod?.quantity);
            reConfirmOrderAwaitingStock = true;
            const inventories = await this.imRepository.findOne({
              where: qb => {
                qb.where({
                  variantId: prod?.productId,
                  warehouseId: data?.warehouseId,
                  companyId,
                });
              },
            });
            if (!isEmpty(inventories) && inventories.stockedPhySellable > 0)
              totalStock[stockInventoryItem.variantId] += Number(inventories?.stockedPhySellable);
          } else if (
            [
              OrderFFMStatus.InReturn,
              OrderFFMStatus.InDelivery,
              OrderFFMStatus.Returned,
              OrderFFMStatus.InTransit,
              OrderFFMStatus.Stocked3PL,
            ]?.includes(oldStatus) &&
            [OrderFFMStatus.LostBy3PL]?.includes(newStatus)
          ) {
            stockInventoryItem.importLost = prod?.quantity;
            stockInventoryItem.warehouseId = data?.returnWarehouseId;
            // stockInventoryItem.originUpdatedAt = 0;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importLost,
              inventory: TypeInventory.importLost,
              purpose: TypePurpose.sale,
              reference: `Update to status Lost By 3PL: SO code [${data?.displayId}]`,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          } else if (
            [OrderFFMStatus.Collecting, OrderFFMStatus.Awaiting3PLPickup]?.includes(oldStatus) &&
            [OrderFFMStatus.LostByWH]?.includes(newStatus)
          ) {
            stockInventoryItem.importLost = prod?.quantity;
            stockInventoryItem.exportSellableSales = prod?.quantity;
            // stockInventoryItem.originUpdatedAt = 0;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.exportSellableSales,
              inventory: TypeInventory.importLost,
              purpose: TypePurpose.sale,
              reference: `Update to status Lost By WH: SO code [${data?.displayId}]`,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          } else if (
            [OrderFFMStatus.Collecting, OrderFFMStatus.Awaiting3PLPickup]?.includes(oldStatus) &&
            [OrderFFMStatus.DamagedByWH]?.includes(newStatus)
          ) {
            stockInventoryItem.importDamagedOthers = prod?.quantity;
            stockInventoryItem.exportSellableSales = prod?.quantity;
            // stockInventoryItem.originUpdatedAt = 0;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.exportSellableSales,
              inventory: TypeInventory.importDamaged,
              purpose: TypePurpose.sale,
              reference: `Update to status Damaged By WH: SO code [${data?.displayId}]`,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
        }

        if (logs?.length > 0) stockInventoryItem.logs = logs;
        // console.log('sale-order-stock-item', stockInventoryItem);
        params.push(stockInventoryItem);
      }
    }

    console.log('sale-order-stock', params);
    if (params?.length > 0) {
      if (
        [OrderFFMStatus.Reconfirm, OrderFFMStatus.Confirmed]?.includes(newStatus) &&
        oldStatus != newStatus
      ) {
        const res: any = await this.iltRepository.save(params).catch(err => {
          if (err?.driverError)
            console.log('sale-order-stock update-stock-inventory-error', err?.driverError);
          return {
            status: 500,
            message: err?.driverError?.constraint,
          };
        });

        if (isPriorityStockInventory) {
          if (!res?.message || res?.message == 'UQ_STOCK_TYPE_ORIGIN') {
            await Promise.all([
              this.odRepository
                .update(
                  { id: data?.id, status: In([OrderFFMStatus.New, OrderFFMStatus.AwaitingStock]) },
                  {
                    status: OrderFFMStatus.Reconfirm,
                    lastUpdatedBy: SystemIdEnum.system,
                  },
                )
                .catch(err => {
                  console.log('reconfirm error', err?.driverError);
                }),
              this.otRepository.delete({ id_order: data?.id, id_tag: -1 }),
            ]);
          }
          if (res?.message == 'CHK_cb6880fb40a80b2e6e52b8f914') {
            const checkInventories = await this.imRepository.find({
              where: qb => {
                qb.where({
                  variantId: In(uniq(varIds)),
                  warehouseId: data?.warehouseId,
                  stockedPhySellable: 0,
                  companyId,
                });
              },
            });
            console.log(
              '🚀🚀🚀 ~ FFMQueueExternalService ~ changeStockInventoryByOrder ~ checkInventories:',
              checkInventories,
            );
            if (!isEmpty(checkInventories))
              return {
                status: 500,
                message: 'CHK_cb6880fb40a80b2e6e52b8f914',
                variantIds: checkInventories?.map(x => x.variantId),
              };
          }
        }

        if (res?.status == 500) {
          if (rollBackStatus) {
            await this.stockUpdateStatusOrderV2Function(<IUpdateStatus3PL>{
              id,
              status: oldStatus,
              user: {
                companyId: user?.companyId,
                id: SystemIdEnum.system,
              },
              lastUpdateStatus: lastUpdateStatus.toString(),
            });
          }
          return res;
        } else if (saveNewStatus) {
          await this.stockUpdateStatusOrderV2Function(<IUpdateStatus3PL>{
            id,
            status: newStatus,
            user: {
              companyId: user?.companyId,
              id: SystemIdEnum.system,
            },
            lastUpdateStatus: lastUpdateStatus.toString(),
          });
        }
      } else {
        const res: any = await this.iltRepository
          .createQueryBuilder()
          .insert()
          .values(params)
          .execute()
          .catch(err => {
            if (err?.driverError)
              console.log('sale-order-stock update-stock-inventory-error', err?.driverError);
            return {
              status: 500,
              message: err?.driverError?.detail ?? err?.driverError?.error,
              constraint: err?.driverError?.constraint,
            };
          });

        if (res.identifiers?.length > 0) {
          const logData: InventoryLogs[] = [];
          res.identifiers.map((it, idx) => {
            params[idx]?.logs?.map((log: InventoryLogs) => {
              logData.push({
                ...log,
                inventoryLineId: it?.id,
              });
            });
          });

          // console.log(222, logData);
          await this.logsRepository
            .createQueryBuilder()
            .insert()
            .values(logData)
            .execute()
            .catch(err => {
              if (err?.driverError)
                console.log('sale-order-stock update-stock-inventory-error', err?.driverError);
              throw new Error(err?.driverError);
            });
        } else if (res?.status == 500) return res;
      }
    }

    if (reConfirmOrderAwaitingStock) {
      for (const prod of products) {
        if (!!totalStock?.[prod?.productId] && totalStock?.[prod?.productId] > 0) {
          await this.amqpConnection.publish('ffm-order-external', 'stock-inventory-changed-2-1', {
            id: prod?.productId,
            user: {
              companyId,
            },
            warehouseId: data?.warehouseId,
            quantity: totalStock?.[prod?.productId] ?? 0,
            comboIds: uniq(comboLookups[Number(prod?.productId)]),
            updatedAt,
          });
        }
      }
    }

    if (isSyncToPartner) {
      if (!!data?.externalId)
        await this.amqpConnection.publish('ffm-sync-order', 'sync-order-2-1', {
          id: data?.id,
          user,
          updatedAt: lastUpdateStatus,
        });
    }
    return {
      status: 200,
      message: 'success',
    };
  }

  async parseCurrentInventory(stockInventory: InventoryManagement): Promise<any[]> {
    const currentSellable =
      Number(stockInventory.importSellableOthers) +
      Number(stockInventory.importSellableReimport) +
      Number(stockInventory.importSellableStocktaking) +
      Number(stockInventory.importSellableSupplier) +
      Number(stockInventory.importSellableTransfer) -
      Number(stockInventory.exportSellableOthers) -
      Number(stockInventory.exportSellableSales) -
      Number(stockInventory.exportSellableStocktaking) -
      Number(stockInventory.exportSellableSupplier) -
      Number(stockInventory.exportSellableTransfer);

    const currentDamaged =
      Number(stockInventory.importDamagedOthers) +
      Number(stockInventory.importDamagedReimport) +
      Number(stockInventory.importDamagedStocktaking) +
      Number(stockInventory.importDamagedSupplier) +
      Number(stockInventory.importDamagedTransfer) -
      Number(stockInventory.exportDamagedClearance) -
      Number(stockInventory.exportDamagedOthers) -
      Number(stockInventory.exportDamagedStocktaking) -
      Number(stockInventory.exportDamagedSupplier) -
      Number(stockInventory.exportDamagedTransfer);
    return [currentSellable, currentDamaged];
  }

  async stockUpdateStatusOrderV2Function(payload: IUpdateStatus3PL) {
    console.log('update status when calc inventory', payload);

    if (!payload?.id || !payload?.status || !payload?.user) {
      console.log('stock-update-order', 'Not found id | status | user');
      return 'Not found id | status | user';
    }

    const { id, status, user, typeUpdateStatus, lastUpdateStatus, reason } = payload;

    const order = await this.odRepository.findOne({
      where: qb => {
        qb.andWhere({
          id,
          companyId: user?.companyId,
        });
      },
      relations: ['products', 'carriers', 'carriers.carrier', 'tags'],
    });

    console.log('stock-update-order', order, lastUpdateStatus);

    if (!order?.id) {
      console.log('stock-update-order', 'Not found order');
      return 'Not found order';
    }
    // Nếu không gửi từ 3pl thì check lastUpdateStatus
    // Còn không sẽ check trạng thái cuối khi 3pl gửi về
    let isSyncTag = false;
    const newStatus =
      !isEmpty(order?.lastCarrier) &&
      order?.lastCarrier?.status == 'activated' &&
      order?.lastCarrier?.waybillNumber &&
      status == OrderFFMStatus.Confirmed
        ? OrderFFMStatus.AwaitingCollection
        : status;
    if (
      (order?.lastUpdateStatus &&
        moment(lastUpdateStatus)?.valueOf() < moment(order?.lastUpdateStatus)?.valueOf() &&
        typeUpdateStatus != TypeUpdateOrderStatus.By3PL) ||
      ((NOT_ALLOW_UPDATE_STATUS_BY_3PL_OUT_FOR_DELIVERY?.includes(order?.status) ||
        BEFORE_ORDER_STATUS_3PL_PICKED_UP?.includes(order?.status)) &&
        typeUpdateStatus == TypeUpdateOrderStatus.By3PL)
    ) {
      console.log('stock-update-order', 'lastUpdateStatus late', payload, order);

      const log = plainToInstance(Logs, {
        action: 'STATUS',
        event: 'Change',
        type: `Status (by ${order?.lastCarrier?.carrier?.name ?? ''})`,
        afterChanges: [
          'reason_code',
          reason?.reasonCode ?? '',
          'reason_note',
          reason?.reasonNote ?? '',
        ],
        changes: [`${newStatus}`],
        beforeChanges: [`${order.status}`],
        tableName: 'orders',
        recordId: order?.id,
        creatorId: SystemIdEnum?.systemCraw3pl,
      });
      this.logRepository.insert(log).catch(err => {
        if (err?.driverError) console.log(err?.driverError?.detail);
        return err;
      });
      return log;
    } else {
      const updateData: any = !!typeUpdateStatus
        ? {
            status: newStatus,
            lastUpdatedBy: user?.id,
            typeUpdateStatus,
            lastUpdateStatus,
            reasonCode: !!reason?.reasonCode ? reason?.reasonCode : order?.reasonCode,
            reasonNote: !!reason?.reasonNote ? reason?.reasonNote : order?.reasonNote,
            lastUpdateReason: !!reason?.reasonCode ? new Date() : order?.lastUpdateReason,
          }
        : {
            status: newStatus,
            lastUpdatedBy: user?.id,
          };

      let tags = [];
      if (order?.lastCarrier?.extraData?.courier_name) {
        tags = order?.tags;
        const tagInFFM = await this.tagService.getTagByContent(
          [order?.lastCarrier?.extraData?.courier_name],
          Number(user?.companyId),
          order?.countryId,
        );
        if (tagInFFM?.[0]) {
          const tagLookup = find(order?.tags, (it: Tag) => it.id == tagInFFM?.[0]?.id);
          if (!tagLookup) tags.push(tagInFFM?.[0]);
        } else {
          const newTag = new Tag();
          newTag.content = order?.lastCarrier?.extraData?.courier_name;
          newTag.companyId = Number(user?.companyId);
          newTag.modules = [TypeOfTagEnum.Order];
          newTag.countryId = order.countryId;
          newTag.color = '#D4E0FF';
          tags.push(newTag);
        }
        isSyncTag = true;
      }

      const responseUpdate = await this.odRepository
        .update({ id: order?.id }, updateData)
        .catch(err => {
          if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
          return err;
        });

      if (responseUpdate?.affected == 0 || !responseUpdate?.affected) {
        throw new BadRequestException('save order affected = 0');
      }

      if (tags?.length > 0)
        await this.odRepository
          .save({
            ...order,
            ...updateData,
            tags,
          })
          .catch(err => {
            if (err?.driverError) console.log(err?.driverError?.detail);
            return err;
          });
    }

    if (
      order?.id &&
      [OrderFFMStatus.ReturnedStocked, OrderFFMStatus.ReturnedCompleted].includes(newStatus)
    ) {
      this.amqpConnection.publish('ffm-order', 'ffm-queue-auto-generate-remittance', {
        orderId: order?.id,
      });
    }

    console.log('update status when calc inventory', 'success', payload);

    if (!!order?.externalId)
      this.amqpConnection.publish('ffm-sync-order', 'sync-order-2-1', {
        id: order?.id,
        user,
        updatedAt: lastUpdateStatus,
        isSyncTag,
      });
    if (ORDER_STATUS_CAN_UPDATE_TICKET_STATUS?.includes(newStatus))
      this.updateTicketStatus(order?.id, user, newStatus);
    // if (status == OrderFFMStatus.ReturnedStocked) this.updateOrderAwaitingStock(order, user);
    return `Update success ${order?.id}`;
  }

  async updateTicketStatus(id: number, user: any, status: OrderFFMStatus) {
    if (!!id) {
      await this.amqpConnection.publish('ffm-order', 'order-update-ticket-status', {
        id,
        user,
        status,
      });
    }
  }

  async reimportChangeStockInventory(payload: IReImportOrderPayload) {
    console.log('payload', 'reimport-change-stock', payload);
    if (!payload?.reImport?.orderId) {
      return {
        status: 500,
        message: 'Error',
      };
    }
    const { reImport, user, nextStatus, currentStatus, productReimports, updatedAt } = payload;
    const { warehouses, companyId } = user;
    if (!warehouses || !companyId) {
      return {
        status: 500,
        message: 'Error',
      };
    }

    if (isEmpty(reImport?.products)) {
      console.log(payload?.reImport?.orderId, 'not found order');
      return {
        status: 500,
        message: 'Error',
      };
    }

    const varIds = [],
      totalStock = [],
      prodCombo = [],
      variantPhyIds = [];

    reImport?.products.forEach((item: any) => {
      varIds.push(item?.productId);
    });

    const variants: any[] = [];

    // get product in order
    if (varIds?.length > 0) {
      reImport?.products?.forEach((item: IReImportOrderItem) => {
        variantPhyIds.push(item?.productId);
        variants.push({
          quantity: item?.needed,
          good: item?.good,
          damaged: item?.damaged + item?.damagedBy3pl,
          lost: item?.lost + item?.lostBy3pl,
          needed: item?.needed,
          productId: item?.productId,
          id: item?.productId,
          itemId: item?.itemId,
        });
      });
    }

    if (variants?.length <= 0) {
      console.log(payload?.reImport?.orderId, 'not found product');
      return {
        status: 500,
        message: 'Error',
      };
    }

    // const inventories = await this.imRepository.find({
    //   where: qb => {
    //     qb.where({
    //       variantId: In(uniq(variantPhyIds)),
    //       warehouseId: reImport?.warehouseId,
    //       companyId,
    //     });
    //   },
    // });

    const [inventories, productCombos] = await Promise.all([
      this.imRepository.find({
        where: qb => {
          qb.where({
            variantId: In(uniq(variantPhyIds)),
            warehouseId: reImport?.warehouseId,
            companyId,
          });
        },
      }),
      this.pcvRepository
        .createQueryBuilder('pcv')
        .select('variants.id', 'id')
        .addSelect('pcv.variantId', 'variant_id')
        .leftJoin('pcv.product', 'product')
        .leftJoin('product.variants', 'variants')
        .andWhere('pcv.variantId IN (:...variantIds)', { variantIds: uniq(varIds) })
        .andWhere('pcv.status = :status', { status: ProductStatus.active })
        .getRawMany(),
    ]);
    productCombos?.forEach((item: any) => {
      prodCombo.push(item?.id);
    });

    if (uniq(variantPhyIds)?.length != inventories?.length) {
      console.log(payload?.reImport?.orderId, 'Not found inventory');
      return {
        status: 500,
        message: 'Error',
      };
    }

    const params = [];
    for (const it of variants) {
      const prod: IReImportOrderItem = it;
      const stock = find(inventories, function(o: InventoryManagement) {
        return o?.variantId == prod?.productId;
      });

      if (stock?.id) {
        const [currentSellable, currentDamaged] = await this.parseCurrentInventory(stock);

        const stockInventoryItem = new InventoryLineItem();
        stockInventoryItem.originName = `Re-import: SO code [${reImport?.orderDisplayId}]`;
        stockInventoryItem.inventoryId = stock?.id;
        stockInventoryItem.originId = reImport?.orderId;
        stockInventoryItem.originStatus = nextStatus;
        stockInventoryItem.originItemId = prod?.itemId;
        stockInventoryItem.type = InventoryType.reImport;

        stockInventoryItem.currentInventory = Number(stock.stockedPhySellable) ?? 0;
        stockInventoryItem.currentSellable = Number(currentSellable) ?? 0;
        stockInventoryItem.currentDamaged = Number(currentDamaged) ?? 0;

        stockInventoryItem.variantId = prod?.productId;
        stockInventoryItem.warehouseId = reImport?.warehouseId;
        stockInventoryItem.companyId = companyId?.toString();
        stockInventoryItem.originUpdatedAt = updatedAt;
        stockInventoryItem.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
        const logs = [];

        if (currentStatus == OrderFFMStatus.Canceled || currentStatus == OrderFFMStatus.Returned) {
          let exportSellable = 0;
          if (prod.damaged) {
            stockInventoryItem.importDamagedReimport = prod?.damaged;
            exportSellable += Number(prod?.damaged);
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importDamagedReimport,
              inventory: TypeInventory.importDamaged,
              purpose: TypePurpose.reimport,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          if (prod.lost) {
            stockInventoryItem.importLost = prod?.lost;
            exportSellable += Number(prod?.lost);
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importLost,
              inventory: TypeInventory.importLost,
              purpose: TypePurpose.others,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          if (currentStatus == OrderFFMStatus.Returned) {
            stockInventoryItem.importSellableReimport = prod?.good;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importSellableReimport,
              inventory: TypeInventory.importSellable,
              purpose: TypePurpose.reimport,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }

          if (currentStatus == OrderFFMStatus.Canceled && exportSellable > 0) {
            stockInventoryItem.exportSellableOthers = exportSellable;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.exportSellableOthers,
              inventory: TypeInventory.exportSellable,
              purpose: TypePurpose.others,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          stockInventoryItem.stockedPhySellable = prod?.good;
        } else if (currentStatus == OrderFFMStatus.InReturn) {
          if (prod?.good) {
            stockInventoryItem.importSellableReimport = prod?.good;
            stockInventoryItem.stockedPhySellable = prod?.good;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importSellableReimport,
              inventory: TypeInventory.importSellable,
              purpose: TypePurpose.reimport,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          if (prod.good < prod.needed) {
            if (prod.damaged) {
              stockInventoryItem.importDamagedReimport = prod?.damaged;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.importDamagedReimport,
                inventory: TypeInventory.importDamaged,
                purpose: TypePurpose.reimport,
                reference: stockInventoryItem.originName,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            }
            if (prod.lost) {
              stockInventoryItem.importLost = prod?.lost;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.importLost,
                inventory: TypeInventory.importLost,
                purpose: TypePurpose.others,
                reference: stockInventoryItem.originName,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            }
          }
        }

        if (logs?.length > 0) stockInventoryItem.logs = logs;
        // console.log('reimport-order', stockInventoryItem);
        params.push(stockInventoryItem);
        if (prod?.productId)
          totalStock[prod?.productId] =
            Number(stockInventoryItem.currentInventory) +
            Number(stockInventoryItem.stockedPhySellable);
      }
    }

    if (params?.length > 0) {
      const res: any = await this.iltRepository
        .createQueryBuilder()
        .insert()
        .values(params)
        .execute()
        .catch(err => {
          if (err?.driverError)
            console.log('sale-order-stock update-stock-inventory-error', err?.driverError);
          return {
            status: 500,
            message: err?.driverError?.detail ?? err?.driverError?.error,
          };
        });

      if (res.identifiers?.length > 0) {
        const logData = [];
        res.identifiers.map((it, idx) => {
          params[idx]?.logs?.map((log: InventoryLogs) => {
            logData.push({
              ...log,
              inventoryLineId: it?.id,
            });
          });
        });

        // console.log(222, logData);
        await this.logsRepository
          .createQueryBuilder()
          .insert()
          .values(logData)
          .execute()
          .catch(err => {
            if (err?.driverError)
              console.log('sale-order-stock update-stock-inventory-error', err?.driverError);
            throw new Error(err?.driverError);
          });
        const updatedAt = moment()?.valueOf();
        for (const prod of variants) {
          if (!!totalStock?.[prod?.productId] && totalStock?.[prod?.productId] > 0)
            await this.amqpConnection.publish('ffm-order-external', 'stock-inventory-changed-2-1', {
              id: prod?.productId,
              user: {
                companyId,
              },
              warehouseId: reImport?.warehouseId,
              quantity: totalStock?.[prod?.productId] ?? 0,
              comboIds: uniq(prodCombo),
              updatedAt,
            });
        }
      } else if (res?.status == 500) return res;
    }
    return {
      status: 200,
      message: 'success',
    };
  }

  async orderChangedWarehouseV2(payload: IChangeWarehouse) {
    const {
      order,
      variantIds,
      variantSkus,
      newWarehouseId,
      newCountryWh,
      products,
      user,
      stillChangeWarehouse,
      updatedAt,
      checkUniq,
      isDifferenceMarket,
    } = payload;
    const { companyId, id } = user;
    let result: Record<string, any> = { code: 200 };
    const status = order?.status;

    let variants: ProductVariation[] = [];
    let newVariantIds = [];
    let varriantLookup: Record<string, any>;
    if(!isEmpty(variantSkus)){
      variants = await this.varRepository.createQueryBuilder('var')
      // .select('id')
      .where('var.client_id = :clientId', {clientId: order?.clientId})
      .andWhere('var.sku IN (:...variantSkus)', {variantSkus})
      .andWhere('var.status = :status', {status: ProductStatus.active})
      .andWhere('var.biz_id = :companyId', {companyId})
      .andWhere('var.country_id = :countryId', {countryId: newCountryWh})
      .getMany();

      if(isEmpty(variants) || variants.length != variantSkus.length){
          return {
            status: 500,
            code: 'SO_0086',
            message: 'Not found SKU in return Warehouse',
          };
      }
      newVariantIds = variants?.map(x => x.id);
      varriantLookup = variants?.reduce((acc, item) => {
        acc[item?.sku] = item;
        return acc;
      }, {});
    }

    if([OrderFFMStatus.Draft, OrderFFMStatus.New].includes(status)) return {
      status: 500,
      message: 'Not affect inventory',
    };
    const [stockSenders, stockRecipients] = await Promise.all([
      [OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status)
        ? this.imRepository.find({
            where: qb => {
              qb.where({
                variantId: In(variantIds),
                warehouseId: order.warehouseId,
                companyId,
              });
            },
          })
        : [],
      this.imRepository.find({
        where: qb => {
          qb.where({
            variantId: In(!isEmpty(newVariantIds) ? newVariantIds : variantIds),
            warehouseId: newWarehouseId,
            companyId,
          });
        },
      }),
    ]);

    const lookupSender = [],
      lookupRecipient = [];

    stockSenders?.forEach((e: InventoryManagement) => {
      lookupSender[e.variantId] = e;
    });

    stockRecipients?.forEach((e: InventoryManagement) => {
      lookupRecipient[e.variantId] = e;
    });

    if (
      [OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status) &&
      (stockRecipients?.length == 0 || stockRecipients?.length != uniq(variantIds)?.length)
    ) {
      if (!stillChangeWarehouse) return { code: 'SO_0013', message: 'Sản phẩm không đủ tồn kho' };
    }

    const params: InventoryLineItem[] = [];

    for (const prod of products) {
      const stockInventory = !isDifferenceMarket ? lookupRecipient?.[prod?.productId] : (!isEmpty(varriantLookup) ? lookupRecipient?.[varriantLookup[prod?.sku].id] : undefined);

      if (!stockInventory?.id) {
        if (!stillChangeWarehouse) {
          return {
            status: 500,
            message: 'Not found inventory',
          };
        } else {
          if ([OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status)) {
            const stockInventoryOld = lookupSender?.[prod?.productId];

            if (!stockInventoryOld?.id) {
              return {
                status: 500,
                message: 'Not found inventory',
              };
            }

            // console.log('order-stock stockInventoryOld', stockInventoryOld);
            const [currentSellableOld, currentDamagedOld] = await this.parseCurrentInventory(
              stockInventoryOld,
            );

            const stockInventoryItemOld = new InventoryLineItem();
            stockInventoryItemOld.originName = `Sales Order ${order?.displayId}`;

            stockInventoryItemOld.stockedPhySellable = Number(prod.quantity);

            stockInventoryItemOld.inventory = stockInventoryOld;

            stockInventoryItemOld.originId = order?.id;
            stockInventoryItemOld.originItemId = prod?.id;
            stockInventoryItemOld.type = InventoryType.order;

            stockInventoryItemOld.currentInventory =
              Number(stockInventoryOld.stockedPhySellable) ?? 0;
            stockInventoryItemOld.currentSellable = Number(currentSellableOld) ?? 0;
            stockInventoryItemOld.currentDamaged = Number(currentDamagedOld) ?? 0;

            stockInventoryItemOld.variantId = prod?.productId;
            stockInventoryItemOld.warehouseId = order.warehouseId;
            stockInventoryItemOld.companyId = companyId?.toString();
            stockInventoryItemOld.originUpdatedAt = checkUniq
              ? moment(updatedAt).valueOf() + 10
              : moment(updatedAt).valueOf();
            stockInventoryItemOld.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
            // console.log('change-warehouse-order-stock', stockInventoryItemOld);
            params.push(stockInventoryItemOld);
          }
        }
      } else {
        if (!stillChangeWarehouse) {
          // console.log('order-stock stockInventory 2', stockInventory);
          const [currentSellable, currentDamaged] = await this.parseCurrentInventory(
            stockInventory,
          );

          const stockInventoryItem = new InventoryLineItem();
          stockInventoryItem.originName = `Sales Order ${order?.displayId}`;

          stockInventoryItem.stockedPhySellable = -Number(prod.quantity);

          stockInventoryItem.inventory = stockInventory;

          stockInventoryItem.originId = order?.id;
          stockInventoryItem.originItemId = prod?.id;
          stockInventoryItem.type = InventoryType.order;

          stockInventoryItem.currentInventory = Number(stockInventory.stockedPhySellable) ?? 0;
          stockInventoryItem.currentSellable = Number(currentSellable) ?? 0;
          stockInventoryItem.currentDamaged = Number(currentDamaged) ?? 0;

          stockInventoryItem.variantId = stockInventory.variantId;
          stockInventoryItem.warehouseId = newWarehouseId;
          stockInventoryItem.companyId = companyId?.toString();
          stockInventoryItem.originUpdatedAt = moment(updatedAt).valueOf();
          stockInventoryItem.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
          // console.log('purchase-order-stock 2', stockInventoryItem);
          params.push(stockInventoryItem);

          if ([OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status)) {
            const stockInventoryOld = lookupSender?.[prod?.productId];

            if (!stockInventoryOld?.id) {
              return {
                status: 500,
                message: 'Not found inventory',
              };
            }

            // console.log('order-stock stockInventoryOld 2', stockInventoryOld);
            const [currentSellableOld, currentDamagedOld] = await this.parseCurrentInventory(
              stockInventoryOld,
            );

            const stockInventoryItemOld = new InventoryLineItem();
            stockInventoryItemOld.originName = `Sales Order ${order?.displayId}`;

            stockInventoryItemOld.stockedPhySellable = Number(prod.quantity);

            stockInventoryItemOld.inventory = stockInventoryOld;

            stockInventoryItemOld.originId = order?.id;
            stockInventoryItemOld.originItemId = prod?.id;
            stockInventoryItemOld.type = InventoryType.order;

            stockInventoryItemOld.currentInventory =
              Number(stockInventoryOld.stockedPhySellable) ?? 0;
            stockInventoryItemOld.currentSellable = Number(currentSellableOld) ?? 0;
            stockInventoryItemOld.currentDamaged = Number(currentDamagedOld) ?? 0;

            stockInventoryItemOld.variantId = prod?.productId;
            stockInventoryItemOld.warehouseId = order.warehouseId;
            stockInventoryItemOld.companyId = companyId?.toString();
            stockInventoryItemOld.originUpdatedAt = checkUniq
              ? moment(updatedAt).valueOf() + 10
              : moment(updatedAt).valueOf();
            stockInventoryItemOld.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
            // console.log('change-warehouse-order-stock 2', stockInventoryItemOld);
            params.push(stockInventoryItemOld);
          }
        } else {
          if ([OrderFFMStatus.Confirmed, OrderFFMStatus.Reconfirm].includes(status)) {
            const stockInventoryOld = lookupSender?.[prod?.productId];

            if (!stockInventoryOld?.id) {
              return {
                status: 500,
                message: 'Not found inventory',
              };
            }

            // console.log('order-stock stockInventoryOld 3', stockInventoryOld);
            const [currentSellableOld, currentDamagedOld] = await this.parseCurrentInventory(
              stockInventoryOld,
            );

            const stockInventoryItemOld = new InventoryLineItem();
            stockInventoryItemOld.originName = `Sales Order ${order?.displayId}`;

            stockInventoryItemOld.stockedPhySellable = Number(prod.quantity);

            stockInventoryItemOld.inventory = stockInventoryOld;

            stockInventoryItemOld.originId = order?.id;
            stockInventoryItemOld.originItemId = prod?.id;
            stockInventoryItemOld.type = InventoryType.order;

            stockInventoryItemOld.currentInventory =
              Number(stockInventoryOld.stockedPhySellable) ?? 0;
            stockInventoryItemOld.currentSellable = Number(currentSellableOld) ?? 0;
            stockInventoryItemOld.currentDamaged = Number(currentDamagedOld) ?? 0;

            stockInventoryItemOld.variantId = prod?.productId;
            stockInventoryItemOld.warehouseId = order.warehouseId;
            stockInventoryItemOld.companyId = companyId?.toString();
            stockInventoryItemOld.originUpdatedAt = checkUniq
              ? moment(updatedAt).valueOf() + 10
              : moment(updatedAt).valueOf();
            stockInventoryItemOld.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
            // console.log('change-warehouse-order-stock 3', stockInventoryItemOld);
            params.push(stockInventoryItemOld);
          }
        }
      }
    }

    if (params?.length > 0) {
      const res: any = await this.iltRepository.save(params).catch(err => {
        if (err?.driverError) console.log('change-warehouse-order-stock-error', err?.driverError);
        return {
          status: 500,
          message: err?.driverError?.detail ?? err?.driverError?.error,
        };
      });

      if (res?.status == 500) result = res;
    }
    // console.log('order-change-warehouse-v2', result);
    return result;
  }

  async reimportChangeStockInventoryMultiMarket(payload: IReImportOrderPayload) {
    console.log('payload', 'reimport-change-stock', payload);
    if (!payload?.reImport?.orderId) {
      return {
        status: 500,
        message: 'Error',
      };
    }
    const { reImport, user, nextStatus, currentStatus, productReimports, updatedAt } = payload;
    const { warehouses, companyId } = user;
    if (!warehouses || !companyId) {
      return {
        status: 500,
        message: 'Error',
      };
    }

    if (isEmpty(reImport?.products)) {
      console.log(payload?.reImport?.orderId, 'not found order');
      return {
        status: 500,
        message: 'Error',
      };
    }

    const varSkus = [],
      varIds = [],
      totalStock = [],
      prodCombo = [],
      variantPhyIds = [];

    reImport?.products.forEach((item: any) => {
      // varIds.push(item?.productId);
      varSkus.push(item?.sku);
    });
    if(varSkus.length == 0) return {
      status: 500,
      message: 'Error',
    };
    const variantss = await this.varRepository.createQueryBuilder('var')
    // .select('id')
    .where('var.client_id = :clientId', {clientId: reImport?.clientId})
    .andWhere('var.sku IN (:...varSkus)', {varSkus})
    .andWhere('var.biz_id = :companyId', {companyId})
    .andWhere('var.country_id = :countryId', {countryId: reImport?.countryId})
    .getMany();

    if(isEmpty(variantss) || varSkus.length != variantss.length){
      console.log(payload?.reImport?.orderId, 'not found SKU in return Warehouse');
      return {
        status: 500,
        message: 'not found SKU in return Warehouse',
      };
    }
    const varriantLookup = variantss?.reduce((acc, item) => {
      acc[item?.sku] = item;
      return acc;
    }, {});
    const variants: any[] = [];

    // get product in order
    if (varSkus?.length > 0) {
      reImport?.products?.forEach((item: IReImportOrderItem) => {
        if(varriantLookup[item?.sku]){
          variantPhyIds.push(varriantLookup[item?.sku].id);
          variants.push({
            quantity: item?.needed,
            good: item?.good,
            damaged: item?.damaged + item?.damagedBy3pl,
            lost: item?.lost + item?.lostBy3pl,
            needed: item?.needed,
            productId: item?.productId,
            id: varriantLookup[item?.sku].id,
            itemId: item?.itemId,
          });
        }
      });
    }

    if (variants?.length != variantss.length) {
      console.log(payload?.reImport?.orderId, 'not found product');
      return {
        status: 500,
        message: 'Error',
      };
    }

    const stocks = [];
    for (const item of variantss) {
      const stock = new InventoryManagement();
      stock.companyId = companyId?.toString();
      stock.stockedPhySellable = 0;
      stock.lastEditorId = '-99';
      stock.warehouseId = reImport?.warehouseId;
      stock.variantId = Number(item.id);
      stocks.push(stock);
    }

    if(!isEmpty(stocks)){
      await this.imRepository
      .createQueryBuilder()
      .insert()
      .into(InventoryManagement)
      .values(stocks)
      .orIgnore()
      .execute();
    }

    const [inventories, productCombos] = await Promise.all([
      this.imRepository.find({
        where: qb => {
          qb.where({
            variantId: In(uniq(variantPhyIds)),
            warehouseId: reImport?.warehouseId,
            companyId,
          });
        },
      }),
      this.pcvRepository
        .createQueryBuilder('pcv')
        .select('variants.id', 'id')
        .addSelect('pcv.variantId', 'variant_id')
        .leftJoin('pcv.product', 'product')
        .leftJoin('product.variants', 'variants')
        .leftJoin('pcv.variant', 'pvcvariant')
        .andWhere('pvcvariant.sku IN (:...varSkus)', { varSkus: uniq(varSkus) })
        .andWhere('pcv.status = :status', { status: ProductStatus.active })
        .getRawMany(),
    ]);
    productCombos?.forEach((item: any) => {
      prodCombo.push(item?.id);
    });

    if (uniq(variantPhyIds)?.length != inventories?.length) {
      console.log(payload?.reImport?.orderId, 'Not found inventory');
      return {
        status: 500,
        message: 'Error',
      };
    }

    const params = [];
    for (const it of variants) {
      const prod: IReImportOrderItem = it;
      const stock = find(inventories, function(o: InventoryManagement) {
        return o?.variantId == prod?.id;
      });

      if (stock?.id) {
        const [currentSellable, currentDamaged] = await this.parseCurrentInventory(stock);

        const stockInventoryItem = new InventoryLineItem();
        stockInventoryItem.originName = `Re-import: SO code [${reImport?.orderDisplayId}]`;
        stockInventoryItem.inventoryId = stock?.id;
        stockInventoryItem.originId = reImport?.orderId;
        stockInventoryItem.originStatus = nextStatus;
        stockInventoryItem.originItemId = prod?.itemId;
        stockInventoryItem.type = InventoryType.reImport;

        stockInventoryItem.currentInventory = Number(stock.stockedPhySellable) ?? 0;
        stockInventoryItem.currentSellable = Number(currentSellable) ?? 0;
        stockInventoryItem.currentDamaged = Number(currentDamaged) ?? 0;

        stockInventoryItem.variantId = prod?.id;
        stockInventoryItem.warehouseId = reImport?.warehouseId;
        stockInventoryItem.companyId = companyId?.toString();
        stockInventoryItem.originUpdatedAt = updatedAt;
        stockInventoryItem.createdBy = `${user?.id} | ${user?.displayId} | ${user?.fullname}`;
        const logs = [];

        if (currentStatus == OrderFFMStatus.Canceled || currentStatus == OrderFFMStatus.Returned) {
          let exportSellable = 0;
          if (prod.damaged) {
            stockInventoryItem.importDamagedReimport = prod?.damaged;
            exportSellable += Number(prod?.damaged);
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importDamagedReimport,
              inventory: TypeInventory.importDamaged,
              purpose: TypePurpose.reimport,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          if (prod.lost) {
            stockInventoryItem.importLost = prod?.lost;
            exportSellable += Number(prod?.lost);
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importLost,
              inventory: TypeInventory.importLost,
              purpose: TypePurpose.others,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          if (currentStatus == OrderFFMStatus.Returned) {
            stockInventoryItem.importSellableReimport = prod?.good;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importSellableReimport,
              inventory: TypeInventory.importSellable,
              purpose: TypePurpose.reimport,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }

          if (currentStatus == OrderFFMStatus.Canceled && exportSellable > 0) {
            stockInventoryItem.exportSellableOthers = exportSellable;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.exportSellableOthers,
              inventory: TypeInventory.exportSellable,
              purpose: TypePurpose.others,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          stockInventoryItem.stockedPhySellable = prod?.good;
        } else if (currentStatus == OrderFFMStatus.InReturn) {
          if (prod?.good) {
            stockInventoryItem.importSellableReimport = prod?.good;
            stockInventoryItem.stockedPhySellable = prod?.good;
            logs.push({
              variantId: stockInventoryItem.variantId,
              warehouseId: stockInventoryItem.warehouseId,
              companyId: stockInventoryItem.companyId,
              quantity: stockInventoryItem.importSellableReimport,
              inventory: TypeInventory.importSellable,
              purpose: TypePurpose.reimport,
              reference: stockInventoryItem.originName,
              actor: stockInventoryItem.createdBy,
            } as InventoryLogs);
          }
          if (prod.good < prod.needed) {
            if (prod.damaged) {
              stockInventoryItem.importDamagedReimport = prod?.damaged;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.importDamagedReimport,
                inventory: TypeInventory.importDamaged,
                purpose: TypePurpose.reimport,
                reference: stockInventoryItem.originName,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            }
            if (prod.lost) {
              stockInventoryItem.importLost = prod?.lost;
              logs.push({
                variantId: stockInventoryItem.variantId,
                warehouseId: stockInventoryItem.warehouseId,
                companyId: stockInventoryItem.companyId,
                quantity: stockInventoryItem.importLost,
                inventory: TypeInventory.importLost,
                purpose: TypePurpose.others,
                reference: stockInventoryItem.originName,
                actor: stockInventoryItem.createdBy,
              } as InventoryLogs);
            }
          }
        }

        if (logs?.length > 0) stockInventoryItem.logs = logs;
        // console.log('reimport-order', stockInventoryItem);
        params.push(stockInventoryItem);
        if (prod?.productId)
          totalStock[prod?.productId] =
            Number(stockInventoryItem.currentInventory) +
            Number(stockInventoryItem.stockedPhySellable);
      }
    }
    // throw new BadRequestException()

    if (params?.length > 0) {
      const res: any = await this.iltRepository
        .createQueryBuilder()
        .insert()
        .values(params)
        .execute()
        .catch(err => {
          if (err?.driverError)
            console.log('sale-order-stock update-stock-inventory-error', err?.driverError);
          return {
            status: 500,
            message: err?.driverError?.detail ?? err?.driverError?.error,
          };
        });

      if (res.identifiers?.length > 0) {
        const logData = [];
        res.identifiers.map((it, idx) => {
          params[idx]?.logs?.map((log: InventoryLogs) => {
            logData.push({
              ...log,
              inventoryLineId: it?.id,
            });
          });
        });

        // console.log(222, logData);
        await this.logsRepository
          .createQueryBuilder()
          .insert()
          .values(logData)
          .execute()
          .catch(err => {
            if (err?.driverError)
              console.log('sale-order-stock update-stock-inventory-error', err?.driverError);
            throw new Error(err?.driverError);
          });
        const updatedAt = moment()?.valueOf();
        for (const prod of variants) {
          if (!!totalStock?.[prod?.productId] && totalStock?.[prod?.productId] > 0)
            await this.amqpConnection.publish('ffm-order-external', 'stock-inventory-changed-2-1', {
              id: prod?.productId,
              user: {
                companyId,
              },
              warehouseId: reImport?.warehouseId,
              quantity: totalStock?.[prod?.productId] ?? 0,
              comboIds: uniq(prodCombo),
              updatedAt,
            });
        }
      } else if (res?.status == 500) return res;
    }
    return {
      status: 200,
      message: 'success',
    };
  }

}
