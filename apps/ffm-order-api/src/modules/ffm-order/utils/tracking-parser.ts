import { isEmpty, isNil, orderBy } from 'lodash';
import * as moment from 'moment-timezone';
import { INimBusTracking, NimbusStatus } from '../clients/nimbus.client';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { IBestOrder } from '../clients/best-express-malaysia.client';
import { IBestThOrder } from '../clients/best-express-thailand.client';

export const ORDER_RETURN_STATUSES = [
  OrderFFMStatus.Returned,
  OrderFFMStatus.InReturn,
  OrderFFMStatus.ReturnedStocked, // Đ<PERSON> hoàn (L<PERSON>u kho)
  OrderFFMStatus.ReturnedCompleted, // Đ<PERSON> hoàn (Hoàn tất)
  OrderFFMStatus.DeliveredCompleted, //  Đã giao (Hoàn tất)
  OrderFFMStatus.ReturnedDamagedBy3PL, //  Đã hoàn (Hư hỏng bởi 3PL)
  OrderFFMStatus.LostByWH,
  OrderFFMStatus.DamagedByWH,
];

export const ORDER_SHIPPING_STATUSES = [
  OrderFFMStatus.AwaitingCollection,
  OrderFFMStatus.Collecting,
  OrderFFMStatus.Awaiting3PLPickup,
  OrderFFMStatus.PickedUp3PL,
  OrderFFMStatus.InTransit,
  OrderFFMStatus.Stocked3PL,
  OrderFFMStatus.InDelivery,
  OrderFFMStatus.FailedDelivery,
  OrderFFMStatus.AwaitingReturn,
  OrderFFMStatus.InReturn,
  OrderFFMStatus.LostBy3PL,
];

const NJV_COUNTRY_CODES = {
  66: 'th',
  63: 'ph',
  60: 'my',
  95: 'mm',
};

export interface IReason {
  reasonCode: string;
  reasonNote: string;
}
export interface IDataVNP {
  status: string;
  displayId: string;
  waybillNumber: string;
  updatedAt: Date;
  note: string;
}

export interface IPosLajuTracking {
  connote_id: string;
  sender_data: {
    sender_postcode: string;
    sender_state: string;
    sender_city: string;
    sender_country: string;
  };
  recipient_data: {
    receipient_postcode: string;
    receipient_state: string;
    receipient_city: string;
    receipient_country: string;
  };
  process_status: string;
  eta_data: {
    summary: string;
    day: string;
    date: string;
    upper_eta: string;
    lower_eta: string;
  };
  tracking_data: {
    type: string;
    date: string;
    process: string;
    process_summary: string;
    office: string;
    error_details: any;
    event_type: string;
    epod: string;
  }[];
}

export interface IFlashCrawlOrder {
  pno_display?: string;
  search_no: string;
  state: number;
  state_text: string;
  src_province_name: string;
  dst_province_name: string;
  store_phone: string;
  routes: {
    message: string;
    route_action: string;
    routed_at: string;
    route_icon: number;
    ticket_route_extra_value_vo_list: string[];
    state: number;
    state_text: string;
    dst_detail_address: string;
  }[];
  sign_info: {
    signer_show: string;
    image_url: string[];
  };
}

export interface IJneTracking {
  status: string;
  updatedAt: Date;
}

export interface IAnousithTracking {
  trackingId: string;
  createdDate: string;
  originReceiveDate: string;
  originSendDate: string;
  destReceiveDate: string;
  destSendDate: string;
  sendCompleteDate: string;
  receiveBackwardDate: string;
  isBackward: string;
  backwardDetail: string;
}

export interface IHalTracking {
  shipment_info: {
    bill_number: string;
    receiver_phone_number: string;
  };
  tracking_events: {
    date: string;
    place: string;
    message: string;
    delivery_state: number;
    delivery_state_name: string;
  }[];
}

export interface IShopeeTracking {
  sls_tracking_number: string;
  tracking_list: {
    status: string;
    timestamp: number;
    message: string;
  }[];
}

export interface IKerryTrackingNumberData {
  shipmentInfo: {
    trackingNo: string;
    refNo: string;
    statusDatetime: string;
    statusDesc: string;
  };
}

export interface IKerryTrackingData {
  tracking_no: string;
  ref: {
    icon: {
      current_idx: number;
      display: {
        code: number;
        desc: string;
      }[];
    };
    shipment: {
      dest_postcode: string;
      dest_province: string;
      edt: string;
      org_province: string;
      r_mobile: string;
      r_name: string;
      ref_no: string;
      s_mobile: string;
      sender: string;
      sig: string;
    };
    shipment_status: {
      indx: number;
      loc: string;
      s_action: string;
      s_code: string;
      s_datetime: string;
      s_date: string;
      s_desc: string;
      s_time: string;
      s_group: string;
    }[];
    sta: {
      code: string;
      desc: string;
    };
  };
}

export interface IBluedartTracking {
  Scans: {
    ScanDetail: {
      ScanCode: number;
      Scan: string;
      ScanDate: string;
      ScanTime: string;
      ScanGroupType: string;
    }[];
  };
}

export interface ITrackingUpdate {
  updatedAt: Date;
  status: OrderFFMStatus;
  note: string;
  raw: Record<string, any>;
}

export interface IUpdateOrderTracking {
  waybill: string;
  updates: ITrackingUpdate[];
  lastDataUpdate?: ITrackingUpdate;
  orderId: number;
  reason?: Record<string, any>[];
  isRequest?: boolean;
}

const JNT_STATUS = {
  110: OrderFFMStatus.Stocked3PL,
  // 106: OrderFFMStatus.PickedUp3PL,
  109: OrderFFMStatus.InTransit,
  112: OrderFFMStatus.InDelivery,
  118: OrderFFMStatus.FailedDelivery,
  116: OrderFFMStatus.InReturn,
  113: OrderFFMStatus.Delivered,
  117: OrderFFMStatus.Returned,
};

const HAL_STATUS = {
  // 2: OrderFFMStatus.PickedUp3PL,
  3: OrderFFMStatus.InTransit,
  4: OrderFFMStatus.InTransit,
  5: OrderFFMStatus.Stocked3PL,
  6: OrderFFMStatus.Delivered,
  11: OrderFFMStatus.InReturn,
};

const SHOPEE_STATUS = {
  // FMHub_Pickup_Done: OrderFFMStatus.PickedUp3PL,
  FMHub_LHTransporting: OrderFFMStatus.InTransit,
  SOC_Received: OrderFFMStatus.Stocked3PL,
  SOC_LHTransporting: OrderFFMStatus.InTransit,
  LMHub_Received: OrderFFMStatus.Stocked3PL,
  Delivering: OrderFFMStatus.InDelivery,
  'On Hold': OrderFFMStatus.FailedDelivery,
  Delivered: OrderFFMStatus.Delivered,
  Return_LMHub_Received: OrderFFMStatus.InReturn,
  Return_SOC_Received: OrderFFMStatus.InReturn,
  Return_SOC_LHTransporting: OrderFFMStatus.InReturn,
  Return_LMHub_Returning: OrderFFMStatus.InReturn,
  Return_LMHub_Returned: OrderFFMStatus.Returned,
  SOC_Handover: OrderFFMStatus.InTransit,
  '3PL_Received': OrderFFMStatus.InTransit,
  '3PL_SOC_Outbound': OrderFFMStatus.Stocked3PL,
  '3PL_SOC_Inbound': OrderFFMStatus.Stocked3PL,
  '3PL_Delivering': OrderFFMStatus.InDelivery,
  '3PL_Delivered': OrderFFMStatus.Delivered,
};

const KERRY_STATUS = {
  'Arrived at Distribution center': OrderFFMStatus.InTransit,
  'Arrived at Hub / Transit station': OrderFFMStatus.InTransit,
  // 'Picked up by courier': OrderFFMStatus.PickedUp3PL,
  'Departed from Origin station': OrderFFMStatus.InTransit,
  'Departed from Distribution center': OrderFFMStatus.InTransit,
  'Out for delivery': OrderFFMStatus.InDelivery,
  'Our courier is delivering your parcel': OrderFFMStatus.InDelivery,
  'Courier has contacted the recipient': OrderFFMStatus.InDelivery,
  'Your parcel is in transit': OrderFFMStatus.InTransit,
  'Arrived at Origin station': OrderFFMStatus.InTransit,
  'The parcel is being returned to the sender.': OrderFFMStatus.InReturn,
  'Your parcel has been returned': OrderFFMStatus.InReturn,
  'The parcel has been returned to the sender': OrderFFMStatus.InReturn,
  'Successfully delivered': OrderFFMStatus.Delivered,
  'Arrived at Destination station': OrderFFMStatus.Stocked3PL,
  'Arrived at collection station': OrderFFMStatus.Stocked3PL,
  // 117: OrderFFMStatus.Returned,
  'Pending for re-delivery': OrderFFMStatus.FailedDelivery,
  'Unable to deliver due to shipment problem': OrderFFMStatus.FailedDelivery,
  'Delivery Unsuccessful': OrderFFMStatus.FailedDelivery,
  'Deliver not success address changed': OrderFFMStatus.FailedDelivery,
};

const JNT_SCAN_STATUS = {
  'Picked Up': OrderFFMStatus.PickedUp3PL,
  'Departure': OrderFFMStatus.InTransit,
  'Arrival': OrderFFMStatus.Stocked3PL,
  'On Delivery': OrderFFMStatus.InDelivery,
  'Signature': OrderFFMStatus.Delivered,
  'Return Confirmation': OrderFFMStatus.InReturn,
  'Return Signature': OrderFFMStatus.Returned,
  'Problematic': OrderFFMStatus.FailedDelivery,
  'Overnight': OrderFFMStatus.Stocked3PL,
  // 'waybill closed': OrderFFMStatus.Cancel waybill,
};
const JNT_SCAN_TYPE = {
  Arrival: OrderFFMStatus.Stocked3PL,
  // 'Picked Up': OrderFFMStatus.PickedUp3PL,
  Departure: OrderFFMStatus.InTransit,
  Departed: OrderFFMStatus.InTransit,
  'On Delivery': OrderFFMStatus.InDelivery,
  'Refuse delivery': OrderFFMStatus.FailedDelivery,
  'On Return': OrderFFMStatus.InReturn,
  'On return': OrderFFMStatus.InReturn,
  Delivered: OrderFFMStatus.Delivered,
  Returned: OrderFFMStatus.Returned,
  'Return Delivered': OrderFFMStatus.Returned,
  'Return Register': OrderFFMStatus.InReturn,
  'On Hold': OrderFFMStatus.FailedDelivery,
};

const JNT_THAI_STATUS = {
  // '1': OrderFFMStatus.PickedUp3PL,
  '2': OrderFFMStatus.InTransit,
  '3': OrderFFMStatus.Stocked3PL,
  '4': OrderFFMStatus.InDelivery,
  '5': OrderFFMStatus.Delivered,
  '6': OrderFFMStatus.FailedDelivery,
  '7': OrderFFMStatus.FailedDelivery,
  '8': OrderFFMStatus.InReturn,
  '9': OrderFFMStatus.Returned,
};

const POS_LAJU_STATUS = {
  depart_from_facility: OrderFFMStatus.InTransit,
  delivery_assigned: OrderFFMStatus.InTransit,
  shipment_inbounded_at_facility: OrderFFMStatus.InTransit,
  EM012: OrderFFMStatus.InTransit,
  EM062: OrderFFMStatus.InTransit,
  hip_inbound: OrderFFMStatus.InTransit,
  rip: OrderFFMStatus.InTransit,
  bag_opened: OrderFFMStatus.InTransit,
  out_for_delivery: OrderFFMStatus.InDelivery,
  delivery_fail_1st_attempt: OrderFFMStatus.FailedDelivery,
  delivery_fail: OrderFFMStatus.FailedDelivery,
  parcel_return_initiated: OrderFFMStatus.AwaitingReturn,
  parcel_return_assigned: OrderFFMStatus.InReturn,
  out_for_return: OrderFFMStatus.InReturn,
  assigned_for_window_delivery: OrderFFMStatus.Stocked3PL,
  rto_success: OrderFFMStatus.Returned,
  window_delivery_success: OrderFFMStatus.Delivered,
  delivery_success: OrderFFMStatus.Delivered,
};

const NJV_STATUS = {
  // FROM_SHIPPER_TO_DP: OrderFFMStatus.Awaiting3PLPickup,
  FROM_DP_TO_DRIVER: OrderFFMStatus.InTransit,
  // DRIVER_PICKUP_SCAN: OrderFFMStatus.PickedUp3PL,
  ROUTE_INBOUND_SCAN: OrderFFMStatus.InTransit,
  HUB_INBOUND_SCAN: OrderFFMStatus.InTransit,
  ADDED_TO_SHIPMENT: OrderFFMStatus.InTransit,
  PARCEL_ROUTING_SCAN: OrderFFMStatus.Stocked3PL,
  DRIVER_INBOUND_SCAN: OrderFFMStatus.InDelivery,
  DELIVERY_FAILURE: OrderFFMStatus.FailedDelivery,
  RTS: OrderFFMStatus.AwaitingReturn,
  DELIVERY_SUCCESS: OrderFFMStatus.Delivered,
  TRANSFERRED_TO_THIRD_PARTY: OrderFFMStatus.InTransit,
  FORCED_SUCCESS: OrderFFMStatus.Delivered,
};

const NJV_WEBHOOK_STATUS = {
  // 'Picked Up': {
  //   'In Transit To Origin Hub': OrderFFMStatus.PickedUp3PL,
  // },
  'Arrived at Origin Hub': OrderFFMStatus.InTransit,
  'Arrived at Transit Hub': OrderFFMStatus.InTransit,
  'Arrived at Destination Hub': OrderFFMStatus.InTransit,
  'In Transit to Next Sorting Hub': OrderFFMStatus.InTransit,
  'On Vehicle for Delivery': OrderFFMStatus.InDelivery,
  'At PUDO': {
    'Pending Customer Collection': OrderFFMStatus.Stocked3PL,
  },
  Delivered: {
    'Collected by Customer': OrderFFMStatus.Delivered,
    'Left at Doorstep': OrderFFMStatus.Delivered,
    'Received by Customer': OrderFFMStatus.Delivered,
  },
  'Delivery Exception': {
    'Pending Reschedule': OrderFFMStatus.FailedDelivery,
    'Max Attempts Reached': OrderFFMStatus.AwaitingReturn,
    'ParParcel Overstayed at PUDO': OrderFFMStatus.AwaitingReturn,
    'Return to Sender Initiated': OrderFFMStatus.InReturn,
  },
  'Returned to Sender': OrderFFMStatus.Returned,
  Cancelled: OrderFFMStatus.Canceled,
};

export const VNP_WEBHOOK_STATUS = {
  // 7: OrderFFMStatus.PickedUp3PL,
  9: OrderFFMStatus.Canceled,
  10: OrderFFMStatus.InTransit,
  13: OrderFFMStatus.InTransit,
  11: OrderFFMStatus.Stocked3PL,
  12: OrderFFMStatus.InDelivery,
  14: OrderFFMStatus.Delivered,
  15: OrderFFMStatus.FailedDelivery,
  16: OrderFFMStatus.AwaitingReturn,
  17: OrderFFMStatus.AwaitingReturn,
  18: OrderFFMStatus.InReturn,
  19: OrderFFMStatus.Returned,
  20: OrderFFMStatus.Returned,
  21: OrderFFMStatus.Delivered,
  22: OrderFFMStatus.Canceled,
};
const VTP_WEBHOOK_STATUS = {
  101: OrderFFMStatus.Canceled,
  // 105: OrderFFMStatus.PickedUp3PL,
  107: OrderFFMStatus.Canceled,
  // 200: OrderFFMStatus.Stocked3PL,
  300: OrderFFMStatus.InTransit,
  301: OrderFFMStatus.InTransit,
  302: OrderFFMStatus.InTransit,
  303: OrderFFMStatus.InTransit,
  400: OrderFFMStatus.Stocked3PL,
  401: OrderFFMStatus.Stocked3PL,
  402: OrderFFMStatus.Stocked3PL,
  403: OrderFFMStatus.Stocked3PL,
  500: OrderFFMStatus.InDelivery,
  501: OrderFFMStatus.Delivered,
  502: OrderFFMStatus.InReturn,
  503: OrderFFMStatus.Canceled,
  504: OrderFFMStatus.Returned,
  505: OrderFFMStatus.FailedDelivery,
  506: OrderFFMStatus.FailedDelivery,
  507: OrderFFMStatus.FailedDelivery,
  508: OrderFFMStatus.InDelivery,
  509: OrderFFMStatus.InTransit,
  515: OrderFFMStatus.InReturn,
  550: OrderFFMStatus.InDelivery,
};

const GHTK_WEBHOOK_STATUS = {
  '-1': OrderFFMStatus.Canceled,
  3: OrderFFMStatus.InTransit,
  4: OrderFFMStatus.InDelivery,
  5: OrderFFMStatus.Delivered,
  6: OrderFFMStatus.Delivered,
  9: OrderFFMStatus.FailedDelivery,
  10: OrderFFMStatus.FailedDelivery,
  49: OrderFFMStatus.FailedDelivery,
  410: OrderFFMStatus.FailedDelivery,
  13: OrderFFMStatus.LostBy3PL,
  20: OrderFFMStatus.InReturn,
  21: OrderFFMStatus.Returned,
  // 123: OrderFFMStatus.PickedUp3PL,
};

const SPXVN_WEBHOOK_STATUS = {
  7001: OrderFFMStatus.Canceled,
  2001: OrderFFMStatus.InTransit,
  2006: OrderFFMStatus.InDelivery,
  4001: OrderFFMStatus.Delivered,
  6001: OrderFFMStatus.InReturn,
  6003: OrderFFMStatus.Returned,
  3001: OrderFFMStatus.FailedDelivery,
};

const BEST_THAILAND_STATUS = {
  'send_from_hub': OrderFFMStatus.InTransit,
  'send_from_station': OrderFFMStatus.InTransit,
  'arrive_hub': OrderFFMStatus.Stocked3PL,
  'out_for_delivery': OrderFFMStatus.InDelivery,
  'delivered': OrderFFMStatus.Delivered,
  'package_return': OrderFFMStatus.InReturn,
  'return_success': OrderFFMStatus.Returned,
  'RoS01':OrderFFMStatus.FailedDelivery,
  'RoS02':OrderFFMStatus.FailedDelivery,
  'RoS03':OrderFFMStatus.FailedDelivery,
  'RoS04':OrderFFMStatus.FailedDelivery,
  'RoS05':OrderFFMStatus.FailedDelivery,
  'TH011':OrderFFMStatus.FailedDelivery,
  'TH012':OrderFFMStatus.FailedDelivery,
  'TH013':OrderFFMStatus.FailedDelivery,
  'TH021':OrderFFMStatus.FailedDelivery,
  'TH022':OrderFFMStatus.FailedDelivery,
  'TH023':OrderFFMStatus.FailedDelivery,
  'TH024':OrderFFMStatus.FailedDelivery,
  'TH031':OrderFFMStatus.FailedDelivery,
  'TH032':OrderFFMStatus.FailedDelivery,
  'TH033':OrderFFMStatus.FailedDelivery,
  'TH034':OrderFFMStatus.FailedDelivery,
  'TH035':OrderFFMStatus.FailedDelivery,
  'TH041':OrderFFMStatus.FailedDelivery,
  'TH042':OrderFFMStatus.FailedDelivery,
  'TH861':OrderFFMStatus.FailedDelivery,
  'TH871':OrderFFMStatus.FailedDelivery,
  'TH881':OrderFFMStatus.FailedDelivery,
  'TH882':OrderFFMStatus.FailedDelivery,
  'TH883':OrderFFMStatus.FailedDelivery,
  'TH891':OrderFFMStatus.FailedDelivery,
  'TH901':OrderFFMStatus.FailedDelivery,
  'TH902':OrderFFMStatus.FailedDelivery,
  'TH911':OrderFFMStatus.FailedDelivery,
  'TH921':OrderFFMStatus.FailedDelivery,
  'TH931':OrderFFMStatus.FailedDelivery,
  'TH941':OrderFFMStatus.FailedDelivery,
  'TH951':OrderFFMStatus.FailedDelivery,
  'TH952':OrderFFMStatus.FailedDelivery,
  'TH961':OrderFFMStatus.FailedDelivery,
  'TH971':OrderFFMStatus.FailedDelivery,
  'TH981':OrderFFMStatus.FailedDelivery,
  'TH991':OrderFFMStatus.FailedDelivery,
  'W161':OrderFFMStatus.FailedDelivery,
  'W162':OrderFFMStatus.FailedDelivery,
  'W261':OrderFFMStatus.FailedDelivery,
  'W262':OrderFFMStatus.FailedDelivery,
  'W331':OrderFFMStatus.FailedDelivery,
  'W332':OrderFFMStatus.FailedDelivery,
  'W333':OrderFFMStatus.FailedDelivery,
  'W334':OrderFFMStatus.FailedDelivery,
  'W372':OrderFFMStatus.FailedDelivery,
  'W373':OrderFFMStatus.FailedDelivery,
  'W374':OrderFFMStatus.FailedDelivery,
  'W375':OrderFFMStatus.FailedDelivery,
  'W376':OrderFFMStatus.FailedDelivery,
  'W381':OrderFFMStatus.FailedDelivery,
  'W382':OrderFFMStatus.FailedDelivery,
  'W401':OrderFFMStatus.FailedDelivery,
  'W402':OrderFFMStatus.FailedDelivery,
};

const JNT_BANGKOK_STATUS = {
  'Picked Up':OrderFFMStatus.PickedUp3PL,
  'Departure':OrderFFMStatus.InTransit,
  'Arrival ':OrderFFMStatus.Stocked3PL,
  'On Delivery':OrderFFMStatus.InDelivery,
  'Signature':OrderFFMStatus.Delivered,
  'Return Confirmation':OrderFFMStatus.InReturn,
  'Return Signature':OrderFFMStatus.Returned,
  'Problematic':OrderFFMStatus.FailedDelivery,
  // 'waybill closed': 'cancel waybill',
}

const MIAOSHOU_STATUS = {
  'ship_success': OrderFFMStatus.InTransit,
  'ship_fail': OrderFFMStatus.FailedDelivery,
  'wait_receiver_confirm': OrderFFMStatus.InDelivery,
  'refunding': OrderFFMStatus.InDelivery,
  'returned': OrderFFMStatus.Returned,
  'canceled': OrderFFMStatus.Canceled,
  'finished': OrderFFMStatus.Delivered,
};

export function parseJntTrackings(updates: Record<string, any>[]): ITrackingUpdate[] {
  console.log('updates', updates);
  return orderBy(
    updates
      ?.map(
        i =>
          ({
            updatedAt: moment
              .tz(i.scantime || i.scanTime || i.acceptTime, 'YYYY-MM-DD HH:mm:ss', 'Asia/Manila')
              .toDate(),
            status: JNT_STATUS[i.code],
            note: i.desc,
            raw: i,
          } as ITrackingUpdate),
      )
      .filter(i => i.status) || [],
    'updatedAt',
    'desc',
  );
}
export function parseJntBangkokTrackings(updates: Record<string, any>[]): ITrackingUpdate[] {
  console.log('updates', updates);
  return orderBy(
    updates
      ?.map(
        i =>
          ({
            updatedAt: moment
              .tz(i.scantime || i.scanTime || i.acceptTime, 'YYYY-MM-DD HH:mm:ss', 'Asia/Bangkok')
              .toDate(),
            status: JNT_BANGKOK_STATUS[i.scantype],
            note: i.desc,
            raw: i,
          } as ITrackingUpdate),
      )
      .filter(i => i.status) || [],
    'updatedAt',
    'desc',
  );
}

export function parseNimbusTracking(data: INimBusTracking): ITrackingUpdate[] {
  return orderBy(
    data.history?.map((item, index) => {
      if (item.event_time == data.history[index + 1]?.event_time) {
        item.event_time = moment
          .tz(item.event_time, 'YYYY-MM-DD HH:mm', 'Asia/Kolkata')
          .add(1, 'minute')
          .format('YYYY-MM-DD HH:mm');
      }
      const updatedAt = moment.tz(item.event_time, 'YYYY-MM-DD HH:mm', 'Asia/Kolkata').toDate();
      let status: OrderFFMStatus | null = null;
      switch (item.status_code) {
        case NimbusStatus.DL:
          status = OrderFFMStatus.Delivered;
          break;
        case NimbusStatus.EX:
          status = OrderFFMStatus.FailedDelivery;
          break;
        case NimbusStatus.IT:
          status = OrderFFMStatus.InTransit;
          if (item.message?.trim()?.toLowerCase() == 'lost') {
            status = OrderFFMStatus.LostBy3PL;
          } else if (data.history[index + 1]?.status_code == NimbusStatus.PP) {
            status = OrderFFMStatus.PickedUp3PL;
            if (data.history[index + 1]?.message == '1340') {
              status = null;
            }
          }
          break;
        case NimbusStatus.LT:
          status = OrderFFMStatus.LostBy3PL;
          break;
        case NimbusStatus.RT:
          status = OrderFFMStatus.AwaitingReturn;
          break;
        case NimbusStatus.OFD:
          status = OrderFFMStatus.InDelivery;
          break;
        case NimbusStatus['RT-IT']:
          status = OrderFFMStatus.InReturn;
          if (item.message == 'SIGNATURE IMAGE') status = OrderFFMStatus.Returned;
          break;
        case NimbusStatus['RT-DL']:
          status = OrderFFMStatus.Returned;
          break;
      }
      if (data.status === 'cancelled' && index === 0) {
        status = OrderFFMStatus.Canceled;
      }
      return {
        updatedAt: updatedAt,
        status: status,
        note: item.message,
        raw: item,
      } as ITrackingUpdate;
    }),
    'updatedAt',
    'desc',
  );
}

export function parseBluedartTracking(data: IBluedartTracking): ITrackingUpdate[] {
  return orderBy(
    data.Scans.ScanDetail?.map((item, index) => {
      const updatedAt = moment
        .tz(item.ScanDate + ' ' + item.ScanTime, 'DD-MMM-YYYY HH:mm', 'Asia/Kolkata')
        .toDate();
      let status: OrderFFMStatus | null = null;
      switch (item.ScanCode) {
        case 15:
          status = OrderFFMStatus.PickedUp3PL;
          break;
        case 1:
          status = OrderFFMStatus.Stocked3PL;
          break;
        case 3:
          if (item.ScanGroupType === 'S') status = OrderFFMStatus.InTransit;
          if (item.ScanGroupType === 'T') status = OrderFFMStatus.FailedDelivery;
          break;
          break;
        case 2:
          status = OrderFFMStatus.InDelivery;
          break;
        case 29:
        case 11:
        case 12:
          status = OrderFFMStatus.FailedDelivery;
          break;
        case 24:
          status = OrderFFMStatus.AwaitingReturn;
          break;
        case 503:
          status = OrderFFMStatus.InReturn;
          break;
        case 74:
          status = OrderFFMStatus.Returned;
          break;
        case 0:
          status = OrderFFMStatus.Delivered;
          break;
        case 20:
          if (item.ScanGroupType === 'S') status = OrderFFMStatus.Stocked3PL;
          if (item.ScanGroupType === 'T') status = OrderFFMStatus.FailedDelivery;
          break;
      }
      return {
        updatedAt: updatedAt,
        status: status,
        note: item.Scan,
        raw: item,
      } as ITrackingUpdate;
    }),
    'updatedAt',
    'desc',
  );
}

export function parsePosLajuTrackings(update: IPosLajuTracking): ITrackingUpdate[] {
  return orderBy(
    update.tracking_data
      ?.map((i, index) => {
        const { process, date, office, event_type, epod } = i;
        const updatedAt = moment.tz(date, 'DD MMM YYYY, hh:mm:ss A', 'Asia/Kuala_Lumpur').toDate();
        const oStatus: OrderFFMStatus = POS_LAJU_STATUS[event_type];
        return {
          updatedAt,
          status: oStatus,
          note: `${process} [${office}]${!isEmpty(epod) ? ` [${epod}]` : ''}`,
          raw: i,
        } as ITrackingUpdate;
      })
      .filter(i => i.status) || [],
    'updatedAt',
    'desc',
  );
}

export function parseJneIdTrackings(updates: IJneTracking[]): ITrackingUpdate[] {
  let isReturn = false;
  let lastStatus: OrderFFMStatus;
  return orderBy(
    updates
      ?.map((i, index) => {
        const { status, updatedAt } = i;
        if (lastStatus) {
          return { updatedAt, raw: i, note: status, status: lastStatus };
        }
        let oStatus: OrderFFMStatus = null;
        if (status.startsWith('PICKED UP BY COURIER')) {
          oStatus = OrderFFMStatus.PickedUp3PL;
        } else if (status.includes('DELIVERED TO')) {
          oStatus = OrderFFMStatus.Delivered;
          lastStatus = oStatus;
        } else if (status.includes('RECEIVED AT INBOUND STATION')) {
          if (updates[index - 1]?.status.includes('DELIVERED TO')) {
            oStatus = OrderFFMStatus.Delivered;
            lastStatus = oStatus;
          }
        } else if (status.startsWith('RECEIVED AT')) {
          oStatus = OrderFFMStatus.Stocked3PL;
        } else if (status.startsWith('DEPARTED FROM')) {
          oStatus = OrderFFMStatus.InTransit;
        } else if (status.startsWith('PROCESSED AT')) {
          oStatus = OrderFFMStatus.InTransit;
        } else if (status.startsWith('SHIPMENT FORWARDED FROM')) {
          oStatus = OrderFFMStatus.InTransit;
        } else if (status.startsWith('WITH DELIVERY COURIER')) {
          oStatus = OrderFFMStatus.InDelivery;
        } else if (
          status.startsWith('CONSIGNEE REFUSE') ||
          status.startsWith('INCOMPLETE CONSIGNEE') ||
          status.startsWith('RECIPIENTS REFUSED') ||
          status.startsWith('NOBODY AT') ||
          status.startsWith('DELIVERY PROBLEM') ||
          status.startsWith('OFFICE CLOSED') ||
          status.startsWith('NOT INHABITED') ||
          status.startsWith('UNABLE TO DELIVER') ||
          status.startsWith('HOME/OFFICE ADDRESS ALREADY MATCH,BUT') ||
          status.startsWith('WAITING COD PAYMENT') ||
          status.startsWith('WAITING FOR COD AMOUNT CONFIRMATION') ||
          status.startsWith('SHIPMENT PICKED UP BY CONSIGNEE AS REQUESTED BY SHIPPER/CONSIGNEE') ||
          status.startsWith('COMPLETE ADDRESS, BUT') ||
          status.startsWith('OVER 3 DAYS IN LOCKER BOX') ||
          status.startsWith('UNDEL') ||
          status.startsWith('SHIPMENT CANCELED') ||
          status.startsWith('SHIPMENT PROBLEM') ||
          status.startsWith('NO ACCESS DUE TO FORCE MAJEURE') ||
          status.startsWith('SHIPMENT BEING HELD AS SHIPPERS REQUEST') ||
          status.startsWith('OUT OF YES SERVICE AREA')
        ) {
          oStatus = OrderFFMStatus.FailedDelivery;
        } else if (
          status.includes('CLOSED') &&
          updates[index - 1]?.status?.startsWith('WITH DELIVERY COURIER')
        ) {
          oStatus = OrderFFMStatus.FailedDelivery;
        } else if (status.includes('SHIPMENT RETURN')) {
          oStatus = OrderFFMStatus.InReturn;
          isReturn = true;
        } else if (status.includes('RETURN SHIPMENT TO')) {
          oStatus = OrderFFMStatus.Returned;
          lastStatus = oStatus;
        }
        if (isReturn && oStatus !== OrderFFMStatus.Returned) {
          oStatus = OrderFFMStatus.InReturn;
        }
        return {
          updatedAt,
          status: oStatus,
          note: status,
          raw: i,
        } as ITrackingUpdate;
      })
      .filter(i => i.status) || [],
    'updatedAt',
    'desc',
  );
}

export function parseJntThTrackings(updates: Record<string, any>[]): ITrackingUpdate[] {
  return orderBy(
    updates
      ?.map(i => {
        let status;
        if (i.scansCode) status = JNT_THAI_STATUS[i.scansCode];
        if (i.scanStatus && JNT_SCAN_STATUS[i.scanStatus]) status = JNT_SCAN_STATUS[i.scanStatus];
        if (i.scanType && JNT_SCAN_STATUS[i.scanType]) status = JNT_SCAN_STATUS[i.scanType];
        // if (status === OrderFFMStatus.PickedUp3PL) {
        //   if (i.scanStatus != 'Picked Up') {
        //     status = null;
        //   }
        // }
        return {
          updatedAt: moment
            .tz(i.scantime || i.scanTime || i.acceptTime, 'YYYY-MM-DD HH:mm:ss', 'Asia/Bangkok')
            .toDate(),
          status: status,
          note: !isNil(i.returnReason)
            ? !isNil(i.remark)
              ? `${i.returnReason} (${i.remark})`
              : i.returnReason
            : i.remark,
          raw: i,
        } as ITrackingUpdate;
      })
      .filter(i => i.status) || [],
    'updatedAt',
    'desc',
  );
}

export function parseNJVTrackings(updates: Record<string, any>[]): ITrackingUpdate[] {
  console.log('njv updates', updates);
  const check = updates.find(x => x.type == 'RTS');
  return orderBy(
    updates?.map(i => {
      let status =
        i.type != 'ROUTE_INBOUND_SCAN'
          ? NJV_STATUS[i.type]
          : !check
          ? NJV_STATUS[i.type]
          : OrderFFMStatus.InReturn; // có RTS trước thì ROUTE_INBOUND_SCAN sẽ thành InReturn
      if (status === OrderFFMStatus.Delivered) {
        if (i.data?.is_rts) {
          status = OrderFFMStatus.Returned;
        }
      }
      return {
        updatedAt: new Date(i.time),
        status,
        note: i.data?.failure_reason?.en || i?.data?.hub_name,
        raw: i,
      } as ITrackingUpdate;
    }) || [],
    'updatedAt',
    'desc',
  );
}

export function parseFlashMyTrackings(order: IFlashCrawlOrder): ITrackingUpdate[] {
  let returnAt: Date = null;
  const updates = order.routes.reverse();
  return orderBy(
    updates?.map((i, index) => {
      const { state, routed_at, message, ticket_route_extra_value_vo_list, route_action } = i;
      let status: OrderFFMStatus;
      const updatedAt = moment.tz(routed_at, 'YYYY-MM-DD HH:mm:ss', 'Asia/Kuala_Lumpur').toDate();
      if (route_action == 'CONTINUE_TRANSPORT') {
        returnAt = null;
      }
      if (returnAt) {
        if (updatedAt >= returnAt) {
          status = OrderFFMStatus.InReturn;
          if (state == 5) {
            status = OrderFFMStatus.Returned;
          }
        }
      }
      if (!returnAt || updatedAt < returnAt) {
        if (state == 1) {
          status = OrderFFMStatus.PickedUp3PL;
        }
        if (state == 2) {
          status = OrderFFMStatus.InTransit;
        }
        if (state == 3) {
          status = OrderFFMStatus.InDelivery;
        }
        if (state == 4 && updates[index - 1]?.state == 3) {
          status = OrderFFMStatus.FailedDelivery;
        }
        if (state == 5) {
          status = OrderFFMStatus.Delivered;
        }
        if (state == 6) {
          status = OrderFFMStatus.AwaitingReturn;
        }
        if (state == 7 && route_action != 'CONTINUE_TRANSPORT') {
          status = OrderFFMStatus.InReturn;
          if (!returnAt) returnAt = updatedAt;
        }
        if (state == 9) {
          status = OrderFFMStatus.Canceled;
        }
      }
      return {
        updatedAt,
        status,
        note:
          message +
          (ticket_route_extra_value_vo_list
            ? '\n' + ticket_route_extra_value_vo_list.join('\n')
            : ''),
        raw: i,
      } as ITrackingUpdate;
    }) || [],
    'updatedAt',
    'desc',
  );
}

export function parseHalTrackings(order: IHalTracking): ITrackingUpdate[] {
  const {
    shipment_info: { bill_number },
    tracking_events,
  } = order;
  let isReturn = false;
  return orderBy(
    tracking_events.map(i => {
      const { date, message, delivery_state } = i;
      const updatedAt = moment.tz(date, 'YYYY-MM-DD HH:mm:ss', 'Asia/Ho_Chi_Minh').toDate();
      let status = HAL_STATUS[delivery_state];
      if (status === OrderFFMStatus.InReturn) {
        isReturn = true;
      }
      if (isReturn) {
        if (status === OrderFFMStatus.Delivered) {
          status = OrderFFMStatus.Returned;
        } else {
          status = OrderFFMStatus.InReturn;
        }
      }
      return {
        updatedAt,
        status,
        note: message,
        raw: i,
      } as ITrackingUpdate;
    }),
    'updatedAt',
    'desc',
  );
}

export function parseAnousithTrackings(order: IAnousithTracking): ITrackingUpdate[] {
  const {
    trackingId,
    createdDate,
    originReceiveDate,
    originSendDate,
    destReceiveDate,
    destSendDate,
    sendCompleteDate,
    receiveBackwardDate,
    isBackward,
  } = order;
  const updates: ITrackingUpdate[] = [];
  if (originReceiveDate) {
    updates.push({
      updatedAt: new Date(originReceiveDate),
      status: OrderFFMStatus.PickedUp3PL,
      raw: order,
      note: '',
    });
  }
  if (originSendDate) {
    updates.push({
      updatedAt: new Date(originSendDate),
      status: OrderFFMStatus.InTransit,
      raw: order,
      note: '',
    });
  }
  if (destReceiveDate) {
    updates.push({
      updatedAt: new Date(destReceiveDate),
      status: OrderFFMStatus.Stocked3PL,
      raw: order,
      note: '',
    });
  }
  if (sendCompleteDate) {
    updates.push({
      updatedAt: new Date(sendCompleteDate),
      status: isBackward ? OrderFFMStatus.Returned : OrderFFMStatus.Delivered,
      raw: order,
      note: '',
    });
  }
  if (receiveBackwardDate) {
    updates.push({
      updatedAt: new Date(receiveBackwardDate),
      status: OrderFFMStatus.InReturn,
      raw: order,
      note: '',
    });
  }
  return orderBy(updates, 'updatedAt', 'desc');
}

export function parseFlashKerryTrackings(data: IKerryTrackingData): ITrackingUpdate[] {
  return orderBy(
    data.ref.shipment_status?.map(i => {
      const { s_desc, s_datetime, s_action } = i;
      const updatedAt = moment.tz(s_datetime, 'YYYY-MM-DDTHH:mm:ss', 'Asia/Ho_Chi_Minh').toDate();
      return {
        updatedAt,
        status: KERRY_STATUS[s_desc],
        note: s_action,
        raw: i,
      } as ITrackingUpdate;
    }) || [],
    'updatedAt',
    'desc',
  );
}

export function parseBestMyTrackings(order: IBestOrder): ITrackingUpdate[] {
  console.log('best updates', order);
  let returnAt = 0;
  if (order.isReturn) {
    for (const { remark, acceptTime } of order.traceDetails) {
      if (remark.includes('Return to origin')) {
        returnAt = acceptTime;
        break;
      }
    }
  }
  return orderBy(
    order.traceDetails?.map(i => {
      const { remark, acceptTime, scanTypeCode } = i;
      let status: OrderFFMStatus;
      if (returnAt) {
        if (acceptTime >= returnAt) {
          status = OrderFFMStatus.InReturn;
          if (scanTypeCode == '17') {
            status = OrderFFMStatus.Returned;
          }
        }
      }
      if (!returnAt || acceptTime < returnAt) {
        if (remark.includes('delivery on the way')) {
          status = OrderFFMStatus.InDelivery;
        }
        if (remark.includes('on the way to')) {
          status = OrderFFMStatus.InTransit;
        }
        if (remark.includes('arrived')) {
          status = OrderFFMStatus.Stocked3PL;
        }
        if (remark.includes('has been cancelled')) {
          status = OrderFFMStatus.Canceled;
        }
        if (scanTypeCode == '17') {
          status = order.isReturn ? OrderFFMStatus.Returned : OrderFFMStatus.Delivered;
        }
        if (remark.includes('has registered as a problem item')) {
          status = OrderFFMStatus.FailedDelivery;
        }
      }
      return {
        updatedAt: new Date(acceptTime),
        status,
        note: remark,
        raw: i,
      } as ITrackingUpdate;
    }) || [],
    'updatedAt',
    'desc',
  );
}

export function parseBestThTrackings(order: IBestThOrder): ITrackingUpdate[] {
  console.log('best thailand updates', order);
  return orderBy(
    order.traces?.trace?.map(i => {
      const status = BEST_THAILAND_STATUS[i.packageStatusCode];
      return {
        updatedAt: new Date(i.operateTime),
        status,
        note: i.statusCodeDesc,
        raw: i,
      };
    }),
    'updatedAt',
    'desc',
  );
}

export function parseShopeeTrackings(order: IShopeeTracking): ITrackingUpdate[] {
  const { sls_tracking_number, tracking_list } = order;
  let isReturn = false;
  return orderBy(
    tracking_list.map(i => {
      const { status, timestamp, message } = i;
      const updatedAt = new Date(timestamp * 1000);
      let statusMapping = SHOPEE_STATUS[status];
      if (statusMapping === OrderFFMStatus.InReturn) {
        isReturn = true;
      }
      if (isReturn) {
        if (statusMapping === OrderFFMStatus.Delivered) {
          statusMapping = OrderFFMStatus.Returned;
        } else {
          statusMapping = OrderFFMStatus.InReturn;
        }
      }
      return {
        updatedAt,
        status: statusMapping,
        note: message,
        raw: i,
      } as ITrackingUpdate;
    }),
    'updatedAt',
    'desc',
  );
}

export function parseNJVWebhook(update: Record<string, any>): ITrackingUpdate {
  const state =
    update?.picked_up_information?.state ||
    update?.arrived_at_pudo_information?.state ||
    update?.delivery_information?.state ||
    update?.delivery_exception?.state;
  const status = state
    ? typeof NJV_WEBHOOK_STATUS[update.status] === 'object' &&
      !isEmpty(NJV_WEBHOOK_STATUS[update.status])
      ? NJV_WEBHOOK_STATUS[update.status][state]
      : NJV_WEBHOOK_STATUS[update.status]
    : NJV_WEBHOOK_STATUS[update.status];

  return {
    updatedAt: new Date(update.timestamp),
    status,
    note: update.comments ?? update?.delivery_exception?.failure_reason,
    raw: update,
  } as ITrackingUpdate;
}

export function parseVNPWebhook(update: IDataVNP): ITrackingUpdate {
  const status = VNP_WEBHOOK_STATUS[update.status];

  const updatedAt = new Date(update.updatedAt);

  return {
    updatedAt: new Date(
      moment(updatedAt, 'DD/MM/YYYY HH:mm:ss')
        .add(-7, 'hours')
        .format('MM/DD/YYYY HH:mm:ss'),
    ),
    status,
    note: update?.note,
    raw: update,
  } as ITrackingUpdate;
}

export function parseVTPWebhook(update: Record<string, any>): ITrackingUpdate {
  const status = VTP_WEBHOOK_STATUS[update.ORDER_STATUS];
  return {
    updatedAt: new Date(
      moment(update.ORDER_STATUSDATE, 'DD/MM/YYYY HH:mm:ss')
        .add(-7, 'hours')
        .format('MM/DD/YYYY HH:mm:ss'),
    ),
    status,
    note: update.NOTE,
    raw: update,
  } as ITrackingUpdate;
}

export function parseGHTKWebhook(update: Record<string, any>): ITrackingUpdate {
  const status = GHTK_WEBHOOK_STATUS[update.status_id];
  return {
    updatedAt: new Date(update.action_time),
    status,
    note: update.reason,
    raw: update,
  } as ITrackingUpdate;
}

export function parseSPXVNWebhook(update: Record<string, any>): ITrackingUpdate {
  const status = SPXVN_WEBHOOK_STATUS[update.status_code];
  return {
    updatedAt: new Date(moment.unix(update.timestamp).format('MM/DD/YYYY HH:mm:ss')),
    status,
    note: update.message,
    raw: update,
  } as ITrackingUpdate;
}

export function parseMiaoshouTrackings(order: any): ITrackingUpdate {
  console.log('miaoshou updates', order);
  const status = MIAOSHOU_STATUS[order.appPackageOperateStatus];
  const updatedAt = moment.tz(order.orderInfo?.gmtOrderModified, 'YYYY-MM-DD HH:mm:ss', 'Asia/Shanghai').toDate();
  return {
    updatedAt, 
    status,
    note: '',
    raw: {
      orderInfo: order.orderInfo ?? '',
      appPackageStatus: order.appPackageStatus ?? '',
      appPackageStatusText: order.appPackageStatusText ?? '',
      appPackageOperateStatus: order.appPackageOperateStatus ?? '',
      appPackageNo: order.appPackageNo ?? '',
    },
  } as ITrackingUpdate;
}
