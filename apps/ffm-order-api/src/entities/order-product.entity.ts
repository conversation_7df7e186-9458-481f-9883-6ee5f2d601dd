import {Expose, Type} from 'class-transformer';
import {Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn,} from 'typeorm';
import {Order} from './order.entity';
import { OneToMany } from 'typeorm';
import { OrderProductComboVariant } from './order-product-combo-variant.entity';

@Entity({
  name: 'order_products',
  database: process.env.DATABASE_ORDER_FFM,
})
export class OrderProduct {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id: number;

  @Column({
    name: 'order_id',
    type: 'float',
    nullable: true,
  })
  @Index()
  @Expose()
  order_id?: number;

  @Column({
    name: 'product_id',
    type: 'int',
  })
  @Expose()
  @Index()
  productId: number;

  @Column({
    name: 'product_name',
    type: 'varchar',
    nullable: true
  })
  @Expose()
  productName: string;

  @Column({
    name: 'product_detail',
    type: 'json',
    nullable: true
  })
  @Expose()
  productDetail?: Record<string, any>;

  @Column({
    name: 'quantity',
    type: 'float',
    nullable: true,
  })
  @Expose()
  quantity?: number;

  @Column({
    name: 'price',
    type: 'float',
    nullable: true,
  })
  @Expose()
  price?: number;

  @Column({
    name: 'weight',
    type: 'float',
    nullable: true,
  })
  @Expose()
  weight?: number;

  @ManyToOne(
    () => Order,
    order => order.products,
  )
  @JoinColumn({
    name: 'order_id',
  })
  @Expose()
  order: Order;

  @Column({
    name: 'creator_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  creatorId: number;
  
  @OneToMany(
    () => OrderProductComboVariant,
    orderProductCombos => orderProductCombos.orderProduct,
    { cascade: true },
  )
  @Type(() => OrderProductComboVariant)
  @Expose()
  orderProductCombos: OrderProductComboVariant[];
}
