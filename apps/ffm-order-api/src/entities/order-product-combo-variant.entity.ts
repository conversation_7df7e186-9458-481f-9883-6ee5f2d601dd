import { Expose } from 'class-transformer';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { Column, CreateDateColumn, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { OrderProduct } from './order-product.entity';
import { Order } from './order.entity';

@Entity({
  name: 'order_product_combo_variant',
  database: process.env.DATABASE_ORDER_FFM,
})
export class OrderProductComboVariant {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id?: number;

  @CreateDateColumn({
    name: 'created_at',
    type: 'timestamp with time zone',
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  createdAt?: Date;

  @Column({
    name: 'updated_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  updatedAt?: Date;

  @Column({
    name: 'order_product_id',
    type: 'float',
    nullable: true,
  })
  @Index()
  @Expose()
  @DateTransform()
  orderProductId?: number;

  @ManyToOne(
    () => OrderProduct,
    orderProduct => orderProduct.orderProductCombos,
  )
  @JoinColumn({
    name: 'order_product_id',
  })
  @Expose()
  orderProduct?: OrderProduct;

  @Column({
    name: 'combo_sku',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  comboSku?: string;

  @Column({
    name: 'sku',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  sku?: string;

  @Column({
    name: 'attributes',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  attributes?: string;

  @Column({
    name: 'weight',
    type: 'float',
    default: 0,
  })
  @Expose()
  weight?: number;

  @Column({
    name: 'qty',
    type: 'float',
    default: 0,
  })
  @Expose()
  qty?: number;

  @Column({ type: 'integer', name: 'variant_id', nullable: true })
  @Index()
  @Expose()
  @NonEmptyTransform()
  variantId?: number;

  @Column({ type: 'integer', name: 'product_id', nullable: true })
  @Expose()
  @NonEmptyTransform()
  productId?: number;
  
  @Column({
    name: 'country_id',
    type: 'varchar',
    nullable: true,
  })
  @Index()
  @Expose()
  countryId: string;

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Index()
  @Expose()
  companyId?: number;

  @Column({
    name: 'order_id',
    type: 'float',
    nullable: true,
  })
  @Index()
  @Expose()
  orderId?: number;

  @ManyToOne(
    () => Order,
    order => order.orderProductCombo,
  )
  @JoinColumn({
    name: 'order_id',
  })
  @Expose()
  order?: Order;
}
