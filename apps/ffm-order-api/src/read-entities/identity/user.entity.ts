import { Expose } from 'class-transformer';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { IntegerIdEntity } from 'core/entities/base/integer-id-entity.entity';
import {
  Column, Entity
} from 'typeorm';

@Entity({
  name: 'users',
  database: process.env.DATABASE_IDENTITY,
  synchronize: false
})

export class User extends IntegerIdEntity {
  @Column({
    name: 'name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  name?: string;

  @Column({
    name: 'email',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  email?: string;

  @Column({
    name: 'phone',
    type: 'varchar',
    nullable: true,
    length: 25,
  })
  @Expose()
  @NonEmptyTransform()
  phone?: string;

  @Column({
    name: 'display_id',
    type: 'varchar',
    nullable: true,
    length: 25,
  })
  @Expose()
  @NonEmptyTransform()
  displayId?: string;

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  companyId?: number;

  @Column('varchar', {
    name: 'countries',
    nullable: true,
    array: true,
  })
  @Expose()
  @NonEmptyTransform()
  countries: string[] | null;
}
