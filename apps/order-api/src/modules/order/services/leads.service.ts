import { Amqp<PERSON>onnection, Nack, <PERSON><PERSON><PERSON> } from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { InjectQueue } from '@nestjs/bull';
import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Users } from 'apps/ffm-catalog-api/src/read-entities/identity-entities/Users';
import { PlainCareStateGroup } from 'apps/order-api/src/constants/care-state-groups.constant';
import {
  CANCEL_LEAD_STATES,
  CONFIRM_LEAD_STATES,
  CONFIRM_LEAD_STATES_AFTER_SALES,
  GATHERABLE_STATES,
  GATHERABLE_STATES_AFTER_SALES,
  MAPPING_CARE_STATE_TO_GROUP,
  NEXT_CARE_STATES,
  NEXT_CARE_STATES_FE,
  NON_COUNT_REPEAT_TIMES_CARE_STATES,
  PlainCareState,
} from 'apps/order-api/src/constants/care-states.constant';
import {
  LEAD_STATES_CAN_BE_CONSIDERED_AS_ASSIGNED,
  LEAD_STATES_CAN_BE_CONSIDERED_AS_PROCESSING,
} from 'apps/order-api/src/constants/distribute-lead.constant';

import { NOT_SCAN_FOR_POSSIBLE_DUPLICATES_STATUSES } from 'apps/order-api/src/constants/order-statuses.constant';
import { CreateOrdersFilterDto } from 'apps/order-api/src/dtos/create-orders-filter.dto';
import { CreateLeadCareItemDto } from 'apps/order-api/src/dtos/lead-item.dto';
import {
  BulkCancelDuplicateLeadsDto,
  CreateLeadDto,
  UpdateLeadDto,
} from 'apps/order-api/src/dtos/lead.dto';
import { ManualDistributeLeadsDto } from 'apps/order-api/src/dtos/manual-distribute-leads.dto';
import { OrderCarrierDto } from 'apps/order-api/src/dtos/order-carrier.dto';
import { UpdateOrderDto } from 'apps/order-api/src/dtos/update-order.dto';
import {
  CallCenterExtensionsDeleteDto,
  CreateCallCenterExtensionDto,
  DeActiveCallCenterDto,
  ReturnCallCenterExtensionsDto,
  UpdateCallCenterExtensionDto,
  YcallRequestWebhookDto,
} from 'apps/order-api/src/dtos/callcenter.dto';

import { AppointmentSchedule } from 'apps/order-api/src/entities/appointment-schedule.entity';
import { CareReason } from 'apps/order-api/src/entities/care-reason.entity';
import { DuplicateLead } from 'apps/order-api/src/entities/duplicate-lead.entity';
import { FilterCollection } from 'apps/order-api/src/entities/filter-collection.entity';
import { LandingPage } from 'apps/order-api/src/entities/landing-page.entity';
import { LeadCareItem } from 'apps/order-api/src/entities/lead-care-item.entity';
import { LeadCare } from 'apps/order-api/src/entities/lead-care.entity';
import { Lead } from 'apps/order-api/src/entities/lead.entity';
import { OrderSource } from 'apps/order-api/src/entities/order-source.entity';
import { Order } from 'apps/order-api/src/entities/order.entity';
import { ShiftUser } from 'apps/order-api/src/entities/shift-user.entity';
import { SystemLog } from 'apps/order-api/src/entities/system-log.entity';
import { CareState, CareStateAfterSales } from 'apps/order-api/src/enums/care-state.enum';
import { LeadCollectType } from 'apps/order-api/src/enums/lead-collect-type.enum';
import { LeadConfigType } from 'apps/order-api/src/enums/lead-config-type.enum';
import { LeadDistributeCondition } from 'apps/order-api/src/enums/lead-distribute-condition.enum';
import { LeadDistributeRuleType } from 'apps/order-api/src/enums/lead-distribute-rule-type.enum';
import { SourceEntity } from 'apps/order-api/src/enums/source-entity.enum';
import {
  GatherLeadsFilter,
  LeadsFilter,
  MobileLeadsFilter,
} from 'apps/order-api/src/filters/leads.filter';
import { TagMethodType, TagOperatorType, TagStatus } from 'apps/order-api/src/enums/tag.enum';
import { ManualDistributeLeadsFilter } from 'apps/order-api/src/filters/manual-distribute-leads.filter';
import { User } from 'apps/order-api/src/read-entities/identity/user.entity';
import { LeadDistributeRuleValue } from 'apps/order-api/src/read-entities/order/lead-distribute-rule-value.entity';
import { CallCenter, CallCenterExtension, CallHistory } from 'apps/order-api/src/entities';

import axios from 'axios';
import { Queue } from 'bullmq';
import { instanceToPlain, plainToInstance } from 'class-transformer';
import { orderConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { AmountType } from 'core/enums/amount-type.enum';
import { OrderStatus } from 'core/enums/order-status.enum';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { AuthUser } from 'core/interfaces/auth-user.interface';
import { RawResponse } from 'core/raw/raw-response';
import * as FormData from 'form-data';
import { Redis } from 'ioredis';
import { chunk, flatMap, intersection, isEmpty, isNil, omit, reduce, remove } from 'lodash';
import { Moment } from 'moment';
import * as moment from 'moment-timezone';
import xlsx from 'node-xlsx';
import {
  Brackets,
  FindConditions,
  getConnection,
  In,
  IsNull,
  Not,
  Repository,
  SelectQueryBuilder,
  TreeRepository,
  UpdateResult,
} from 'typeorm';
import { v4 as uuid } from 'uuid';
import { PANCAKE_API_ENDPOINT } from '../../../../../facebook-bot/src/constants/fb-api-endpoints.constant';
import { ICdrWebhook, PortSipClient } from '../../../clients/port-sip.client';
import { Cdr } from '../../../entities/cdr.entity';
import { RecordFile } from '../../../entities/record-file.entity';
import { ShiftsService } from '../../shift/services/shifts.service';
import { LandingPagesService } from './landing-pages.service';
import { LeadDistributeConfigsService } from './lead-distribute-configs.service';
import { OrdersService } from './orders.service';
import { TagsService } from './tags.service';
import { LeadPerformanceByUserIdsDto } from 'apps/order-api/src/dtos/lead-performance-by-user-ids.dto';
import FilterUtils from 'core/utils/FilterUtils';
import { CONFIRMED_STATES } from 'apps/analytics-api/src/constants/orders/lead-states.constant';
import {
  ACTUAL_REVENUE_STATUSES,
  RETURNED_SALES_STATUSES,
} from 'apps/analytics-api/src/constants/orders/order-statuses.constant';
import { DataAccessLevel } from 'apps/identity-api/src/enums/data-access-level.enum';
import { ModuleInCharge } from 'apps/identity-api/src/enums/module-in-charge.enum';
import { CallCenterExtensionsFilter } from 'apps/order-api/src/filters/call-center-extensions.filter';
import { CALLCENTER } from 'core/cache/constants/prefix.constant';
import {
  CallHistoryStatus,
  CallHistoryType,
  convertCallHistoryStatusToEnum,
} from 'apps/order-api/src/enums/call-center.enum';
import SignatureUtils from 'core/utils/SignatureUtils';
import { ErrorCode } from 'apps/order-api/src/enums/error-code.enum';
import ExcelUtils from 'core/utils/ExcelUtils';
import Utils from 'core/utils/Utils';
import { AwsUtils } from 'core/utils/AwsUtils';
import { QUEUE_SETTING } from 'core/constants/constants';
import { Job } from 'bull';
import { JobDataCallCenter } from 'apps/order-api/src/modules/order/processors/callcenter.processor';
import { Sort } from 'core/enums/sort.enum';
import { RemovedDuplicateLeads } from 'apps/order-api/src/entities/removed-duplicate-leads.entity';
import StringUtils from 'core/utils/StringUtils';
import { PermissionUtils } from 'core/utils/PermissionUtils';
import { SalePermission } from 'core/enums/sale-permissions';
import { TelesalesPermission } from 'core/enums/sale-permissions/telesales-permission.enum';
import { PossibleDuplicateOrder } from 'apps/order-api/src/entities/possible-duplicate-order.entity';
import { LeadASDistributeConfigsService } from './lead-as-distribute-configs.service';
import { LeadASDistributeRuleType } from 'apps/order-api/src/enums/lead-as-distribute-rule-type.enum';
import { LeadASDistributeCondition } from 'apps/order-api/src/enums/lead-as-distribute-condition.enum';
import { LeadType } from 'apps/order-api/src/enums/lead-type.enum';
import { LeadsASService } from './leads-as.service';
import { RemovedDuplicateOrders } from 'apps/order-api/src/entities/removed-duplicate-orders.entity';
import OneSignalUtils from 'core/utils/OneSignalUtils';
import { AfterSalesLeadPermission } from 'core/enums/sale-permissions/after-sales-lead.permission.enum';
import { FilterCollectionType } from 'core/enums/filter-collection-type.enum';
import { ReasonTab } from 'apps/order-api/src/enums/lead-aftersales-filter.enum';

export class LeadsService implements OnModuleInit {
  private readonly logger = new Logger(LeadsService.name);

  constructor(
    @InjectRepository(Lead, orderConnection)
    private leadsRepo: Repository<Lead>,
    @InjectRepository(RecordFile, orderConnection)
    private recordFileRepository: Repository<RecordFile>,
    @InjectRepository(LeadCare, orderConnection)
    private leadCaresRepo: Repository<LeadCare>,
    @InjectRepository(LeadCareItem, orderConnection)
    private leadCareItemsRepo: Repository<LeadCareItem>,
    @InjectRepository(DuplicateLead, orderConnection)
    private duplicateLeadsRepo: Repository<DuplicateLead>,
    @InjectRepository(CareReason, orderConnection)
    private careReasonRepo: Repository<CareReason>,
    @InjectRepository(Order, orderConnection)
    private orderRepo: Repository<Order>,
    @InjectRepository(AppointmentSchedule, orderConnection)
    private appointmentScheduleRepo: Repository<AppointmentSchedule>,
    @InjectRepository(FilterCollection, orderConnection)
    private filterRepo: Repository<FilterCollection>,
    @InjectRepository(SystemLog, orderConnection)
    private logsRepo: Repository<SystemLog>,
    @InjectRepository(OrderSource, orderConnection)
    private externalSourceRepo: TreeRepository<OrderSource>,
    @InjectRepository(Cdr, orderConnection)
    private cdrRepository: Repository<Cdr>,
    @InjectRepository(CallHistory, orderConnection)
    private callHistoryRepo: Repository<CallHistory>,
    @InjectRepository(CallCenter, orderConnection)
    private callCenterRepo: Repository<CallCenter>,
    @InjectRepository(CallCenterExtension, orderConnection)
    private callCenterExtensionRepo: Repository<CallCenterExtension>,
    private landingService: LandingPagesService,
    private ordersService: OrdersService,
    private distributeConfigsService: LeadDistributeConfigsService,
    private leadAfterSaleDistributeConfigsService: LeadASDistributeConfigsService,
    private shiftsService: ShiftsService,
    private tagsService: TagsService,
    private leadAfterSaleService: LeadsASService,
    private amqpConnection: AmqpConnection,
    @InjectQueue('order')
    private orderQueue: Queue,
    @InjectQueue('callcenter')
    private callCenterQueue: Queue,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async onModuleInit() {
    // if (process.env.CONSUMER == 'false') {
    await this.initCallCenterExts();
    // }
  }

  async initCallCenterExts() {
    const extensions = await this.callCenterExtensionRepo.find({
      where: { isAvailable: true },
      order: {
        createdAt: 'ASC',
      },
    });

    const grouped: Record<string, number[]> = extensions.reduce((acc, extension) => {
      const key = `-${CALLCENTER}-${extension.callCenterId}-${extension.countryId}-${extension.companyId}`;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(extension.id);
      return acc;
    }, {} as Record<string, number[]>);

    for (const [key, ids] of Object.entries(grouped)) {
      await this.redis.del(key);
      await this.redis.rpush(key, ...ids);
    }
  }

  async getExt(ext: string) {
    const key = `running-ext-${ext}`;
    const running = await this.redis.get(key);
    if (!running) {
      await this.redis.set(key, 'pong', 'EX', 5);
      return 'got';
    }
    return 'failed';
  }

  async pingCall(ext: string) {
    const key = `running-ext-${ext}`;
    const running = await this.redis.get(key);
    if (!running) {
      await this.redis.set(key, 'pong', 'EX', 5);
      return 'failed';
    }
    await this.redis.expire(key, 5);
    return 'pong';
  }

  async getRecordFile(id: string) {
    const file = await this.recordFileRepository.findOne({
      id,
    });
    return file?.src;
  }

  async saveRecordFile({ id, src }: { id: string; src: string }) {
    return this.recordFileRepository.save({
      id,
      src,
    });
  }

  async countUserLeads(
    filter: LeadsFilter,
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const countryId = headers['country-ids'];
    const companyId = request?.user?.companyId;
    const { userIds, projectIds } = filter;

    const qb = this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('orders', 'o', 'o.id = l.order_id')
      .where('o.company_id = :companyId', { companyId })
      .andWhere('l.state IN (:...states)', { states: LEAD_STATES_CAN_BE_CONSIDERED_AS_ASSIGNED })
      .andWhere('o.country_id = :countryId', { countryId })
      .andWhere('(l.lead_type = :leadType OR l.lead_type IS NULL)', {
        leadType: LeadType.normal,
      })
      .select([
        'l.user_id',
        'COUNT(DISTINCT o.customer_phone) AS count',
        "STRING_AGG(DISTINCT o.customer_phone, ',') AS phones",
      ]) // Count distinct customer_phone
      .groupBy('l.user_id'); // Group by user_id

    if (!isEmpty(userIds)) qb.andWhere('l.user_id IN (:...userIds)', { userIds });
    if (!isEmpty(projectIds)) qb.andWhere('o.project_id IN (:...projectIds)', { projectIds });

    return qb.execute();
  }

  async createLead(
    body: CreateLeadDto,
    headers: Record<string, string>,
    request: Record<string, any>,
  ): Promise<Lead> {
    const countryId = headers['country-ids'];
    const companyId = request?.user?.companyId;
    const userId = request?.user?.id;
    const projectId = Number(headers['project-ids']);

    let leadDup;
    if (body.isCreateDuplicate && body.leadIdDuplicate) {
      leadDup = await this.leadsRepo.findOne({ id: body.leadIdDuplicate });
      if (!leadDup) throw new NotFoundException('Lead Duplicate Not Found');
    }

    if (body.collectType === 'convertFromFacebook') {
      body.customerFbScopedUserId = `${body.sourceId}_${StringUtils.generateSecureUniqueString()}`;
    }

    const order = await this.ordersService.createOrder(
      body,
      Number(countryId),
      companyId,
      userId,
      projectId,
    );

    const lead = plainToInstance(Lead, { orderId: order.id, createdAt: order.createdAt });
    switch (body.collectType) {
      case 'convertFromFacebook':
      case 'manualKeying':
        lead.formCapturedAt = null;
        break;
      case 'captureForm':
        lead.formCapturedAt = order.createdAt;
        break;
      default:
        break;
    }

    await this.leadsRepo.upsert(lead, ['orderId']);

    // Logic for duplicate lead
    if (body.isCreateDuplicate && body.leadIdDuplicate) {
      const [updatedLead] = await Promise.all([
        // insert logs activitiy TLS-00009
        this.leadsRepo.findOne({
          where: { orderId: lead.orderId },
          select: ['id'], // Select only the `id` if needed
        }),
        // this.shiftsService.getUserCurrentShiftByUserId({ userId: leadDup.userId }),
      ]);

      const orderDup = await this.leadsRepo
        .createQueryBuilder('leads')
        .where('leads.id = :id', { id: body.leadIdDuplicate })
        .leftJoin('orders', 'orders', 'orders.id = leads.orderId')
        .select('orders.displayId', 'displayId')
        .getRawOne();

      const logs = [
        plainToInstance(SystemLog, {
          tableName: 'leads',
          action: 'DUPLICATE',
          parentId: body.leadIdDuplicate,
          recordId: updatedLead.id,
          changes: ['lead_id', body.leadIdDuplicate, orderDup?.displayId],
          creatorId: userId,
        }),
        plainToInstance(SystemLog, {
          tableName: 'orders',
          action: 'UPDATE',
          recordId: updatedLead.id,
          changes: ['sale_id', leadDup.userId],
          beforeChanges: ['sale_id', null],
        }),
      ];
      await this.logsRepo.save(logs);
    }
    return lead;
  }

  async unDuplicateData(
    id: number,
    filter: LeadsFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const { ids } = filter;
    const userId = request?.user?.id;
    if (!ids || ids.length === 0) throw new BadRequestException('ids is required');
    const targetId = ids[0];

    const redisKey = `unduplicate-ag-sale-lead.${id}-${targetId}`;
    const [, [, isUpdating]] = await this.redis
      .multi()
      .set(redisKey, 0, 'EX', 20, 'NX')
      .incr(redisKey)
      .exec();
    if (isUpdating > 1)
      throw new BadRequestException(
        { isUpdating },
        `unDuplicateData() This lead is updating by another request. Please try again later.`,
      );

    const [oldRecord, leadOrigin, leadTarget] = await Promise.all([
      this.leadsRepo
        .createQueryBuilder('leads')
        .where('leads.id = :id', { id })
        .leftJoin('leads.duplicateLeads', 'dupLeads')
        .addSelect(['dupLeads.duplicateLeadId'])
        .leftJoin('leads.currentCare', 'currentCare')
        .addSelect([
          'currentCare.id',
          'currentCare.createdAt',
          'currentCare.updatedAt',
          'currentCare.userId',
        ])
        .getOne(),
      this.leadsRepo
        .createQueryBuilder('leads')
        .where('leads.id = :id', { id })
        .leftJoin('orders', 'orders', 'orders.id = leads.order_id')
        .select(['orders.displayId as display_id', 'orders.id as order_id'])
        .getRawOne(),
      this.leadsRepo
        .createQueryBuilder('leads')
        .where('leads.id = :id', { id: targetId })
        .leftJoin('orders', 'orders', 'orders.id = leads.order_id')
        .leftJoin('leads.currentCare', 'currentCare')
        .select([
          'orders.displayId as display_id',
          'orders.id as order_id',
          'currentCare.id as current_care_id',
          'currentCare.userId as current_care_user_id',
          'leads.leadType as lead_type',
          'leads.state as lead_state',
          'leads.id as id',
        ])
        .getRawOne(),
    ]);

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      // add logs TLS-00007
      const log1 = plainToInstance(SystemLog, {
        tableName: 'leads',
        action: 'REMOVE_DUPLICATE',
        recordId: id,
        changes: [
          'lead_id',
          id,
          leadOrigin?.display_id,
          'from_id',
          targetId,
          leadTarget?.display_id,
          userId,
        ],
        creatorId: userId,
      });
      const log2 = plainToInstance(SystemLog, {
        tableName: 'leads',
        action: 'REMOVE_DUPLICATE',
        recordId: targetId,
        changes: [
          'lead_id',
          targetId,
          leadTarget?.display_id,
          'from_id',
          id,
          leadOrigin?.display_id,
          userId,
        ],
        creatorId: userId,
      });

      const removedLead1 = plainToInstance(RemovedDuplicateLeads, {
        tableName: 'removed_duplicate_leads',
        action: 'CREATE',
        leadId: id,
        duplicateLeadId: targetId,
      });
      const removedLead2 = plainToInstance(RemovedDuplicateLeads, {
        tableName: 'removed_duplicate_leads',
        action: 'CREATE',
        leadId: targetId,
        duplicateLeadId: id,
      });

      await Promise.all([
        queryRunner.manager
          .createQueryBuilder(DuplicateLead, 'dl')
          .delete()
          .where(`leadId = :targetId and duplicateLeadId = :id`, { targetId, id })
          .orWhere(`leadId = :id and duplicateLeadId = :targetId`, { id, targetId })
          .execute(),
        queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(SystemLog)
          .values([log1, log2])
          .execute(),
        queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(RemovedDuplicateLeads)
          .values([removedLead1, removedLead2])
          .execute(),
        // queryRunner.manager
        //   .createQueryBuilder()
        //   .insert()
        //   .into(RemovedDuplicateOrders)
        //   .values([
        //     {
        //       orderId: leadOrigin?.order_id,
        //       duplicateOrderId: leadTarget?.order_id,
        //     },
        //     {
        //       orderId: leadTarget?.order_id,
        //       duplicateOrderId: leadOrigin?.order_id,
        //     },
        //   ])
        //   .execute(),
      ]);
      // gán lại shift nếu đang trong ca
      if (
        leadTarget &&
        leadTarget?.current_care_id &&
        leadTarget?.lead_type !== LeadType.after_sale
      ) {
        const shifts = await this.shiftsService.getUserCurrentShiftByUserId({
          userId: leadTarget?.current_care_user_id,
        });
        const shift = shifts?.[0];
        if (shift) {
          await queryRunner.manager
            .getRepository(LeadCare)
            .update({ id: leadTarget?.current_care_id }, { shiftId: shift.id });
        }
      }
      // check case original lead is alone before unDuplicate lead target
      const dupLeadsOfOriginalLead = await queryRunner.manager
        .getRepository(DuplicateLead)
        .createQueryBuilder('dl')
        .where('dl.leadId = :id', { id })
        .orWhere('dl.duplicateLeadId = :id', { id })
        .getCount();
      if (oldRecord.currentCareId && oldRecord.leadType !== LeadType.after_sale) {
        if (dupLeadsOfOriginalLead === 0) {
          const shifts = await this.shiftsService.getUserCurrentShiftByUserId({
            userId: oldRecord.currentCare.userId,
          });
          const shift = shifts?.[0];
          if (shift) {
            await queryRunner.manager
              .getRepository(LeadCare)
              .update({ id: oldRecord.currentCareId }, { shiftId: shift.id });
          }
        }
      }

      await queryRunner.commitTransaction();
      // bắn socket gỡ trùng danh sách lead
      if (!isEmpty(oldRecord.duplicateLeads)) {
        for (const duplicate of oldRecord.duplicateLeads) {
          await this.amqpConnection.publish('message-service', 'after-lead-updated', {
            leadId: duplicate.duplicateLeadId,
          });
        }
      }
      await this.amqpConnection.publish('message-service', 'after-lead-updated', {
        leadId: id,
      });
      const unDuplicateTime = new Date();
      // add job gather for lead unDuplicated, need action before commit transaction cause queue
      if (
        leadTarget &&
        leadTarget?.current_care_id &&
        leadTarget?.lead_type !== LeadType.after_sale
      ) {
        await this.amqpConnection.publish('order-service', 'schedule-gather-lead', {
          leadId: leadTarget?.id,
          state: leadTarget?.lead_state,
          updatedAt: unDuplicateTime,
          currentCareId: leadTarget?.current_care_id,
        });
      }
      // check case original lead is alone before unDuplicate lead target
      if (
        oldRecord.currentCareId &&
        dupLeadsOfOriginalLead === 0 &&
        oldRecord.leadType !== LeadType.after_sale
      ) {
        await this.amqpConnection.publish('order-service', 'schedule-gather-lead', {
          leadId: oldRecord.id,
          state: oldRecord.state,
          updatedAt: unDuplicateTime,
          currentCareId: oldRecord.currentCareId,
        });
      }
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
      throw error;
    } finally {
      await queryRunner.release();
      await this.redis.del(redisKey);
    }

    return true;
  }

  async updateLead(
    id: number,
    body: UpdateLeadDto,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<Lead> {
    const redisKey = `updating-ag-sale-lead.${id}`;
    const [, [, isUpdating]] = await this.redis
      .multi()
      .set(redisKey, 0, 'EX', 20, 'NX')
      .incr(redisKey)
      .exec();
    if (isUpdating > 1)
      throw new BadRequestException(
        { isUpdating },
        `This lead is updating by another request. Please try again later.`,
      );

    const oldRecord = await this.leadsRepo
      .createQueryBuilder('leads')
      .where('leads.id =:id', { id })
      .leftJoin('leads.duplicateLeads', 'dupLeads')
      .addSelect(['dupLeads.duplicateLeadId'])
      .getOne();

    if (!oldRecord) throw new NotFoundException();
    const updateLeadData: Partial<Lead> = {};
    const updateOrderData = new UpdateOrderDto();
    if (
      body.ignoreDuplicateWarning &&
      oldRecord.ignoreDuplicateWarning !== body.ignoreDuplicateWarning
    ) {
      updateLeadData.ignoreDuplicateWarning = true;
      updateOrderData.ignoreDuplicateWarning = true;
    }

    switch (body.collectType) {
      case 'manualKeying':
        updateLeadData.formCapturedAt = null;
        break;
      case 'captureForm':
        updateLeadData.formCapturedAt = oldRecord.createdAt;
        break;
      default:
        break;
    }

    if (!isEmpty(updateOrderData))
      await this.ordersService.updateOrder(oldRecord.orderId, updateOrderData, headers, request);

    if (isEmpty(updateLeadData)) return oldRecord;

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const result = await queryRunner.manager
        .createQueryBuilder(Lead, 'l')
        .update()
        .set({ ...updateLeadData, updatedBy: request.user.id })
        .where({ id })
        .returning(['updatedAt'])
        .execute();
      console.log(`update lead ${id} result`, result, updateLeadData);

      await queryRunner.commitTransaction();
      if (result.affected) {
        if (body.ignoreDuplicateWarning) {
          await this.amqpConnection.publish('order-service', 'ignore-duplicate-leads-warning', {
            orderId: oldRecord.orderId,
            updatedBy: request.user.id,
            updatedAt: result.raw?.[0]?.updated_at,
          });
        }

        const updatedLead = plainToInstance(Lead, { id, ...body, updatedBy: request.user.id });
        // bắn socket gỡ trùng danh sách lead
        if (!isEmpty(oldRecord.duplicateLeads)) {
          for (const duplicate of oldRecord.duplicateLeads) {
            await this.amqpConnection.publish('message-service', 'after-lead-updated', {
              leadId: duplicate.duplicateLeadId,
              updatedAt: result.raw?.[0]?.updated_at,
            });
          }
        }
        return updatedLead;
      }
      return oldRecord;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
      throw error;
    } finally {
      await queryRunner.release();
      await this.redis.del(redisKey);
    }
  }

  async countLeads2(
    filter: LeadsFilter,
    groupBy: string[] = [],
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const countryIds = headers?.['country-ids'] ? headers?.['country-ids'].split(',') : [];
    const companyId = request?.user?.companyId;

    const subQb = await this.getLeadsQueryBuilder(filter, undefined, countryIds, companyId);
    subQb.leftJoin(DuplicateLead, 'dupLeads', 'lead.id = dupLeads.lead_id');
    if (isNil(subQb)) return [];
    subQb.leftJoin('order.tags', 'tags', `tags.status = ${TagStatus.active}`);
    if (filter.tagIds) {
      const subQbTag = getConnection(orderConnection)
        .createQueryBuilder()
        .from('order_tags', 'ot')
        .select('ot.order_id', 'order_id')
        .where('ot.tag_id IN (:...tagIds)', { tagIds: filter.tagIds })
        .groupBy('ot.order_id');
      if (filter.tagMethod == TagMethodType.Include) {
        if (filter.operator == TagOperatorType.And) {
          subQbTag.having(`COUNT(DISTINCT ot.tag_id) = ${filter.tagIds?.length}`);
          subQb
            .andWhere(`lead.orderId IN (${subQbTag.getQuery()})`)
            .setParameters(subQbTag.getParameters());
        }
        if (filter.operator == TagOperatorType.Or) {
          subQb.andWhere('order_tags.tag_id IN (:...tagIds)', {
            tagIds: filter.tagIds,
          });
        }
      }

      if (filter.tagMethod == TagMethodType.Exclude) {
        if (filter.operator == TagOperatorType.And) {
          subQbTag.having(`COUNT(DISTINCT ot.tag_id) = ${filter.tagIds?.length}`);
          subQb
            .andWhere(`lead.orderId NOT IN (${subQbTag.getQuery()})`)
            .setParameters(subQbTag.getParameters());
        }
        if (filter.operator == TagOperatorType.Or) {
          subQb
            .andWhere(`lead.orderId NOT IN (${subQbTag.getQuery()})`)
            .setParameters(subQbTag.getParameters());
        }
      }
    }

    if (filter.fromCurrentCare) {
      subQb.leftJoin('lead.currentCare', 'currentCare');
      if (filter.fromCurrentCare) {
        subQb.andWhere('currentCare.created_at >= :fromCurrentCare', {
          fromCurrentCare: filter.fromCurrentCare,
        });
      }

      if (filter.toCurrentCare) {
        subQb.andWhere('currentCare.created_at <= :toCurrentCare', {
          toCurrentCare: filter.toCurrentCare,
        });
      }
    }

    subQb.select('COUNT(DISTINCT lead.id)', 'count');
    for (const group of groupBy) {
      subQb.addGroupBy(`lead.${group}`);
      subQb.addSelect(`lead.${group}`, group);
    }

    const data = await subQb.getRawMany();

    for (const item of data) {
      if (!isNil(item.state)) {
        item.state = CareState[item.state];
      }
      item.count = Number(item.count);
    }
    return data;
  }

  async countLeads(
    filter: LeadsFilter,
    groupBy: string[] = [],
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    if (groupBy.length > 0) return await this.countLeads2(filter, groupBy, headers, request);

    const countryIds = headers?.['country-ids'] ? headers?.['country-ids'].split(',') : [];
    const companyId = request?.user?.companyId;

    const subQb = filter.isDistributable
      ? await this.getLeadsToDistributeQueryBuilderV2(filter, countryIds, companyId)
      : await this.getLeadsQueryBuilder(filter, undefined, countryIds, companyId);

    if (isNil(subQb)) return [];
    subQb.leftJoin(DuplicateLead, 'dupLeads', 'lead.id = dupLeads.lead_id');
    subQb.leftJoin('order.tags', 'tags', `tags.status = ${TagStatus.active}`);
    if (filter.tagIds) {
      const subQbTag = getConnection(orderConnection)
        .createQueryBuilder()
        .from('order_tags', 'ot')
        .select('ot.order_id', 'order_id')
        .where('ot.tag_id IN (:...tagIds)', { tagIds: filter.tagIds })
        .groupBy('ot.order_id');
      if (filter.tagMethod == TagMethodType.Include) {
        if (filter.operator == TagOperatorType.And) {
          subQbTag.having(`COUNT(DISTINCT ot.tag_id) = ${filter.tagIds?.length}`);
          subQb
            .andWhere(`lead.orderId IN (${subQbTag.getQuery()})`)
            .setParameters(subQbTag.getParameters());
        }
        if (filter.operator == TagOperatorType.Or) {
          subQb.andWhere('order_tags.tag_id IN (:...tagIds)', {
            tagIds: filter.tagIds,
          });
        }
      }

      if (filter.tagMethod == TagMethodType.Exclude) {
        if (filter.operator == TagOperatorType.And) {
          subQbTag.having(`COUNT(DISTINCT ot.tag_id) = ${filter.tagIds?.length}`);
          subQb
            .andWhere(`lead.orderId NOT IN (${subQbTag.getQuery()})`)
            .setParameters(subQbTag.getParameters());
        }
        if (filter.operator == TagOperatorType.Or) {
          subQb
            .andWhere(`lead.orderId NOT IN (${subQbTag.getQuery()})`)
            .setParameters(subQbTag.getParameters());
        }
      }
    }

    if (filter.fromCurrentCare) {
      subQb.andWhere('currentCare.created_at >= :fromCurrentCare', {
        fromCurrentCare: filter.fromCurrentCare,
      });
    }

    if (filter.toCurrentCare) {
      subQb.andWhere('currentCare.created_at <= :toCurrentCare', {
        toCurrentCare: filter.toCurrentCare,
      });
    }

    subQb
      .distinctOn(['order.id'])
      .select([
        'order.id AS order_id',
        'order.total_price AS total_price',
        'order.surcharge AS surcharge',
        'order.shipping_fee AS shipping_fee',
        'order.discount AS discount',
      ])
      .orderBy('order.id', 'DESC');

    const qb = this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('(' + subQb.getQuery() + ')', 'sub', 'l.order_id = sub.order_id')
      .andWhere('(l.lead_type = :leadType OR l.lead_type IS NULL)', {
        leadType: LeadType.normal,
      })
      .select('COUNT(DISTINCT l.id)', 'count')
      .addSelect(
        'SUM(sub.total_price + COALESCE(sub.surcharge, 0) + COALESCE(sub.shipping_fee, 0) - COALESCE(sub.discount, 0))',
        'revenue',
      )
      .setParameters(subQb.getParameters());
    for (const group of groupBy) {
      qb.addGroupBy(`l.${group}`);
      qb.addSelect(`l.${group}`, group);
    }
    const data = await qb.getRawMany();
    for (const item of data) {
      if (!isNil(item.state)) {
        item.state = CareState[item.state];
      }
      item.revenue = Number(item.revenue);
      item.count = Number(item.count);
    }
    return data;
  }

  async mobileCountLeads(
    filter: MobileLeadsFilter,
    groupBy: string[] = [],
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const countryIds = headers?.['country-ids'] ? headers?.['country-ids'].split(',') : [];
    const companyId = request?.user?.companyId;

    const subQb = filter.isDistributable
      ? await this.getLeadsToDistributeQueryBuilderV2(filter, countryIds, companyId)
      : await this.getLeadsQueryBuilder(filter, undefined, countryIds, companyId);

    if (isNil(subQb)) return [];

    if (filter.productIds) {
      subQb.leftJoinAndSelect('order.products', 'products');
      subQb.andWhere('products.id IN (:...productIds)', { productIds: filter.productIds });
    }

    if (filter.tagIds) {
      subQb
        .leftJoin('order_tags', 'order_tags', `order_tags.order_id = order.id`)
        .andWhere('order_tags.tag_id IN (:...tagIds)', {
          tagIds: filter.tagIds,
        });
    }

    subQb
      .distinctOn(['order.id'])
      .select([
        'order.id AS order_id',
        'order.total_price AS total_price',
        'order.surcharge AS surcharge',
        'order.shipping_fee AS shipping_fee',
        'order.discount AS discount',
      ])
      .orderBy('order.id', 'DESC');

    const qb = this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('(' + subQb.getQuery() + ')', 'sub', 'l.order_id = sub.order_id')
      .select('COUNT(DISTINCT l.id)', 'count')
      .addSelect(
        'SUM(sub.total_price + COALESCE(sub.surcharge, 0) + COALESCE(sub.shipping_fee, 0) - COALESCE(sub.discount, 0))',
        'revenue',
      )
      .setParameters(subQb.getParameters());

    for (const group of groupBy) {
      qb.addGroupBy(`l.${group}`);
      qb.addSelect(`l.${group}`, group);
    }
    const data = await qb.getRawMany();
    for (const item of data) {
      if (!isNil(item.state)) {
        item.state = CareState[item.state];
      }
      item.revenue = Number(item.revenue);
      item.count = Number(item.count);
    }
    return data;
  }

  async getLeadsToDistributeQueryBuilder(
    filter: LeadsFilter,
    countryIds: (number | string)[],
    companyId: number,
  ): Promise<SelectQueryBuilder<Lead> | null> {
    const connection = getConnection(orderConnection);
    const subQb = connection
      .createQueryBuilder()
      .select(
        'DISTINCT (CASE WHEN pdl.lead_id < pdl.min_dup_lead_id THEN pdl.lead_id ELSE pdl.min_dup_lead_id END)',
        'origin_ids',
      )
      .from(subQuery => {
        return subQuery
          .select('lead_id')
          .addSelect('MIN(duplicate_lead_id)', 'min_dup_lead_id')
          .from('duplicate_leads', 'pdl')
          .groupBy('lead_id');
      }, 'pdl');
    const qb = await this.getLeadsQueryBuilder(
      {
        ...filter,
        state: isEmpty(filter.state) ? [CareState.new, CareState.unassign_attempted] : filter.state,
      },
      null,
      countryIds,
      companyId,
    );

    return isNil(qb)
      ? qb
      : qb
          .leftJoin('lead.duplicateLeads', 'pdl')
          .andWhere(
            new Brackets(subQuery => {
              subQuery.where('pdl.id IS NULL');
              subQuery.orWhere('lead.id IN (' + subQb.getSql() + ')');
            }),
          )
          .select(['lead.id', 'lead.state'])
          .orderBy('lead.id', 'ASC');
  }

  async getLeadsToDistributeDuplicateIds() {
    const connection = getConnection(orderConnection);
    const data = await connection
      .createQueryBuilder()
      .select('DISTINCT a.lead_id', 'originId')
      .addSelect("string_agg(a.duplicate_lead_id::text, ',')", 'duplicates')
      .from('duplicate_leads', 'a')
      .leftJoin('leads', 'l1', 'a.lead_id = l1.id')
      .where('l1.user_id IS NULL')
      .andWhere(
        `NOT EXISTS (
      SELECT 1
      FROM duplicate_leads b
      JOIN leads l2 ON b.lead_id = l2.id
      WHERE b.duplicate_lead_id = a.lead_id
      AND l2.user_id IS NULL
      AND b.lead_id < a.lead_id
    )`,
      )
      .orderBy('a.lead_id', 'DESC')
      .groupBy('a.lead_id')
      .execute();

    return data.map(item => {
      return {
        leadId: item.originId,
        duplicates: item.duplicates.split(',').map(Number),
      };
    });
  }

  async getLeadsToDistributeQueryBuilderV2(
    filter: LeadsFilter,
    countryIds: (number | string)[],
    companyId: number,
    headers?: Record<string, string>,
  ): Promise<SelectQueryBuilder<Lead> | null> {
    let projectId;
    if (headers) projectId = Number(headers['project-ids']);

    if (!projectId && isEmpty(filter.projectIds)) return null;

    const connection = getConnection(orderConnection);
    const subQb = await connection.createQueryBuilder();
    if (isEmpty(filter?.ids)) {
      subQb
        .select('MIN(l.id)', 'originId')
        .from('duplicate_leads', 'dl')
        .innerJoin('leads', 'l', 'dl.lead_id = l.id');
    } else {
      subQb
        .select('MIN(l.id)', 'originId')
        .from('leads', 'l')
        .leftJoin('duplicate_leads', 'dl', 'dl.lead_id = l.id');
    }

    subQb
      .innerJoin('orders', 'o', 'o.id = l.order_id')
      .where('l.state IN (:...states)', { states: [CareState.new, CareState.unassign_attempted] })
      .andWhere('l.user_id IS NULL')
      .andWhere('o.company_id = :companyId', { companyId })
      .andWhere('o.customer_phone != :emptyPhone', { emptyPhone: '' })
      .groupBy('o.customer_phone')
      .addGroupBy('o.project_id');

    if (projectId) subQb.andWhere('o.project_id = :projectId', { projectId });
    if (!isEmpty(filter.projectIds))
      subQb.andWhere('o.project_id IN (:...projectIds)', { projectIds: filter.projectIds });
    if (!isEmpty(filter?.ids)) subQb.andWhere('l.id IN (:...ids)', { ids: filter.ids });
    if (filter?.from) subQb.andWhere('l.created_at >= :from', { from: filter.from });
    if (filter?.to) subQb.andWhere('l.created_at <= :to', { to: filter.to });

    const listIds = await subQb.execute();

    const qb = await this.getLeadsQueryBuilder(
      {
        ...filter,
        state: isEmpty(filter.state) ? [CareState.new, CareState.unassign_attempted] : filter.state,
      },
      null,
      countryIds,
      companyId,
    );
    if (filter.hasDuplicates) qb.leftJoin('lead.duplicateLeads', 'dupleads');

    return isNil(qb)
      ? qb
      : qb
          .leftJoin('lead.duplicateLeads', 'pdl')
          .andWhere(
            new Brackets(subQuery => {
              subQuery.where('pdl.id IS NULL');
              if (!isEmpty(listIds)) {
                subQuery.orWhere('lead.id IN (:...ids)', {
                  ids: listIds.map(item => item.originId),
                });
              }
            }),
          )
          .distinct(true)
          .select(['lead.id', 'lead.state', 'order.projectId'])
          .orderBy('lead.id', 'ASC');
  }

  async manualDistribute(
    filter: ManualDistributeLeadsFilter,
    body: ManualDistributeLeadsDto,
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const countryId = headers['country-ids']?.split(',')?.[0];
    let projectIds = headers['project-ids']?.split(',');
    if (!filter.projectIds || isEmpty(filter.projectIds))
      throw new BadRequestException(`ProjectIds required when manual distribute data.`);

    const companyId = request.user.companyId;
    const userId = request.user.id;

    // get projectIds of userrr
    let scopes = flatMap(request?.user?.profiles, p => p[4]);
    scopes = scopes.filter(s => s[0] == countryId);
    const projectIdsActive = scopes.map(s => String(s[1])); // get list Ids of project ['1', '2', '3', ...]
    projectIds = projectIdsActive.filter(item => projectIds.includes(item));

    filter.projectIds = projectIds.filter(item => filter.projectIds.includes(Number(item)));

    const { distributeType, specs } = body;
    const users: ShiftUser[] = await this.shiftsService.getWorkingUsers(
      { userIds: body.userIds },
      headers,
      request,
    );
    if (users.length === 0)
      throw new BadRequestException(`Không có nhân viên nào đang trong ca làm việc`);

    const { userIds, userShiftsLookup, userProjects } = users.reduce(
      (prev, user) => {
        if (!prev.userIds.includes(user.userId)) prev.userIds.push(user.userId);
        prev.userShiftsLookup[user.userId] = user.shiftId;
        prev.userProjects[user.userId] = user.projectIds;
        return prev;
      },
      { userIds: [], userShiftsLookup: {}, userProjects: {} },
    );

    // Get qualified leads to distribute
    const qb = await this.getLeadsToDistributeQueryBuilderV2(
      filter,
      [countryId],
      companyId,
      headers,
    );

    let leads = isNil(qb) ? [] : await qb.getMany();
    const totalLeads = leads.length;
    if (totalLeads === 0) throw new HttpException(`No data to distribute`, HttpStatus.NO_CONTENT);

    // Get distribute config
    const [config, procedureConfig] = await Promise.all([
      this.distributeConfigsService.getLeadDistributeConfig(headers, request),
      this.distributeConfigsService.getConfigByCompanyId(
        companyId,
        Number(countryId),
        LeadConfigType.processing_procedure,
      ),
    ]);
    if (!config) throw new BadRequestException(`Chưa thiết lập cấu hình định mức`);
    const assignedRule = procedureConfig?.rules?.find(
      r => r.type === LeadDistributeRuleType.assigned_leads,
    );
    const skipAssigned = (assignedRule?.value as LeadDistributeRuleValue[])?.find(
      v => v.condition === LeadDistributeCondition.changeCareStateToInProcessAutomatically,
    )?.value;

    const [usersLeadsCount, usersLeadsProcess]: [
      { userId: number; count: number }[],
      { userId: number; count: number }[],
    ] = await Promise.all([
      this.countLeads(
        { state: LEAD_STATES_CAN_BE_CONSIDERED_AS_ASSIGNED, userIds },
        ['userId'],
        headers,
        request,
      ),
      this.countLeads({ state: [CareState.no_attempt], userIds }, ['userId'], headers, request),
    ]);

    const usersLeadsCountLookup = usersLeadsCount.reduce((prev, next) => {
      prev[next.userId] = next.count;
      return prev;
    }, {});

    const usersLeadsCountNoAttempLookup = usersLeadsProcess.reduce((prev, next) => {
      prev[next.userId] = next.count;
      return prev;
    }, {});

    // add key for user has no leads
    userIds.forEach(userId => {
      if (!usersLeadsCountLookup[userId]) usersLeadsCountLookup[userId] = 0;
      if (!usersLeadsCountNoAttempLookup[userId]) usersLeadsCountNoAttempLookup[userId] = 0;
    });

    // Get maximum amount of data a user can have
    const maxAssignedLeadsRule = config.rules.find(
      rule => rule.type === LeadDistributeRuleType.assigned_leads,
    )?.value;
    const maxLeadToDistribute: number | undefined =
      maxAssignedLeadsRule.find(v => v.condition === LeadDistributeCondition.maxLeads)?.value ||
      Infinity;

    const maxProcessingData =
      config.rules
        .find(r => r.type === LeadDistributeRuleType.processing_leads)
        ?.value.find(v => v.condition === LeadDistributeCondition.maxLeads).value || Infinity;

    // Initial distribute leads by user object
    let distributedLeads: Record<string, Lead[]> = {};

    // Let's distribute
    if (isNil(distributeType)) {
      distributedLeads = this.distributeEqually(
        userIds,
        leads,
        usersLeadsCountLookup,
        maxLeadToDistribute,
        userProjects,
        usersLeadsCountNoAttempLookup,
        maxProcessingData,
        Boolean(skipAssigned),
      );
    } else {
      const maxLeads = skipAssigned ? maxProcessingData : maxLeadToDistribute;
      const leadsCountLookup = skipAssigned ? usersLeadsCountNoAttempLookup : usersLeadsCountLookup;

      switch (distributeType) {
        case AmountType.fixed: {
          for (const spec of specs) {
            const { userId, value } = spec;

            let remainingLeads = 0;
            if (skipAssigned) {
              const numCanReceive = maxLeadToDistribute - usersLeadsCountLookup[userId];
              const numProcessCanReceive =
                maxProcessingData - usersLeadsCountNoAttempLookup[userId];
              const realNumCanReceive = Math.min(numCanReceive, numProcessCanReceive);
              remainingLeads = Math.min(realNumCanReceive, value);
            } else {
              remainingLeads = Math.max(maxLeads - (leadsCountLookup[userId] || 0), 0);
            }
            const leadsToDistribute = Math.min(remainingLeads, value);
            const userProjectIds = userProjects[userId] || [];
            const userLeads = leads.filter(lead => userProjectIds.includes(lead?.order.projectId));
            distributedLeads[userId] = userLeads.splice(0, leadsToDistribute);
            leads = leads.filter(
              lead =>
                !distributedLeads[userId].some(distributedLead => distributedLead.id === lead.id),
            );
          }
          // Distribute remaining orders equally among the rest of the user IDs
          const remainingUsers = userIds.filter(userId => !distributedLeads[userId]);
          distributedLeads = {
            ...distributedLeads,
            ...this.distributeEqually(
              remainingUsers,
              leads,
              usersLeadsCountLookup,
              maxLeadToDistribute,
              userProjects,
              usersLeadsCountNoAttempLookup,
              maxProcessingData,
              Boolean(skipAssigned),
            ),
          };
          break;
        }
        case AmountType.percentage:
        default: {
          for (const spec of specs) {
            const { userId } = spec;
            const value = Math.ceil((totalLeads * spec.value) / 100);

            let remainingLeads = 0;
            if (skipAssigned) {
              const numCanReceive = maxLeadToDistribute - usersLeadsCountLookup[userId];
              const numProcessCanReceive =
                maxProcessingData - usersLeadsCountNoAttempLookup[userId];
              const realNumCanReceive = Math.min(numCanReceive, numProcessCanReceive);
              remainingLeads = Math.min(realNumCanReceive, value);
            } else {
              remainingLeads = Math.max(maxLeads - (leadsCountLookup[userId] || 0), 0);
            }
            const leadsToDistribute = Math.min(remainingLeads, value);
            const userProjectIds = userProjects[userId] || [];
            const userLeads = leads.filter(lead => userProjectIds.includes(lead?.order.projectId));
            distributedLeads[userId] = userLeads.splice(0, leadsToDistribute);
            leads = leads.filter(
              lead =>
                !distributedLeads[userId].some(distributedLead => distributedLead.id === lead.id),
            );
          }
          // Distribute remaining orders equally among the rest of the user IDs
          const remainingUsers = userIds.filter(userId => !distributedLeads[userId]);
          distributedLeads = {
            ...distributedLeads,
            ...this.distributeEqually(
              remainingUsers,
              leads,
              usersLeadsCountLookup,
              maxLeadToDistribute,
              userProjects,
              usersLeadsCountNoAttempLookup,
              maxProcessingData,
              Boolean(skipAssigned),
            ),
          };
          break;
        }
      }
    }

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const records = Object.keys(distributedLeads).reduce((prev: LeadCare[], key: string) => {
        const items = distributedLeads[key].map(lead => {
          return plainToInstance(LeadCare, {
            userId: key,
            leadId: lead.id,
            shiftId: userShiftsLookup[key],
            updatedBy: userId,
          });
        });
        prev.push(...items);
        return prev;
      }, []);
      const leadCares = await queryRunner.manager.save(records);
      const countLeadDistributedForEachUsers: Record<string, number> = {};
      const mLeads = leadCares.map(care => {
        if (countLeadDistributedForEachUsers[care.userId.toString()] === undefined) {
          countLeadDistributedForEachUsers[care.userId.toString()] = 1;
        } else {
          countLeadDistributedForEachUsers[care.userId.toString()] =
            countLeadDistributedForEachUsers[care.userId.toString()] + 1;
        }
        return plainToInstance(Lead, {
          id: care.leadId,
          userId: care.userId,
          state: skipAssigned ? CareState.no_attempt : CareState.assigned,
          currentCareId: care.id,
          lastCareId: care.id,
          updatedBy: userId,
          updatedAt: care.createdAt,
        });
      });
      await queryRunner.manager.save(mLeads);

      await queryRunner.commitTransaction();
      await Promise.all(
        mLeads.flatMap(it => [
          this.onLeadUpdated(it),
          this.amqpConnection.publish(
            'order-service',
            'assign-duplicate-leads-according-to-original-lead',
            { leadId: it.id, updatedBy: it.updatedBy, state: it.state, filter },
          ),
        ]),
      );

      await Promise.all([
        this.amqpConnection.publish(
          'message-service',
          'lead-distributed',
          countLeadDistributedForEachUsers,
        ),
        OneSignalUtils.pushNotification(
          'lead-distributed',
          Object.keys(countLeadDistributedForEachUsers).map(it => +it),
        ),
      ]);
      return leadCares;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  distributeEqually(
    userIds: number[],
    leads: Lead[],
    usersLeadsCount: Record<string, number>,
    maxLeadToDistribute: number,
    userProjects: Record<string, number[]>,
    usersLeadsCountNoAttempLookup: Record<string, number>,
    maxProcessingData?: number,
    skipAssigned?: boolean,
  ): Record<number, Lead[]> {
    if (isEmpty(userIds)) return {};
    // Sort user ids based on the number of orders they have already
    userIds.sort((a, b) => (usersLeadsCount[a] || 0) - (usersLeadsCount[b] || 0));
    // Calculate the maximum number of orders to distribute
    if (maxLeadToDistribute !== Infinity && maxProcessingData === Infinity)
      maxProcessingData = maxLeadToDistribute;

    const distributedLeads = {};
    const numUsers = userIds.length;
    const numLeads = leads.length;
    const leadsCanReceive = {};

    if (skipAssigned) {
      Object.keys(usersLeadsCountNoAttempLookup).forEach(userId => {
        if (maxLeadToDistribute - usersLeadsCount[userId] <= 0) return;
        const numCanReceive = maxLeadToDistribute - usersLeadsCount[userId];
        const numProcessCanReceive = maxProcessingData - usersLeadsCountNoAttempLookup[userId];
        leadsCanReceive[userId] = Math.min(numCanReceive, numProcessCanReceive);
      });
    } else {
      Object.keys(usersLeadsCount).forEach(userId => {
        const count = usersLeadsCount[userId] || 0;
        if (maxLeadToDistribute - count > 0) leadsCanReceive[userId] = maxLeadToDistribute - count;
      });
    }

    leadsLoop: for (let i = 0; i < numLeads; i++) {
      const lead = leads[i];
      const projectId = lead?.order?.projectId;

      for (let j = 0; j < numUsers; j++) {
        const userId = userIds[(i + j) % numUsers];
        if (!distributedLeads[userId]) distributedLeads[userId] = [];
        if (!leadsCanReceive[userId]) continue;

        // Check if user is eligible for the project
        if (userProjects && userProjects[userId]?.includes(projectId)) {
          // Check if user has reached max lead limit
          if (distributedLeads[userId].length < leadsCanReceive[userId]) {
            distributedLeads[userId].push(lead);
            continue leadsLoop;
          }
        }
      }
    }
    return distributedLeads;
  }

  nextStates() {
    return NEXT_CARE_STATES;
  }

  async getLeadsQueryBuilder(
    filter?: LeadsFilter,
    pagination?: PaginationOptions,
    countryIds?: (number | string)[],
    companyId?: number,
  ): Promise<SelectQueryBuilder<Lead> | null> {
    const qb = this.leadsRepo
      .createQueryBuilder('lead')
      .leftJoin('lead.order', 'order')
      .leftJoinAndSelect('order.products', 'products')
      .andWhere('(lead.lead_type = :leadType OR lead.lead_type IS NULL)', {
        leadType: LeadType.normal,
      });

    if (companyId) qb.andWhere('order.companyId = :companyId', { companyId });
    if (pagination) qb.take(pagination.limit).skip(pagination.skip);

    if (!isNil(filter.isCaptureForm)) {
      if (filter.isCaptureForm) filter.collectType = [LeadCollectType.captureForm];
      else filter.collectType = [LeadCollectType.manualKeying, LeadCollectType.convertFromFacebook];
    }

    const {
      from,
      to,
      fromLastCare,
      toLastCare,
      numberOfCares,
      fromNumberOfCares,
      toNumberOfCares,
      query,
      state,
      userIds,
      excludeUserIds,
      sourceType,
      sourceIds,
      reasonIds,
      tagIds,
      marketerIds,
      numberOfRepeats,
      ids,
      projectIds,
      collectType,
      hasDuplicates,
      isSearchLatestReason,
      fromUpdatedAt,
      toUpdatedAt,
      type,
      reasonTab,
      sourceTypes,
      productIds,
    } = filter;

    if (!isNil(type)) qb.andWhere('order.type = :type', { type });
    if (!isEmpty(countryIds)) qb.andWhere('order.countryId IN (:...countryIds)', { countryIds });
    if (ids) qb.andWhere('lead.id IN (:...ids)', { ids });
    if (from) qb.andWhere('lead.created_at >= :from', { from });
    if (to) qb.andWhere('lead.created_at <= :to', { to });
    if (fromUpdatedAt)
      qb.andWhere('lead.updated_at >= :fromUpdatedAt', { fromUpdatedAt: fromUpdatedAt });
    if (toUpdatedAt) qb.andWhere('lead.updated_at <= :toUpdatedAt', { toUpdatedAt: toUpdatedAt });
    if (numberOfRepeats) {
      const subQb = this.leadCaresRepo
        .createQueryBuilder('lc')
        .select('lc.leadId', 'lead_id')
        .addSelect('COUNT (lc.id)', 'cares_count')
        .groupBy('lc.leadId');
      qb.leftJoin(
        `(${subQb.getQuery()})`,
        'lc',
        'lead.id = lc.lead_id',
      ).andWhere('lc.cares_count > :numberOfRepeats', { numberOfRepeats });
    }
    if (fromLastCare || toLastCare) {
      qb.leftJoin('lead.latestCareItem', 'lcii');
      if (fromLastCare)
        qb.andWhere('lcii.createdAt IS NOT NULL').andWhere('lcii.createdAt >= :fromLastCare', {
          fromLastCare,
        });

      if (toLastCare) {
        qb.andWhere('lcii.createdAt IS NOT NULL').andWhere('lcii.createdAt <= :toLastCare', {
          toLastCare,
        });
      }
    }

    if (!isNil(filter.numberOfCares)) {
      const subQuery = await this.leadsRepo
        .createQueryBuilder('l')
        .innerJoin(Order, 'o', 'o.id = l.orderId')
        .leftJoin(LeadCareItem, 'lci', 'lci.leadId = l.id')
        .where(`(lci.note != 'from system' OR lci.note IS NULL)`)
        .select(['l.id'])
        .orderBy('l.id', 'DESC');
      if (from) subQuery.andWhere('l.createdAt >= :from', { from });
      if (to) subQuery.andWhere('l.createdAt <= :to', { to });
      if (filter.numberOfCares === 0) {
        subQuery.andWhere('lci.leadId IS NULL');
      } else {
        if (filter.numberOfCares === 1) subQuery.andWhere('lci.leadId IS NOT NULL');
        subQuery.groupBy('l.id').having('COUNT(*) = :numberOfCares', { numberOfCares });
      }
      qb.andWhere(`lead.id IN (${subQuery.getQuery()})`).setParameters(subQuery.getParameters());
    }
    if (filter.fromNumberOfCares) {
      const subQuery = await this.leadsRepo
        .createQueryBuilder('l')
        .innerJoin(Order, 'o', 'o.id = l.orderId')
        .leftJoin(LeadCareItem, 'lci', 'lci.leadId = l.id')
        .where(`(lci.note != 'from system' OR lci.note IS NULL)`)
        .select(['l.id'])
        .orderBy('l.id', 'DESC');
      if (from) subQuery.andWhere('l.createdAt >= :from', { from });
      if (to) subQuery.andWhere('l.createdAt <= :to', { to });
      if (filter.fromNumberOfCares === 1) subQuery.andWhere('lci.leadId IS NOT NULL');
      subQuery.groupBy('l.id').having('COUNT(*) >= :fromNumberOfCares', { fromNumberOfCares });
      qb.andWhere(`lead.id IN (${subQuery.getQuery()})`).setParameters(subQuery.getParameters());
    }

    if (filter.toNumberOfCares) {
      const subQuery = await this.leadsRepo
        .createQueryBuilder('l')
        .innerJoin(Order, 'o', 'o.id = l.orderId')
        .leftJoin(LeadCareItem, 'lci', 'lci.leadId = l.id')
        .where(`(lci.note != 'from system' OR lci.note IS NULL)`)
        .select(['l.id'])
        .orderBy('l.id', 'DESC');
      if (from) subQuery.andWhere('l.createdAt >= :from', { from });
      if (to) subQuery.andWhere('l.createdAt <= :to', { to });
      subQuery.groupBy('l.id').having('COUNT(*) <= :toNumberOfCares', { toNumberOfCares });
      qb.andWhere(`lead.id IN (${subQuery.getQuery()})`).setParameters(subQuery.getParameters());
    }
    if (!isEmpty(state)) qb.andWhere('lead.state IN (:...state)', { state });
    if (userIds) qb.andWhere('lead.user_id IN (:...userIds)', { userIds });
    if (excludeUserIds) qb.andWhere('lead.user_id NOT IN (:...excludeUserIds)', { excludeUserIds });
    if (!isEmpty(reasonIds)) {
      const noReasonId = remove(reasonIds, id => id === -1);
      if (
        (isNil(isSearchLatestReason) || !isSearchLatestReason) &&
        reasonTab !== ReasonTab.LATEST
      ) {
        qb.leftJoin(LeadCareItem, 'CI', 'CI.lead_id = lead.id')
          .andWhere(
            new Brackets(sqb => {
              if (!isEmpty(noReasonId)) sqb.where(`CI.reason_id IS NULL`);
              if (!isEmpty(reasonIds)) {
                const reasonsSubQb = this.careReasonRepo
                  .createQueryBuilder('cr1')
                  .innerJoin(
                    CareReason,
                    'cr2',
                    'cr1.reasonKey = cr2.reasonKey AND cr1.companyId = cr2.companyId',
                  )
                  .select('cr1.id')
                  .where('cr2.id IN (:...reasonIds)');

                if (!isEmpty(countryIds))
                  reasonsSubQb.andWhere(
                    '(cr1.countryId IS NULL OR cr1.countryId IN (:...countryIds))',
                  );

                sqb.orWhere(`CI.reason_id IN (${reasonsSubQb.getQuery()})`);
              }
            }),
          )
          .andWhere(`(CI.note is null or CI.note != 'from system')`) // thêm điều kiện này để bỏ qua các lý do tự động từ hệ thống
          .setParameters({ reasonIds, countryIds });
      } else {
        qb.andWhere('lead.lastCareReasonId IN (:...reasonIds)', { reasonIds });
      }
    }
    // if (tagIds) {
    //   qb.leftJoin('order_tags', 'order_tags', `order_tags.order_id = order.id`).andWhere(
    //     'order_tags.tag_id IN (:...tagIds)',
    //     {
    //       tagIds,
    //     },
    //   );
    // }

    if (sourceType && !sourceIds) {
      qb.leftJoin('order.externalSource', 'es');
      if (sourceType === SourceEntity.fb_page) {
        qb.andWhere(`es.entity IN (:...fbSource)`, {
          fbSource: [
            SourceEntity.fb_scoped_user,
            SourceEntity.fb_post,
            SourceEntity.fb_ad,
            SourceEntity.fb_page,
          ],
        });
      } else {
        qb.andWhere(`es.entity = :sourceType`, { sourceType });
      }
    }

    if (sourceType && sourceIds) {
      qb.leftJoin('order.externalSource', 'es');

      if (sourceType && sourceIds) {
        const keyPairs = sourceIds.map(it => {
          return `('${sourceType}', '${it}')`;
        });
        const extSources = await this.externalSourceRepo
          .createQueryBuilder('es')
          .where(`(es.entity, entity_id) IN (${keyPairs.join(', ')})`)
          .select(['es.id'])
          .getMany();
        if (isEmpty(extSources)) return null;

        const extSrcIds = extSources.map(it => it.id);
        const dbConnection = getConnection(orderConnection);
        const ancestorIdQb = dbConnection
          .createQueryBuilder()
          .from('order_sources_closure', 'osc')
          .where('osc.id_descendant IN (:...extSrcIds)')
          .select(['osc.id_ancestor']);
        const descendantsQb = dbConnection
          .createQueryBuilder()
          .from('order_sources_closure', 'osc')
          .where(`osc.id_ancestor IN (${ancestorIdQb.getQuery()})`)
          .select('osc.id_descendant');
        qb.andWhere(`order.externalSourceId IN (${descendantsQb.getQuery()})`).setParameters({
          extSrcIds,
        });
      }
    }

    if (sourceTypes && !sourceIds) {
      qb.leftJoin('order.externalSource', 'es');
      qb.andWhere(`es.entity IN (:...sourceTypes)`, { sourceTypes });
    }

    if (sourceTypes && sourceIds) {
      const landingIds = [];
      const fanPageIds = [];
      for (const id of sourceIds) {
        if (Utils.isFacebookUID(id)) {
          fanPageIds.push(id);
        } else {
          landingIds.push(id);
        }
      }
      const landingKeyPairs = landingIds.map(it => {
        return `('${SourceEntity.landing_page}', '${it}')`;
      });
      const fanPageKeyPairs = fanPageIds.map(it => {
        return `('${SourceEntity.fb_page}', '${it}')`;
      });
      const keyPairs = [...landingKeyPairs, ...fanPageKeyPairs];
      const extSources = await this.externalSourceRepo
        .createQueryBuilder('es')
        .where(`(es.entity, entity_id) IN (${keyPairs.join(', ')})`)
        .select(['es.id'])
        .getMany();
      if (isEmpty(extSources)) return null;

      const extSrcIds = extSources.map(it => it.id);
      const dbConnection = getConnection(orderConnection);
      const ancestorIdQb = dbConnection
        .createQueryBuilder()
        .from('order_sources_closure', 'osc')
        .where('osc.id_descendant IN (:...extSrcIds)')
        .select(['osc.id_ancestor']);
      const descendantsQb = dbConnection
        .createQueryBuilder()
        .from('order_sources_closure', 'osc')
        .where(`osc.id_ancestor IN (${ancestorIdQb.getQuery()})`)
        .select('osc.id_descendant');
      qb.andWhere(`order.externalSourceId IN (${descendantsQb.getQuery()})`).setParameters({
        extSrcIds,
      });
    }

    //Nhân sự MKT
    if (marketerIds) qb.andWhere('order.marketerId IN (:...marketerIds)', { marketerIds });

    if (query) {
      const phonesQuery = filter.query.match(/\S+/g).join('|');
      qb.andWhere(
        new Brackets(qb => {
          qb.where('order.display_id ILIKE :query', { query: `%${query}%` })
            .orWhere('order.customer_name ILIKE :query', { query: `%${query}%` })
            .orWhere('order.customer_phone ILIKE :query', { query: `%${query}%` })
            .orWhere('order.address_ward ILIKE :query', { query: `%${query}%` })
            .orWhere('order.address_district ILIKE :query', { query: `%${query}%` })
            .orWhere('order.address_province ILIKE :query', { query: `%${query}%` })
            .orWhere('order.customer_phone ~* :phonesQuery', { phonesQuery });
        }),
      );
    }

    if (!isEmpty(projectIds)) qb.andWhere('order.projectId IN (:...projectIds)', { projectIds });

    if (!isEmpty(collectType)) {
      qb.andWhere(
        new Brackets(sqb => {
          for (const type of collectType) {
            switch (type) {
              case LeadCollectType.convertFromFacebook:
                sqb.orWhere('(order.crossCare = TRUE AND order.fb_scoped_user_id IS NOT NULL)');
                break;
              case LeadCollectType.captureForm:
                sqb.orWhere('lead.form_captured_at IS NOT NULL');
                break;
              case LeadCollectType.manualKeying:
              default:
                sqb.orWhere('lead.form_captured_at IS NULL AND order.fb_scoped_user_id IS NULL');
                break;
            }
          }
        }),
      );
    }

    if (hasDuplicates) {
      // qb.leftJoin(DuplicateLead, 'd_leads', 'lead.id = d_leads.lead_id').andWhere(
      //   'd_leads.id IS NOT NULL AND lead.ignore_duplicate_warning = FALSE',
      // );
      qb.andWhere('dupLeads.id IS NOT NULL AND lead.ignore_duplicate_warning = FALSE');
    }

    if (productIds) {
      qb.andWhere('products.productId IN (:...productIds)', { productIds });
    }
    return qb;
  }

  async getLeads(
    pagination?: PaginationOptions,
    filter?: LeadsFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<Lead[]> {
    const leadsQb = await this.getLeadsQueryBuilder(
      filter,
      undefined,
      headers['country-ids']?.split(', '),
      request.user.companyId,
    );
    if (isNil(leadsQb)) return [];

    let orderBy = filter.orderBy ? `lead.${filter.orderBy}` : 'lead.id';
    if (filter.orderBy === 'lastCareAt') orderBy = 'lci.createdAt';
    else if (filter.orderBy === 'reasonUpdateTime') orderBy = 'lci.createdAt';

    const sort = filter.sort ? filter.sort : Sort.DESC;
    const mQuery = leadsQb.clone();
    mQuery
      .leftJoin('lead.latestCareItem', 'lci')
      .addSelect(['lci.id', 'lci.reasonId', 'lci.timesRepeatReason', 'lci.createdAt']);
    leadsQb
      .leftJoin('lead.latestCareItem', 'lci')
      .addSelect(['lci.id', 'lci.reasonId', 'lci.timesRepeatReason', 'lci.createdAt'])
      .leftJoin('lci.reason', 're')
      .addSelect(['re.id', 're.name', 're.reasonKey'])
      .addSelect([
        'order.id',
        'order.displayId',
        'order.customerName',
        'order.customerPhone',
        'order.addressText',
        'order.addressProvince',
        'order.addressDistrict',
        'order.addressWard',
        'order.postCode',
        'order.note',
        'order.totalPrice',
        'order.shippingFee',
        'order.surcharge',
        'order.discount',
        'order.marketerId',
        'order.externalSourceId',
        'order.updatedAt',
        'order.projectId',
        'order.marketerId',
        'order.crossCare',
        'order.fbScopedUserId',
        'order.type',
      ])
      .leftJoin('lead.duplicateLeads', 'dupLeads')
      .addSelect([
        'dupLeads.id',
        'dupLeads.orderId',
        'dupLeads.displayId',
        'dupLeads.duplicateDisplayId',
      ])
      .leftJoinAndSelect('order.carrier', 'carrier')
      .leftJoin('order.tags', 'tags', `tags.status = ${TagStatus.active}`)
      .addSelect(['tags.id', 'tags.name'])
      .leftJoin('lead.cares', 'cares')
      .addSelect(['cares.id', 'cares.userId'])
      .leftJoin('cares.careItems', 'ci')
      .addSelect(['ci.id', 'ci.createdAt', 'ci.reasonId', 'ci.note'])
      .leftJoin('ci.reason', 'r')
      .addSelect(['r.id', 'r.name'])
      .leftJoin('lead.currentCare', 'currentCare')
      .addSelect(['currentCare.id', 'currentCare.userId'])
      .addSelect(['currentCare.created_at', 'currentCare.createdAt'])
      .leftJoin('currentCare.careItems', 'careItems')
      .addSelect([
        'careItems.id',
        'careItems.reasonId',
        'careItems.timesRepeatReason',
        'careItems.note',
      ]);

    mQuery
      .leftJoin('order.tags', 'tags', `tags.status = ${TagStatus.active}`)
      .leftJoin('lead.duplicateLeads', 'dupLeads')
      .addSelect([
        'dupLeads.id',
        'dupLeads.orderId',
        'dupLeads.displayId',
        'dupLeads.duplicateDisplayId',
      ]);

    if (filter.getUpcomingAppointments) {
      leadsQb.leftJoinAndMapMany(
        'currentCare.upcomingAppointments',
        AppointmentSchedule,
        'a',
        'currentCare.id = a.leadCareId AND a.appointmentTime > NOW()',
      );
    }
    if (filter.getAllAppointments) {
      leadsQb.leftJoinAndMapMany(
        'lead.appointments',
        AppointmentSchedule,
        'appointments',
        'lead.id = cares.leadId AND cares.id = appointments.leadCareId',
      );
    }

    leadsQb.orderBy(orderBy, sort);
    mQuery.orderBy(orderBy, sort);

    if (filter.hasDuplicates) {
      leadsQb.orderBy('order.customerPhone').addOrderBy('lead.id', 'DESC');
      mQuery.orderBy('order.customerPhone').addOrderBy('lead.id', 'DESC');
    }

    mQuery.select(['lead.id', 'lead.createdAt', 'lead.updatedAt', 'lci.createdAt']);
    if (filter.hasDuplicates) {
      mQuery.addSelect('order.customerPhone');
    }

    if (filter.tagIds) {
      const subQb = getConnection(orderConnection)
        .createQueryBuilder()
        .from('order_tags', 'ot')
        .select('ot.order_id', 'order_id')
        .where('ot.tag_id IN (:...tagIds)', { tagIds: filter.tagIds })
        .groupBy('ot.order_id');
      if (filter.tagMethod == TagMethodType.Include) {
        if (filter.operator == TagOperatorType.And) {
          subQb.having(`COUNT(DISTINCT ot.tag_id) = ${filter.tagIds?.length}`);
          mQuery
            .andWhere(`lead.orderId IN (${subQb.getQuery()})`)
            .setParameters(subQb.getParameters());
        }
        if (filter.operator == TagOperatorType.Or) {
          mQuery.andWhere('order_tags.tag_id IN (:...tagIds)', {
            tagIds: filter.tagIds,
          });
        }
      }

      if (filter.tagMethod == TagMethodType.Exclude) {
        if (filter.operator == TagOperatorType.And) {
          subQb.having(`COUNT(DISTINCT ot.tag_id) = ${filter.tagIds?.length}`);
          mQuery
            .andWhere(`lead.orderId NOT IN (${subQb.getQuery()})`)
            .setParameters(subQb.getParameters());
        }
        if (filter.operator == TagOperatorType.Or) {
          mQuery
            .andWhere(`lead.orderId NOT IN (${subQb.getQuery()})`)
            .setParameters(subQb.getParameters());
        }
      }
    }
    if (filter.fromCurrentCare || filter.toCurrentCare) {
      mQuery.leftJoin('lead.currentCare', 'currentCare');
      if (filter.fromCurrentCare) {
        mQuery.andWhere('currentCare.created_at >= :fromCurrentCare', {
          fromCurrentCare: filter.fromCurrentCare,
        });
        leadsQb.andWhere('currentCare.created_at >= :fromCurrentCare', {
          fromCurrentCare: filter.fromCurrentCare,
        });
      }

      if (filter.toCurrentCare) {
        mQuery.andWhere('currentCare.created_at <= :toCurrentCare', {
          toCurrentCare: filter.toCurrentCare,
        });
        leadsQb.andWhere('currentCare.created_at <= :toCurrentCare', {
          toCurrentCare: filter.toCurrentCare,
        });
      }
    }

    if (pagination) mQuery.take(pagination.limit).skip(pagination.skip);
    const orderIds = await mQuery.getMany();
    if (orderIds?.length <= 0) return [];
    leadsQb.andWhere(`lead.id IN (:...oIds)`, {
      oIds: orderIds?.map((it: any) => it?.id),
    });

    const leads = await leadsQb.getMany();
    if (filter.getExternalSource) {
      const extSourceIds = leads.map(lead => lead.order.externalSourceId);
      if (extSourceIds.length > 0) {
        const extSourcesQb = this.externalSourceRepo
          .createQueryBuilder('es')
          .leftJoin('order_sources_closure', 'osc', 'osc.id_descendant = es.id')
          .leftJoinAndMapMany('es.parents', OrderSource, 'oes', 'osc.id_ancestor = oes.id')
          .where('es.id IN (:...extSourceIds)', { extSourceIds });
        const extSources = await extSourcesQb.getMany();
        const extSourcesLookup = extSources?.reduce((prev, es) => {
          prev[String(es.id)] = es.parents;
          return prev;
        }, {});
        if (extSourcesLookup) {
          for (const lead of leads) {
            if (!lead.order.externalSourceId) continue;
            lead.order.externalSource = extSourcesLookup[lead.order.externalSourceId][0];
            lead.order.externalSources = extSourcesLookup[lead.order.externalSourceId];
          }
        }
      }
    }

    return leads;
  }

  async mobileGetLeads(
    pagination?: PaginationOptions,
    filter?: MobileLeadsFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<Lead[]> {
    const leadsQb = await this.getLeadsQueryBuilder(
      filter,
      pagination,
      headers['country-ids']?.split(', '),
      request.user.companyId,
    );
    if (isNil(leadsQb)) return [];

    const orderBy = filter.orderBy
      ? filter.orderBy !== 'timesRepeatReason'
        ? `lead.${filter.orderBy}`
        : 'lci.timesRepeatReason'
      : 'lead.id';
    const sort = filter.sort ? filter.sort : Sort.DESC;

    leadsQb
      .leftJoin('lead.latestCareItem', 'lci')
      .addSelect(['lci.id', 'lci.reasonId', 'lci.timesRepeatReason', 'lci.createdAt'])
      .leftJoin('lci.reason', 're')
      .addSelect(['re.id', 're.name', 're.reasonKey'])
      .addSelect([
        'order.id',
        'order.displayId',
        'order.customerName',
        'order.customerPhone',
        'order.addressText',
        'order.addressProvince',
        'order.addressDistrict',
        'order.addressWard',
        'order.postCode',
        'order.note',
        'order.totalPrice',
        'order.shippingFee',
        'order.surcharge',
        'order.discount',
        'order.marketerId',
        'order.externalSourceId',
        'order.updatedAt',
        'order.projectId',
        'order.marketerId',
        'order.type',
      ])
      // .leftJoinAndSelect('order.products', 'products')
      // .leftJoin('lead.duplicateLeads', 'dupLeads')
      // .addSelect([
      //   'dupLeads.id',
      //   'dupLeads.orderId',
      //   'dupLeads.displayId',
      //   'dupLeads.duplicateDisplayId',
      // ])
      .leftJoinAndSelect('order.carrier', 'carrier')
      .leftJoin('order.tags', 'tags', `tags.status = ${TagStatus.active}`)
      .addSelect(['tags.id', 'tags.name'])
      .leftJoin('lead.cares', 'cares')
      .addSelect(['cares.id', 'cares.userId'])
      .leftJoin('cares.careItems', 'ci')
      .addSelect(['ci.id', 'ci.createdAt', 'ci.reasonId'])
      .leftJoin('ci.reason', 'r')
      .addSelect(['r.id', 'r.name'])
      .leftJoin('lead.currentCare', 'currentCare')
      .addSelect(['currentCare.id', 'currentCare.userId'])
      .leftJoin('currentCare.careItems', 'careItems')
      .addSelect([
        'careItems.id',
        'careItems.reasonId',
        'careItems.timesRepeatReason',
        'careItems.note',
      ]);

    // if (filter.productIds) {
    //   leadsQb.andWhere('products.id IN (:...productIds)', { productIds: filter.productIds });
    // }

    if (filter.assignedFrom) {
      leadsQb.andWhere('order.updatedAt >= :assignedFrom', { assignedFrom: filter.assignedFrom });
    }

    if (filter.assignedTo) {
      leadsQb.andWhere('order.updatedAt < :assignedTo', { assignedTo: filter.assignedTo });
    }

    if (filter.tagIds) {
      leadsQb.andWhere('order_tags.tag_id IN (:...tagIds)', {
        tagIds: filter.tagIds,
      });
    }

    if (filter.getUpcomingAppointments) {
      leadsQb.leftJoinAndMapMany(
        'currentCare.upcomingAppointments',
        AppointmentSchedule,
        'a',
        'currentCare.id = a.leadCareId AND a.appointmentTime > NOW()',
      );
    }
    if (filter.getAllAppointments) {
      leadsQb.leftJoinAndMapMany(
        'lead.appointments',
        AppointmentSchedule,
        'appointments',
        'lead.id = cares.leadId AND cares.id = appointments.leadCareId',
      );
    }
    if (filter.orderBy !== 'timesRepeatReason') {
      leadsQb.orderBy(orderBy, sort);
    } else {
      leadsQb.orderBy(orderBy, sort, sort === Sort.ASC ? 'NULLS FIRST' : 'NULLS LAST');
    }

    if (filter.hasDuplicates) {
      leadsQb.orderBy('order.customerPhone').addOrderBy('lead.id', 'DESC');
    }

    const leads = await leadsQb.getMany();
    let leadsDupLookup = {};
    if (leads.length > 0) {
      const leadsDup = await this.duplicateLeadsRepo
        .createQueryBuilder('dl')
        .where('dl.lead_id IN (:...oIds)', {
          oIds: leads?.map((it: any) => it?.id),
        })
        .getMany();
      leadsDupLookup = leadsDup.reduce((prev, next) => {
        if (prev[next.leadId] === undefined) prev[next.leadId] = [next];
        else prev[next.leadId].push(next);
        return prev;
      }, {});
    }
    if (filter.getExternalSource) {
      const extSourceIds = leads.map(lead => lead.order.externalSourceId);
      if (extSourceIds.length > 0) {
        const extSourcesQb = this.externalSourceRepo
          .createQueryBuilder('es')
          .leftJoin('order_sources_closure', 'osc', 'osc.id_descendant = es.id')
          .leftJoinAndMapMany('es.parents', OrderSource, 'oes', 'osc.id_ancestor = oes.id')
          .where('es.id IN (:...extSourceIds)', { extSourceIds });
        const extSources = await extSourcesQb.getMany();
        const extSourcesLookup = extSources?.reduce((prev, es) => {
          prev[String(es.id)] = es.parents;
          return prev;
        }, {});
        if (extSourcesLookup) {
          for (const lead of leads) {
            if (!lead.order.externalSourceId) continue;
            lead.order.externalSource = extSourcesLookup[lead.order.externalSourceId][0];
            lead.order.externalSources = extSourcesLookup[lead.order.externalSourceId];
          }
        }
      }
    }
    const newResult = leads.map(l => {
      return {
        ...l,
        duplicateLeads: leadsDupLookup[l.id],
      };
    });
    return plainToInstance(Lead, newResult);
  }

  async getLeadDetail(
    lid: number,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<Lead> {
    const mQuery = this.leadsRepo
      .createQueryBuilder('leads')
      .where('leads.id = :lid', { lid })
      .leftJoinAndSelect('leads.cares', 'cares')
      .leftJoinAndSelect('cares.careItems', 'careItems')
      .leftJoin('leads.duplicateLeads', 'dupLeads')
      .addSelect([
        'dupLeads.id',
        'dupLeads.leadId',
        'dupLeads.duplicateLeadId',
        'dupLeads.orderId',
        'dupLeads.displayId',
        'dupLeads.duplicateDisplayId',
      ]);

    const data = await mQuery.getOne();
    let duplicateLeadIds = [];
    if (data) {
      duplicateLeadIds = data.duplicateLeads.map(dupLead => dupLead.duplicateLeadId);
      if (duplicateLeadIds.length === 0) return data;
      // Fetch lead.id, lead.orderId, and lead.state for duplicateLeadIds
      const leadOrders = await this.leadsRepo
        .createQueryBuilder('leads')
        .where('leads.id IN (:...duplicateLeadIds)', { duplicateLeadIds })
        .select(['leads.id', 'leads.orderId', 'leads.state', 'leads.formCapturedAt'])
        .getMany();

      // Map duplicateLeadId to orderId and state
      const leadOrderMap = new Map(
        leadOrders.map(item => [
          item.id,
          {
            orderId: item.orderId,
            state: CareState[item.state],
            formCapturedAt: item.formCapturedAt,
          },
        ]),
      );

      // Fetch orders using the collected orderIds
      const orderIds = [...new Set(leadOrders.map(item => item.orderId))]; // Ensure unique orderIds
      const orders = await this.orderRepo
        .createQueryBuilder('orders')
        .leftJoinAndSelect('orders.externalSource', 'es')
        .where('orders.id IN (:...orderIds)', { orderIds })
        .getMany();

      // Create a map for orders
      const orderMap = new Map(orders.map(order => [order.id, order]));

      // Enrich duplicateLeads with corresponding orders and states
      data.duplicateLeads = data.duplicateLeads
        .map(dupLead => {
          const leadDetails = leadOrderMap.get(dupLead.duplicateLeadId);
          if (
            [CareState.junk, CareState.lost].includes(
              CareState[leadDetails?.state as keyof typeof CareState],
            )
          )
            return;
          const order = orderMap.get(leadDetails?.orderId);

          return {
            ...dupLead,
            order: order || null,
            state: leadDetails?.state || null, // Add the state here
            formCapturedAt: leadDetails?.formCapturedAt || null,
          };
        })
        .filter(data => data);
    }

    return data;
  }

  /**
   * Get danh sách lý do chăm sóc đã gắn cho data
   * @param id id lead hoặc id đơn hàng (nếu là số thì là id lead, nếu là chữ thì là id đơn hàng (n)
   * @returns list care items of lead
   */
  async getLeadReason(id: string): Promise<LeadCareItem[]> {
    const qb = this.leadCareItemsRepo
      .createQueryBuilder('careItems')
      .leftJoinAndSelect('careItems.leadCare', 'leadCare')
      .leftJoinAndSelect('careItems.reason', 'reason')
      .leftJoinAndSelect('careItems.tags', 'tags')
      .orderBy('careItems.created_at', 'DESC');

    if (`${parseInt(id)}` === id) qb.where('leadCare.lead_id = :id');
    else qb.innerJoin('leadCare.lead', 'l').innerJoin('l.order', 'o', 'o.displayId = :id');
    qb.setParameters({ id });

    return qb.getMany();
  }

  async getLeadHistoriesV2(
    leadId: number,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    // Subqueries to fetch IDs
    const subQb = this.leadCareItemsRepo
      .createQueryBuilder('i')
      .leftJoin('i.leadCare', 'lc')
      .where('lc.leadId = :leadId', { leadId })
      .select('i.id');
    const appointmentLogSubQb = this.appointmentScheduleRepo
      .createQueryBuilder('a')
      .leftJoin('a.leadCare', 'lc')
      .where('lc.leadId = :leadId', { leadId })
      .withDeleted();

    // Fetching IDs
    const [listCareIdsRaw, listAppointmentIdsRaw] = await Promise.all([
      subQb.getMany(),
      appointmentLogSubQb.getMany(),
    ]);

    // Handle empty results
    const listCareIds = listCareIdsRaw.map(it => String(it.id));
    const listAppointmentIds = listAppointmentIdsRaw.map(it => String(it.id));
    const listAppointmentLookup = listAppointmentIdsRaw.reduce((prev, next) => {
      prev[next.id] = next;
      return prev;
    }, {});

    // Query logs with dynamic conditions
    const qb = this.logsRepo
      .createQueryBuilder('logs')
      .andWhere(`(logs.table_name = 'leads' AND logs.record_id = :leadId)`)
      .orWhere(`(logs.parent_table_name = 'leads' AND logs.parent_id = :leadId)`);

    // Add conditions only if IDs are present
    if (listCareIds.length > 0) {
      qb.orWhere(`logs.table_name = 'lead_care_items' AND logs.record_id IN (:...listCareIds)`, {
        listCareIds,
      });
    }
    if (listAppointmentIds.length > 0) {
      qb.orWhere(
        `logs.table_name = 'appointment_schedules' AND logs.record_id IN (:...listAppointmentIds)`,
        { listAppointmentIds },
      );
    }

    qb.orderBy('logs.createdAt', 'DESC').setParameters({ leadId });

    // Fetch logs
    const logs = await qb.getMany();
    return logs.map(l => {
      const newLog = { ...l };
      if (l.tableName === 'appointment_schedules') {
        newLog['dataDetail'] = listAppointmentLookup[l.recordId];
      }
      return newLog;
    });
  }

  async getLeadHistories(
    leadId: number,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const subQb = this.leadCareItemsRepo
      .createQueryBuilder('i')
      .leftJoin('i.leadCare', 'lc')
      .where('lc.leadId = :leadId')
      .select('i.id');
    const appointmentLogSubQb = this.appointmentScheduleRepo
      .createQueryBuilder('a')
      .leftJoin('a.leadCare', 'lc')
      .where('lc.leadId = :leadId')
      .withDeleted()
      .select('a.id');
    const qb = this.logsRepo
      .createQueryBuilder('logs')
      .andWhere(`(logs.table_name = 'leads' AND logs.record_id::INT = :leadId)`)
      .orWhere(`(logs.parent_table_name = 'leads' AND logs.parent_id::INT = :leadId)`)
      .orWhere(
        `(logs.table_name = 'lead_care_items' AND logs.record_id::INT IN (${subQb.getQuery()}))`,
      )
      .orWhere(
        `(logs.table_name = 'appointment_schedules' AND logs.record_id::INT IN (${appointmentLogSubQb.getQuery()}))`,
      )
      .orderBy('logs.createdAt', 'DESC')
      .setParameters({ leadId });
    const logs = await qb.getMany();
    return logs;
  }

  async createCareItem(
    body: CreateLeadCareItemDto,
    request?: Record<string, any>,
    headers?: Record<string, any>,
    force?: boolean,
  ): Promise<LeadCareItem> {
    const redisKey = `updating-ag-sale-lead.${body.leadId}`;
    const [, [, isUpdating]] = await this.redis
      .multi()
      .set(redisKey, 0, 'EX', 60, 'NX')
      .incr(redisKey)
      .exec();
    if (isUpdating > 1) {
      throw new BadRequestException({
        isUpdating,
        message: `This lead is updating by another request. Please try again later.`,
      });
    }

    try {
      const leadItem = plainToInstance(LeadCareItem, {
        leadId: body.leadId,
        leadCareId: body?.leadCareId,
        reasonId: body?.reasonId,
        note: body?.note,
        creatorId: request.user.id,
      });
      if (body.tagIds) leadItem.tags = await this.tagsService.findByIds(body.tagIds);

      const leadCare = await this.leadCaresRepo
        .createQueryBuilder('lc')
        .where('lc.id = :id')
        .select(['lc.id', 'lc.state', 'lc.userId'])
        .leftJoin('lc.lead', 'l')
        .addSelect(['l.id', 'l.currentCareId', 'l.state', 'l.leadType'])
        .leftJoin('l.order', 'o')
        .addSelect(['o.id', 'o.customerName', 'o.customerPhone'])
        .setParameters({ id: body.leadCareId })
        .getOne();

      if (!leadCare) throw new NotFoundException('Lead care not exists!');
      if (leadCare.userId !== request.user.id && !force) throw new ForbiddenException();

      const lead = leadCare.lead;
      if (lead.currentCareId !== leadCare.id) throw new ForbiddenException();

      const order = lead.order;
      if (!order) throw new NotFoundException('Not found order id' + order.id);

      const currentState = lead?.state;

      const reason = await this.careReasonRepo.findOne(
        { id: body?.reasonId },
        { select: ['id', 'reasonKey', 'state'] },
      );
      if (!reason) throw new BadRequestException('Not found reason');

      let nextState: CareState = null;
      if ([CareState.failed, CareState.potential].includes(currentState as CareState)) {
        const winStates = [
          CareState.temp,
          CareState.awaiting_stock,
          CareState.confirmed,
          CareState.reconfirm,
        ];
        if (
          (currentState === CareState.failed &&
            [...winStates, CareState.potential].includes(reason.state as CareState)) ||
          (currentState === CareState.potential &&
            [...winStates, CareState.potential, CareState.failed].includes(
              reason.state as CareState,
            ))
        ) {
          nextState = reason.state as CareState;
        } else {
          nextState = currentState as CareState;
        }
      } else {
        nextState =
          reason.state === CareState.awaiting_stock && !isNil(body.appointmentAt)
            ? CareState.temp
            : (reason.state as CareState);
      }

      if (!NON_COUNT_REPEAT_TIMES_CARE_STATES.includes(reason.state as CareState)) {
        const countReason = await this.leadCareItemsRepo
          .createQueryBuilder('careItem')
          .leftJoin('careItem.leadCare', 'leadCare')
          .andWhere('leadCare.lead_id = :leadId', { leadId: body?.leadId })
          .andWhere('careItem.reason_id = :reasonId', { reasonId: body?.reasonId })
          .getCount();

        leadItem.timesRepeatReason = countReason + 1;
      }

      if (!NEXT_CARE_STATES[currentState]?.includes(nextState) && !force)
        throw new ForbiddenException('The selected reason is not allowed to update');

      let updateOrderData: UpdateOrderDto = {};
      if (CONFIRM_LEAD_STATES.includes(nextState as CareState)) {
        if (!isEmpty(body.orderData)) {
          for (const key of Object.keys(body.orderData)) {
            updateOrderData[key] = body.orderData[key];
          }
        }
        if (nextState !== CareState.temp) {
          updateOrderData.status = OrderStatus.Confirmed;
        }
        if (nextState === CareState.temp) updateOrderData.status = OrderStatus.New;
      } else if (CANCEL_LEAD_STATES.includes(nextState as CareState)) {
        updateOrderData.status = OrderStatus.Canceled;
        updateOrderData.cancelReasonText = `Order canceled due to lead cancellation.`;
      }
      if (body.tagIds) updateOrderData.additionalTagIds = body.tagIds;
      if (body.customerEDD) {
        const orderCarrierData = new OrderCarrierDto();
        orderCarrierData.customerEDD = body.customerEDD;
        updateOrderData.carrier = orderCarrierData;
      }

      /**
       * Workaround to handle case create FFM order fails
       * Firstly, check validations and save order as [New]
       * Next, update order status to [Confirmed] and sync order to FFM after create lead care item
       */
      if (!isEmpty(updateOrderData)) {
        // If the update status is [Confirm], we need to check validations and save order as [New] first
        if (updateOrderData.status && updateOrderData.status === OrderStatus.Confirmed) {
          await this.ordersService.updateOrder(
            body?.orderId,
            { ...updateOrderData, status: OrderStatus.New },
            headers,
            request,
          );
          // Then delete all update order props except status so we can confirm the order later at the end of the process.
          updateOrderData = { status: OrderStatus.Confirmed };
        } else {
          // Update order normally
          await this.ordersService.updateOrder(body?.orderId, updateOrderData, headers, request);
          updateOrderData = undefined;
        }
      }

      const appointment = new AppointmentSchedule();
      if (body.appointmentAt) appointment.appointmentTime = body.appointmentAt;
      if (body.appointmentContent) appointment.content = body.appointmentContent;

      let result: LeadCareItem;

      const connection = getConnection(orderConnection);
      const queryRunner = connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();
      try {
        result = await queryRunner.manager.save(leadItem);
        await queryRunner.manager.update(
          LeadCare,
          { id: leadCare.id },
          {
            state: nextState,
            updatedBy: request.user.id,
            updatedAt: result.createdAt,
          },
        );

        const updateResult = await queryRunner.manager.update(
          Lead,
          {
            id: lead.id,
            state: Not(CareState.confirmed),
          },
          {
            state: nextState,
            updatedBy: request.user.id,
            updatedAt: result.createdAt,
            lastCareItemId: result.id,
            lastCareReasonId: reason.id,
          },
        );

        if (!updateResult.affected) throw new BadRequestException();

        // const newLead = await queryRunner.manager.findOne(Lead, { id: lead.id });

        if (!isEmpty(appointment)) {
          appointment.leadCareId = leadCare.id;
          appointment.customerName = order.customerName;
          appointment.customerPhone = order.customerPhone;
          appointment.updatedBy = request.user.id;
          await queryRunner.manager.save(appointment);
        }

        await queryRunner.commitTransaction();

        lead.state = nextState;
        lead.updatedAt = result.createdAt;
        if (lead.leadType === LeadType.after_sale) {
          await this.leadAfterSaleService.onLeadUpdated(
            {
              id: lead.id,
              state: lead.state,
              currentCareId: leadCare.id,
              userId: request.user.id,
              updatedBy: request.user.id,
              updatedAt: lead.updatedAt,
            },
            currentState as CareStateAfterSales,
          );
        } else {
          await this.onLeadUpdated(
            {
              id: lead.id,
              state: lead.state,
              currentCareId: leadCare.id,
              userId: request.user.id,
              updatedBy: request.user.id,
              updatedAt: lead.updatedAt,
            },
            currentState as CareState,
          );
        }

        result.reason = reason;
        leadCare.lead = lead;
        leadCare.state = nextState;
        leadCare.updatedBy = request.user.id;
        result.leadCare = leadCare;
      } catch (error) {
        await queryRunner.rollbackTransaction();
        if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
        throw error;
      } finally {
        await queryRunner.release();
      }

      if (!isEmpty(updateOrderData)) {
        // Confirm order
        const order = await this.ordersService.updateOrder(
          body?.orderId,
          updateOrderData,
          headers,
          request,
        );
        console.log(`order ${order.id} ${order.displayId} update`, updateOrderData);
      }
      return result;
    } catch (error) {
      throw error;
    } finally {
      await this.redis.del(redisKey);
    }
  }

  async countReasons(leadId: number, filter): Promise<any> {
    const qb = this.leadCareItemsRepo
      .createQueryBuilder('items')
      .leftJoin('items.leadCare', 'care')
      .leftJoin('items.reason', 'reason')
      .where('care.leadId = :leadId', { leadId })
      .select('items.reasonId', 'reasonId')
      .addSelect('COUNT (DISTINCT items.id)', 'count')
      .groupBy('items.reasonId');
    const { state, reasonKey, excludeReasonKeys } = filter || {};
    if (state) qb.andWhere('reason.state IN (:...state)', { state });
    if (reasonKey) qb.andWhere('reason.reason_key = :reasonKey', { reasonKey });
    if (excludeReasonKeys)
      qb.andWhere('reason.reason_key NOT IN (:...excludeReasonKeys)', { excludeReasonKeys });
    const result = await qb.getRawMany();
    return result;
  }

  async countReasonsForSpecialState(leadId: number, filter) {
    const { state, reasonKey, excludeReasonKeys } = filter || {};
    const qbLog = this.logsRepo
      .createQueryBuilder('l')
      .where('l.tableName = :tableName', { tableName: 'leads' })
      .andWhere('l.recordId = :leadId', { leadId })
      .andWhere('l.action = :action', { action: 'STATUS' })
      .andWhere(`'${state}' = ANY(l.changes)`);
    if (state === CareState.failed) {
      qbLog.orderBy('l.createdAt', 'ASC');
    } else {
      qbLog.orderBy('l.createdAt', 'DESC');
    }
    qbLog.limit(1);
    const log = await qbLog.getOne();

    const qb = this.leadCareItemsRepo
      .createQueryBuilder('items')
      .leftJoin('items.leadCare', 'care')
      .leftJoin('items.reason', 'reason')
      .where('care.leadId = :leadId', { leadId })
      .andWhere('items.createdAt >= :createdAt', { createdAt: log?.createdAt })
      .andWhere('reason.state IN (:...state)', {
        state: [state, CareState.attempted],
      })
      .select('items.reasonId', 'reasonId')
      .addSelect('COUNT (DISTINCT items.id)', 'count')
      .groupBy('items.reasonId');

    const result = await qb.getRawMany();
    return result;
  }

  async checkFirstReasonIsAwaitingConfirm(leadId: number) {
    const qb = this.leadCareItemsRepo
      .createQueryBuilder('items')
      .leftJoin('items.leadCare', 'care')
      .leftJoinAndSelect('items.reason', 'reason')
      .where('care.leadId = :leadId', { leadId })
      .orderBy('items.createdAt', 'ASC')
      .limit(1);
    const result = await qb.getOne();
    if (result && result.reason.reasonKey === 'awaiting_confirmation') return true;
    return false;
  }

  async manualGather(
    filter: GatherLeadsFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<UpdateResult> {
    filter.state = !isEmpty(filter.state)
      ? intersection(filter.state, GATHERABLE_STATES)
      : GATHERABLE_STATES;
    const qb = await this.getLeadsQueryBuilder(
      filter,
      undefined,
      headers['country-ids']?.split(', '),
      request.user.companyId,
    );
    if (filter.hasDuplicates) qb.leftJoin('lead.duplicateLeads', 'dupLeads');
    const leads = isNil(qb) ? [] : await qb.select(['lead.id']).getMany();
    // console.log(`leads to gather`, leads);
    if (isEmpty(leads)) throw new BadRequestException('No lead to gather!');
    const leadIds = leads.map(lead => lead.id);
    // console.log(`leadIds`, leadIds);

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const result = await queryRunner.manager
        .createQueryBuilder(Lead, 'l')
        .update()
        .set({
          state: CareState.unassign_attempted,
          userId: null,
          currentCareId: null,
          updatedBy: request.user.id,
        })
        .where({ id: In(leadIds) })
        .returning(['id'])
        .execute();
      console.log(`manual gather result`, result);

      const affectedLeadIds = result.raw.map(r => r.id);

      await queryRunner.manager
        .createQueryBuilder(Order, 'o')
        .update()
        .where(
          `id IN (SELECT order_id FROM leads WHERE leads.id IN (${affectedLeadIds.join(',')}))`,
        )
        .set({
          saleId: null,
          lastUpdatedBy: request.user.id,
        })
        .execute();

      await queryRunner.commitTransaction();
      await this.amqpConnection.publish('message-service', 'lead-gather', leadIds);
      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async exportExcel(
    filter: LeadsFilter,
    request?: Record<string, any>,
    headers?: Record<string, string>,
  ): Promise<Buffer> {
    const timezone = headers?.['timezone'] ? Number(headers?.['timezone']) : -7;
    filter.getExternalSource = true;
    const qb = await this.getLeadsQueryBuilder(
      filter,
      undefined,
      headers['country-ids']?.split(', '),
      request.user.companyId,
    );

    const leads = isNil(qb)
      ? []
      : await qb
          .addSelect([
            'order.id',
            'order.displayId',
            'order.customerName',
            'order.customerPhone',
            'order.addressText',
            'order.addressProvince',
            'order.addressDistrict',
            'order.addressWard',
            'order.postCode',
            'order.note',
            'order.shippingFee',
            'order.surcharge',
            'order.discount',
            'order.marketerId',
            'order.externalSourceId',
          ])
          .orderBy('lead.id', 'DESC')
          .getMany();

    const userIds = [];
    const landingIds = [];
    for (const item of leads) {
      if (userIds.indexOf(item.userId) === -1) userIds.push(item.userId);

      const landingId = item.order?.externalSource?.entityId;
      if (landingId && landingIds.indexOf(landingId) === -1) landingIds.push(landingId);
    }
    const [landingPages, { data: uData }] = await Promise.all([
      this.landingService.getNames({ ids: landingIds }),
      this.amqpConnection.request({
        exchange: 'identity-service-roles',
        routingKey: 'get-users-by-ids',
        payload: { ids: userIds },
        timeout: 5000,
      }),
    ]);
    const landingPagesLookup: Record<string, LandingPage> = reduce(
      landingPages,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );
    const users = uData as Users[];
    const userLookup: Record<string, Users> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );
    const columns = [
      'Số thứ tự', // Col 0
      'Mã đơn hàng', // Col 1
      'Khách hàng', // Col 2
      'Số điện thoại', // Col 3
      'Địa chỉ', // Col 4
      'Ghi chú', // Col 5
      'Tổng tiền', // Col 6
      'Người phụ trách', // Col 7
      'Chăm sóc mới nhất', // Col 8
      'Tag', // Col 9
      'Nguồn', // Col 10
      'Chăm sóc cuối', // Col 11
      'Ngày tạo', // Col 12
      'Trạng thái', // Col 13
    ];
    const data: unknown[][] = [columns];

    for (let index = 0; index < leads.length; index++) {
      const lead = leads[index];
      const tags = lead.order?.tags || [];
      const rows = [...Array(tags.length || 1)].map(o => Array(columns.length).fill(null));

      const state = [
        PlainCareStateGroup[MAPPING_CARE_STATE_TO_GROUP[lead.state]],
        PlainCareState[lead.state],
      ].join(' - ');

      let lastCare: LeadCareItem;
      if (!isEmpty(lead?.cares)) {
        lastCare = lead.cares.flatMap(c => c.careItems).hasMax('id');
      }

      const addresses = [];
      if (lead?.order?.addressText) addresses.push(lead.order.addressText);
      if (lead?.order?.addressProvince) addresses.push(lead.order.addressProvince);
      if (lead?.order?.addressDistrict) addresses.push(lead.order.addressDistrict);
      if (lead?.order?.addressWard) addresses.push(lead.order.addressWard);
      if (lead?.order?.postCode) addresses.push(lead.order.postCode);
      const addressText = addresses.join(', ');

      try {
        rows[0][0] = index + 1;
        rows[0][1] = lead?.order?.displayId;
        rows[0][2] = lead?.order?.customerName;
        rows[0][3] = lead?.order?.customerPhone;
        rows[0][4] = addressText;
        rows[0][5] = lead?.order?.note;
        rows[0][6] = Math.max(
          (lead?.order?.totalPrice || 0) +
            (lead?.order?.shippingFee || 0) +
            (lead?.order?.surcharge || 0) -
            (lead?.order?.discount || 0),
          0,
        );
        rows[0][7] = lead?.userId ? userLookup[lead?.userId]?.name : null;
        rows[0][8] = lastCare?.reason?.name;
        rows[0][10] = lead?.order?.externalSource?.entityId
          ? landingPagesLookup[lead?.order?.externalSource?.entityId]?.name
          : null;
        rows[0][11] = moment(lastCare?.createdAt)
          .utcOffset(timezone * -60)
          .format('DD/MM/YYYY - HH:mm');
        rows[0][12] = moment(lead?.createdAt)
          .utcOffset(timezone * -60)
          .format('DD/MM/YYYY - HH:mm');
        rows[0][13] = state;

        for (let tIndex = 0; tIndex < tags.length; tIndex++) {
          const tag = tags[tIndex];
          rows[tIndex][9] = tag.name;
        }

        data.push(...rows);
      } catch (error) {
        console.log(`error at`, index, leads);
        console.log(`error reason`, error);
      }
    }

    const sheetOptions = {
      '!cols': new Array(columns?.length).fill({ wch: 20 }),
    };
    const buffer = xlsx.build([{ name: 'Leads export', data, options: {} }], {
      sheetOptions,
    }); // Returns a buffer
    return buffer;
  }

  async getCallLogs(id: number, ext: string, paging: PaginationOptions) {
    if (!ext) {
      return [];
    }
    return this.cdrRepository.find({
      take: paging.limit,
      skip: paging.skip,
      where: {
        caller: ext,
      },
      order: {
        startAt: 'DESC',
      },
    });
  }

  @RabbitRPC({
    exchange: 'voip-exchange',
    routingKey: 'handle-cdr-webhook',
    queue: 'handle-cdr-webhook',
    errorHandler: rmqErrorsHandler,
  })
  async handleCdr(data: ICdrWebhook) {
    const {
      call_id,
      callee,
      caller,
      ended_time,
      direction,
      ended_reason,
      fail_code,
      outbound_caller_id,
      ring_time,
      ring_duration,
      start_time,
      talk_duration,
      tenant_id,
      tenant_name,
    } = data;
    const cdr = new Cdr();
    cdr.id = call_id;
    cdr.callee = callee;
    cdr.caller = caller;
    cdr.endedAt = new Date(Number(ended_time) * 1000);
    cdr.direction = direction;
    cdr.endedReason = ended_reason;
    cdr.failCode = Number(fail_code || 0);
    cdr.outboundCallerId = outbound_caller_id;
    cdr.ringAt = new Date(Number(ring_time) * 1000);
    cdr.ringDuration = Number(ring_duration || 0);
    cdr.startAt = new Date(Number(start_time) * 1000);
    cdr.talkDuration = Number(talk_duration || 0);
    cdr.tenantId = tenant_id;
    cdr.tenantName = tenant_name;
    await this.cdrRepository.save(cdr);
    cdr.talkDuration &&
      (await this.amqpConnection.publish('voip-exchange', 'handle-cdr-record', data));
    return cdr;
  }

  @RabbitRPC({
    exchange: 'voip-exchange',
    routingKey: 'handle-cdr-record',
    queue: 'handle-cdr-record',
    errorHandler: rmqErrorsHandler,
  })
  async handleRecordFile(data: ICdrWebhook) {
    const client = new PortSipClient(
      process.env.PORTSIP_USERNAME,
      process.env.PORTSIP_PASSWORD,
      this.redis,
    );
    const record = await client.getRecordFile(data.session_id);
    if (!record) {
      return new Nack(false);
    }
    const attResponse = await axios.get(record, {
      responseType: 'arraybuffer',
    });
    const buffer = Buffer.from(attResponse.data, 'binary');
    const filename =
      moment()
        .valueOf()
        .toString() + '.wav';
    const form = new FormData();
    form.append('file', buffer, filename);
    const headers = {
      'Content-Type': 'multipart/form-data',
    };
    const response = await axios.request({
      method: 'POST',
      baseURL: process.env.PANCAKE_URL_API,
      url: PANCAKE_API_ENDPOINT.CONTENTS,
      params: {
        api_key: process.env.POS_API_KEY,
      },
      data: form,
      headers,
      timeout: 60000,
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });
    if (!response.data?.content_url) {
      throw new Error(`Cannot upload attachment to pancake`);
    }
    const res = await this.cdrRepository.update(
      {
        id: data.call_id,
      },
      { record: response.data?.content_url },
    );
    return new Nack();
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'after-order-status-updated',
    queue: 'queue-after-order-status-updated',
    errorHandler: rmqErrorsHandler,
  })
  async updateLeadState(payload: {
    status: OrderStatus;
    orderId: number;
    updatedAt: number;
    updatedBy: number;
    force?: boolean;
  }) {
    // console.log('payload', 'after-order-status-updated', payload);
    if (!payload?.orderId) return new Nack(false);

    const { status, updatedAt, updatedBy, force } = payload;
    const lead = await this.leadsRepo.findOne({ orderId: payload?.orderId });
    if (!lead) {
      console.log(`Cannot find lead by order ID: `, payload?.orderId);
      return new Nack(false);
    }

    const redisKey = `updating-ag-sale-lead.${lead.id}`;
    const [, [, isUpdating]] = await this.redis
      .multi()
      .set(redisKey, 0, 'EX', 60, 'NX')
      .incr(redisKey)
      .exec();

    if (isUpdating > 1) {
      console.log(`Lead ${lead.id} is updating by create care item`);
      return new Nack(false);
    }

    try {
      const isNeedUpdate =
        force || (lead?.lastUpdatedState ? new Date(updatedAt) > lead.lastUpdatedState : true);
      if (!isNeedUpdate) return new Nack(false);

      let state: CareState;
      switch (status) {
        // case OrderStatus.Canceled:
        //   state = CareState.junk;
        //   break;
        case OrderStatus.AwaitingStock:
          state = CareState.awaiting_stock;
          break;
        case OrderStatus.Reconfirm:
          state = CareState.reconfirm;
          break;
        case OrderStatus.Confirmed:
        case OrderStatus.Preparing:
        case OrderStatus.HandlingOver:
        case OrderStatus.InTransit:
        case OrderStatus.InDelivery:
        case OrderStatus.Delivered:
        case OrderStatus.DeliveredCompleted:
        case OrderStatus.FailedDelivery:
        case OrderStatus.AwaitingReturn:
        case OrderStatus.InReturn:
        case OrderStatus.ReturnedStocked:
        case OrderStatus.ReturnedCompleted:
        case OrderStatus.Damaged:
        case OrderStatus.DamagedCompleted:
        case OrderStatus.Lost:
        case OrderStatus.LostCompleted:
          state = CareState.confirmed;
          break;
        default:
          break;
      }
      if (isNil(state) || lead?.state === state) return new Nack(false);

      const criteria: FindConditions<Lead> = { id: lead.id };
      if (state === CareState.junk) criteria.state = Not(In(CANCEL_LEAD_STATES));
      else if (state === CareState.reconfirm)
        criteria.state = Not(In([CareState.reconfirm, CareState.confirmed]));
      else if (state === CareState.awaiting_stock)
        criteria.state = Not(
          In([CareState.awaiting_stock, CareState.reconfirm, CareState.confirmed]),
        );
      const result = await this.leadsRepo.update(criteria, {
        updatedBy,
        state,
      });
      console.log(`result of update lead ${lead.id} state after order status updated`, result);

      return result;
    } catch (error) {
      console.log(`update lead ${lead.id} state after order status updated error`, error);
      throw error;
    } finally {
      await this.redis.del(redisKey);
    }
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'after-lead-state-updated',
    queue: 'order-process-after-lead-state-updated',
    errorHandler: rmqErrorsHandler,
  })
  async afterLeadStateUpdated({
    leadId,
    state,
    updatedAt,
    currentCareId,
    currentState,
  }: {
    leadId: number;
    state: CareState;
    updatedAt: number;
    currentCareId?: number | null;
    currentState?: CareState;
  }) {
    if (!leadId || !state || !updatedAt) return new Nack(false);
    const lead = await this.leadsRepo
      .createQueryBuilder('l')
      .leftJoin('l.order', 'o')
      .addSelect([
        'o.id',
        'o.status',
        'o.companyId',
        'o.countryId',
        'o.crossCare',
        'o.fbScopedUserId',
      ])
      .where('l.id = :leadId', { leadId })
      .getOne();
    if (!lead) {
      console.log(`not found lead `, leadId);
      return new Nack(false);
    }
    let collectType = 'manualKeying';
    if (lead.formCapturedAt) {
      collectType = 'captureForm';
    } else if (!lead.formCapturedAt && lead.order.crossCare && lead.order.fbScopedUserId) {
      collectType = 'convertFromFacebook';
    } else if (!lead.formCapturedAt && !lead.order.fbScopedUserId) {
      collectType = 'manualKeying';
    }
    // const isManualKeying = isNil(lead.formCapturedAt);

    if (lead.lastUpdatedState && new Date(updatedAt) < lead.lastUpdatedState) {
      console.log(
        `no need to process because updated at ${new Date(
          updatedAt,
        )} is less than last updated at ${lead.lastUpdatedState}`,
      );
      return new Nack(true);
    }

    const isConfirmedLead = CONFIRM_LEAD_STATES.includes(state);
    if (isConfirmedLead) return new Nack(false);

    const companyId = lead.order.companyId;
    const countryId = lead.order.countryId;
    const config = await this.distributeConfigsService.getConfigByCompanyId(companyId, countryId);
    if (!config) {
      console.log(
        `No config for country ${countryId} of company ${companyId}. Therefore no need to trigger actions`,
      );
      return new Nack(false);
    }

    switch (state) {
      case CareState.potential: {
        if (currentState !== CareState.potential) {
          console.log(`No need to trigger action for data is not potential`);
          return new Nack(false);
        }
        const rule = config.rules.find(r => r.type === LeadDistributeRuleType.processing_leads);
        const val = rule?.value?.find(
          v => v.condition === LeadDistributeCondition.markPotentialDataAsFailed,
        );
        const triggerNumber = val?.value;
        if (!triggerNumber) {
          console.log(`No config number for this lead state`, state);
          return new Nack(false);
        }
        const reasonsCount = await this.countReasonsForSpecialState(leadId, {
          state: CareState.potential,
        });
        let countProcess = 0;
        reasonsCount.forEach(it => {
          countProcess += +it.count;
        });
        countProcess = countProcess - 1 >= 0 ? countProcess - 1 : 0;
        const canTrigger = countProcess >= triggerNumber;
        if (!canTrigger) {
          console.log(`No need to trigger action`);
          return new Nack(false);
        }
        const reasonFailed = await this.careReasonRepo.findOne({
          where: {
            state: CareState.failed,
            companyId: lead?.order?.companyId,
            leadType: LeadType.normal,
          },
          order: { id: 'ASC' },
        });
        const connection = getConnection(orderConnection);
        const queryRunner = connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
          await Promise.all([
            queryRunner.manager.update(
              Lead,
              { id: leadId },
              {
                state: CareState.failed,
                updatedBy: null,
                updatedAt,
              },
            ),
            currentCareId
              ? queryRunner.manager.update(
                  LeadCare,
                  { id: currentCareId },
                  { state: CareState.failed, updatedAt, updatedBy: null },
                )
              : Promise.resolve(),
            queryRunner.manager.save(
              plainToInstance(LeadCareItem, {
                leadId,
                reasonId: reasonFailed.id,
                creatorId: null,
                leadCareId: currentCareId,
                note: 'from system',
              }),
            ),
          ]);
          await queryRunner.commitTransaction();
          await this.onLeadUpdated({
            id: leadId,
            state: CareState.failed,
            updatedAt: new Date(updatedAt),
            currentCareId,
            updatedBy: null,
          });
        } catch (error) {
          await queryRunner.rollbackTransaction();
          if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
          throw error;
        } finally {
          await queryRunner.release();
        }
        break;
      }
      case CareState.attempted: {
        // t105697: skip awaiting_confirmation
        // const rule = config.rules.find(rule =>
        //   isManualKeying
        //     ? rule.type === LeadDistributeRuleType.mark_as_failed_when_tries_reached_n_times
        //     : rule.type ===
        //       LeadDistributeRuleType.mark_captured_data_as_failed_when_tries_reached_n_times,
        // );
        const rule = config.rules.find(rule =>
          collectType === 'manualKeying'
            ? rule.type === LeadDistributeRuleType.mark_as_failed_when_tries_reached_n_times
            : collectType === 'convertFromFacebook'
            ? rule.type ===
              LeadDistributeRuleType.mark_facebook_conversion_data_as_failed_when_tries_reached_n_times
            : rule.type ===
              LeadDistributeRuleType.mark_captured_data_as_failed_when_tries_reached_n_times,
        );
        const triggerNumber = rule?.numberOfTimes;
        if (!triggerNumber) {
          console.log(`No config number for this lead state`, state);
          return new Nack(false);
        }

        const reasonsCount = await this.countReasons(leadId, {
          state: [CareState.attempted],
        });
        let countProcess = 0;
        reasonsCount.forEach(it => {
          countProcess += +it.count;
        });
        const canTrigger = countProcess >= triggerNumber;
        if (!canTrigger) {
          console.log(`No need to trigger action`);
          return new Nack(false);
        }

        const reasonFailed = await this.careReasonRepo.findOne({
          where: {
            state: CareState.failed,
            companyId: lead?.order?.companyId,
            leadType: LeadType.normal,
          },
          order: { id: 'ASC' },
        });

        const connection = getConnection(orderConnection);
        const queryRunner = connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
          await Promise.all([
            queryRunner.manager.update(
              Lead,
              { id: leadId },
              {
                state: CareState.failed,
                updatedBy: null,
                updatedAt,
              },
            ),
            currentCareId
              ? queryRunner.manager.update(
                  LeadCare,
                  { id: currentCareId },
                  { state: CareState.failed, updatedAt, updatedBy: null },
                )
              : Promise.resolve(),
            queryRunner.manager.save(
              plainToInstance(LeadCareItem, {
                leadId,
                reasonId: reasonFailed.id,
                creatorId: null,
                leadCareId: currentCareId,
                note: 'from system',
              }),
            ),
          ]);

          await queryRunner.commitTransaction();
          await this.onLeadUpdated({
            id: leadId,
            state: CareState.failed,
            updatedAt: new Date(updatedAt),
            currentCareId,
            updatedBy: null,
          });
        } catch (error) {
          await queryRunner.rollbackTransaction();
          if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
          throw error;
        } finally {
          await queryRunner.release();
        }
        break;
      }
      case CareState.failed: {
        const rule = config.rules.find(
          rule =>
            rule.type === LeadDistributeRuleType.mark_as_canceled_when_failure_reached_n_times,
        );
        const triggerNumber = rule?.numberOfTimes;
        if (!triggerNumber) {
          console.log(`No config number for this lead state`, state);
          return new Nack(false);
        }

        const reasonsCount = await this.countReasonsForFailedStateFreshLead(leadId, {
          state: CareState.failed,
        });
        let countProcess = 0;
        reasonsCount.forEach(it => {
          countProcess += +it.count;
        });
        const canTrigger = countProcess >= triggerNumber;
        if (!canTrigger) {
          console.log(`No need to trigger action`);
          return new Nack(false);
        }

        const connection = getConnection(orderConnection);
        const queryRunner = connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
          await queryRunner.manager.update(
            Lead,
            { id: leadId },
            {
              state: CareState.lost,
              updatedBy: null,
            },
          );
          if (currentCareId)
            await queryRunner.manager.update(
              LeadCare,
              { id: currentCareId },
              { state: CareState.lost, updatedBy: null },
            );

          const updateOrderData = new UpdateOrderDto();
          updateOrderData.status = OrderStatus.Canceled;
          updateOrderData.cancelReasonText = `Order canceled due to lead cancellation.`;

          const headers = { ['country-ids']: String(lead.order.countryId) };
          const request = { user: { companyId: lead.order.companyId } };

          // hot fix: func updateOrder has a queryRunner inside so need commit queryRunner
          // before call this func, but if func updateOrder excute failed -> wrong data
          await queryRunner.commitTransaction();
          await this.ordersService.updateOrder(lead.order.id, updateOrderData, headers, request);

          await this.onLeadUpdated({
            id: leadId,
            state: CareState.lost,
            updatedAt: new Date(updatedAt),
            currentCareId,
            updatedBy: null,
          });
        } catch (error) {
          await queryRunner.rollbackTransaction();
          if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
          throw error;
        } finally {
          await queryRunner.release();
        }
        break;
      }
      default:
        break;
    }

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'after-lead-after-sale-state-updated',
    queue: 'order-process-after-lead-after-sale-state-updated',
    errorHandler: rmqErrorsHandler,
  })
  async afterLeadAfterSaleStateUpdated({
    leadId,
    state,
    updatedAt,
    currentCareId,
    currentState,
  }: {
    leadId: number;
    state: CareStateAfterSales;
    updatedAt: number;
    currentCareId?: number | null;
    currentState?: CareStateAfterSales;
  }) {
    if (!leadId || !state || !updatedAt) return new Nack(false);
    const lead = await this.leadsRepo
      .createQueryBuilder('l')
      .leftJoin('l.order', 'o')
      .addSelect([
        'o.id',
        'o.status',
        'o.companyId',
        'o.countryId',
        'o.crossCare',
        'o.fbScopedUserId',
      ])
      .where('l.id = :leadId', { leadId })
      .getOne();
    if (!lead) {
      console.log(`not found lead `, leadId);
      return new Nack(false);
    }

    if (lead.lastUpdatedState && new Date(updatedAt) < lead.lastUpdatedState) {
      console.log(
        `no need to process because updated at ${new Date(
          updatedAt,
        )} is less than last updated at ${lead.lastUpdatedState}`,
      );
      return new Nack(true);
    }

    const isConfirmedLead = CONFIRM_LEAD_STATES_AFTER_SALES.includes(state);
    if (isConfirmedLead) return new Nack(false);

    const companyId = lead.order.companyId;
    const countryId = lead.order.countryId;
    const config = await this.leadAfterSaleDistributeConfigsService.getConfigByCompanyId(
      companyId,
      countryId,
    );
    if (!config) {
      console.log(
        `No config for country ${countryId} of company ${companyId}. Therefore no need to trigger actions`,
      );
      return new Nack(false);
    }

    switch (state) {
      case CareStateAfterSales.potential: {
        if (currentState !== CareStateAfterSales.potential) {
          console.log(`No need to trigger action for data is not potential`);
          return new Nack(false);
        }
        const rule = config.rules.find(r => r.type === LeadASDistributeRuleType.processing_leads);
        const val = rule?.value?.find(
          v => v.condition === LeadASDistributeCondition.markPotentialDataAsFailed,
        );
        const triggerNumber = val?.value;
        if (!triggerNumber) {
          console.log(`No config number for this lead state`, state);
          return new Nack(false);
        }
        const reasonsCount = await this.countReasonsForPotentialStateAfterSale(leadId, {
          state: CareStateAfterSales.potential,
        });
        let countProcess = 0;
        reasonsCount.forEach(it => {
          countProcess += +it.count;
        });
        // const isFirstReasonIsAwaitingConfirm = await this.checkFirstReasonIsAwaitingConfirm(leadId);
        // if (isFirstReasonIsAwaitingConfirm) {
        //   countProcess = countProcess - 1 >= 0 ? countProcess - 1 : 0;
        // }
        countProcess = countProcess - 1 >= 0 ? countProcess - 1 : 0;
        const canTrigger = countProcess >= triggerNumber;
        if (!canTrigger) {
          console.log(`No need to trigger action`);
          return new Nack(false);
        }
        const reasonFailed = await this.careReasonRepo.findOne({
          where: {
            state: CareStateAfterSales.failed,
            companyId: lead?.order?.companyId,
            leadType: LeadType.after_sale,
          },
          order: { id: 'ASC' },
        });
        const connection = getConnection(orderConnection);
        const queryRunner = connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
          await Promise.all([
            queryRunner.manager.update(
              Lead,
              { id: leadId },
              {
                state: CareStateAfterSales.failed,
                updatedBy: null,
                updatedAt,
              },
            ),
            currentCareId
              ? queryRunner.manager.update(
                  LeadCare,
                  { id: currentCareId },
                  { state: CareStateAfterSales.failed, updatedAt, updatedBy: null },
                )
              : Promise.resolve(),
            queryRunner.manager.save(
              plainToInstance(LeadCareItem, {
                leadId,
                reasonId: reasonFailed.id,
                creatorId: null,
                leadCareId: currentCareId,
                note: 'from system',
              }),
            ),
          ]);

          await queryRunner.commitTransaction();
          await this.leadAfterSaleService.onLeadUpdated({
            id: leadId,
            state: CareStateAfterSales.failed,
            updatedAt: new Date(updatedAt),
            currentCareId,
            updatedBy: null,
          });
        } catch (error) {
          await queryRunner.rollbackTransaction();
          if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
          throw error;
        } finally {
          await queryRunner.release();
        }
        break;
      }
      case CareStateAfterSales.not_connected:
      case CareStateAfterSales.connected: {
        const rule = config.rules.find(r => r.type === LeadASDistributeRuleType.processing_leads);
        const val = rule?.value?.find(
          v => v.condition === LeadASDistributeCondition.markAttemptedDataAsFailed,
        );
        const triggerNumber = val?.value;
        if (!triggerNumber) {
          console.log(`No config number for this lead state`, state);
          return new Nack(false);
        }

        const reasonsCount = await this.countReasonForNotConnectedAndConnectedStateAfterSales(
          leadId,
        );
        let countProcess = 0;
        reasonsCount.forEach(it => {
          countProcess += +it.count;
        });
        countProcess = countProcess - 1 >= 0 ? countProcess - 1 : 0;
        const canTrigger = countProcess >= triggerNumber;
        if (!canTrigger) {
          console.log(`No need to trigger action`);
          return new Nack(false);
        }

        const reasonFailed = await this.careReasonRepo.findOne({
          where: {
            state: CareStateAfterSales.failed,
            companyId: lead?.order?.companyId,
            leadType: LeadType.after_sale,
          },
          order: { id: 'ASC' },
        });

        const connection = getConnection(orderConnection);
        const queryRunner = connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
          await Promise.all([
            queryRunner.manager.update(
              Lead,
              { id: leadId },
              {
                state: CareStateAfterSales.failed,
                updatedBy: null,
                updatedAt,
              },
            ),
            currentCareId
              ? queryRunner.manager.update(
                  LeadCare,
                  { id: currentCareId },
                  { state: CareStateAfterSales.failed, updatedAt, updatedBy: null },
                )
              : Promise.resolve(),
            queryRunner.manager.save(
              plainToInstance(LeadCareItem, {
                leadId,
                reasonId: reasonFailed.id,
                creatorId: null,
                leadCareId: currentCareId,
                note: 'from system',
              }),
            ),
          ]);

          await queryRunner.commitTransaction();
          await this.leadAfterSaleService.onLeadUpdated({
            id: leadId,
            state: CareStateAfterSales.failed,
            updatedAt: new Date(updatedAt),
            currentCareId,
            updatedBy: null,
          });
        } catch (error) {
          await queryRunner.rollbackTransaction();
          if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
          throw error;
        } finally {
          await queryRunner.release();
        }
        break;
      }
      case CareStateAfterSales.failed: {
        const rule = config.rules.find(
          rule =>
            rule.type === LeadASDistributeRuleType.mark_as_canceled_when_failure_reached_n_times,
        );
        const triggerNumber = rule?.numberOfTimes;
        if (!triggerNumber) {
          console.log(`No config number for this lead state`, state);
          return new Nack(false);
        }

        const reasonsCount = await this.countReasonsForFailedStateAfterSales(leadId, {
          state: CareStateAfterSales.failed,
        });
        let countProcess = 0;
        reasonsCount.forEach(it => {
          countProcess += +it.count;
        });
        const canTrigger = countProcess >= triggerNumber;
        if (!canTrigger) {
          console.log(`No need to trigger action`);
          return new Nack(false);
        }

        const connection = getConnection(orderConnection);
        const queryRunner = connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
          await queryRunner.manager.update(
            Lead,
            { id: leadId },
            {
              state: CareStateAfterSales.lost,
              updatedBy: null,
            },
          );
          if (currentCareId)
            await queryRunner.manager.update(
              LeadCare,
              { id: currentCareId },
              { state: CareStateAfterSales.lost, updatedBy: null },
            );

          const updateOrderData = new UpdateOrderDto();
          updateOrderData.status = OrderStatus.Canceled;
          updateOrderData.cancelReasonText = `Order canceled due to lead cancellation.`;

          const headers = { ['country-ids']: String(lead.order.countryId) };
          const request = { user: { companyId: lead.order.companyId } };
          await queryRunner.commitTransaction();
          await this.ordersService.updateOrder(lead.order.id, updateOrderData, headers, request);
          await this.leadAfterSaleService.onLeadUpdated({
            id: leadId,
            state: CareStateAfterSales.lost,
            updatedAt: new Date(updatedAt),
            currentCareId,
            updatedBy: null,
          });
        } catch (error) {
          await queryRunner.rollbackTransaction();
          if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
          throw error;
        } finally {
          await queryRunner.release();
        }
        break;
      }
      default:
        break;
    }

    return new Nack(false);
  }

  async scanLeadFromOldOrders(headers: Record<string, string>, request: Record<string, any>) {
    const orders = await this.orderRepo
      .createQueryBuilder('o')
      .leftJoin('o.externalSource', 'es')
      .leftJoin('leads', 'l', 'o.id = l.orderId')
      .where(`es.entity = :entity`, { entity: SourceEntity.landing_page })
      .andWhere('l.id IS NULL')
      .select(['o.id', 'o.displayId', 'o.customerName', 'o.customerPhone', 'o.countryId'])
      .getMany();
    for (const order of orders) {
      this.amqpConnection.publish('order-service', 'upsert-lead-after-upsert-order', {
        id: order.id,
      });
    }
    return orders;
  }

  async bulkCancelDuplicateLeads(
    body: BulkCancelDuplicateLeadsDto,
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const companyId = request.user.companyId;
    const userId = request.user.id;

    const { displayIds, reasonKey } = body;
    if (isEmpty(displayIds)) return [];

    const leads = await this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('l.order', 'o', 'o.displayId IN (:...displayIds)', { displayIds })
      .getMany();

    if (isEmpty(leads)) return [];

    const cancelReason = await this.careReasonRepo.findOne({
      where: {
        companyId,
        reasonKey,
        countryId: IsNull(),
        leadType: leads[0].leadType,
      },
    });

    if (!cancelReason) throw new BadRequestException(`Not found reason ${reasonKey}`);

    const chunkLeads = chunk(leads, 2);

    const results = [];
    for (const leads of chunkLeads) {
      const rs = await Promise.all(
        leads.map(async lead => {
          try {
            if (lead.currentCareId) {
              const gatherResult = await this.gatherLead({
                leadId: lead.id,
                state: lead.state,
                currentCareId: lead.currentCareId,
              });
              if (
                !gatherResult ||
                (gatherResult instanceof UpdateResult && !gatherResult.affected)
              ) {
                throw new BadRequestException(
                  `Cannot gather lead ${lead.id} at state ${
                    PlainCareState[lead.state]
                  } and care id is ${lead.currentCareId}`,
                );
              }
            }

            const assignResult = await this.attachUserIdToLead(
              lead.id,
              userId,
              undefined,
              lead.leadType,
              undefined,
              userId,
            );
            if (!assignResult || !(assignResult instanceof Lead))
              throw new BadRequestException(
                `Cannot assign lead id ${lead.id} to ${userId} at ${PlainCareState[lead.state]}`,
              );

            const mBody = plainToInstance(CreateLeadCareItemDto, {
              leadId: lead.id,
              leadCareId: assignResult.currentCareId,
              orderId: lead.orderId,
              reasonId: cancelReason.id,
            });
            const result = await this.createCareItem(mBody, request, headers, true);
            return result;
          } catch (error) {
            console.log(`bulk create cancel lead item error`, error);
            throw error;
          }
        }),
      );
      results.push(rs);
    }

    return new RawResponse({
      leads: instanceToPlain(leads),
      results,
    });
  }

  async bulkCancelDuplicateLeadsV2(
    body: BulkCancelDuplicateLeadsDto,
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const companyId = request.user.companyId;
    const userId = request.user.id;

    const { originDisplayId, displayIds, reasonKey, note } = body;
    if (isEmpty(displayIds)) return [];
    const leadIdCancelId = displayIds[0];

    const [lead, originLead] = await Promise.all([
      this.leadsRepo
        .createQueryBuilder('l')
        .innerJoin('l.order', 'o', 'o.displayId = :displayId', { displayId: leadIdCancelId })
        .select([
          'l.id',
          'l.userId',
          'l.state',
          'l.currentCareId',
          'l.orderId',
          'o.displayId',
          'l.leadType',
        ])
        .getOne(),
      this.leadsRepo
        .createQueryBuilder('l')
        .innerJoin('l.order', 'o', 'o.displayId = :displayId', { displayId: originDisplayId })
        .select([
          'l.id',
          'l.userId',
          'l.state',
          'l.currentCareId',
          'l.orderId',
          'o.displayId',
          'l.leadType',
        ])
        .leftJoin('l.currentCare', 'currentCare')
        .addSelect([
          'currentCare.id',
          'currentCare.createdAt',
          'currentCare.updatedAt',
          'currentCare.userId',
        ])
        .getOne(),
    ]);
    const hasPermCancel =
      PermissionUtils.checkHasPermission(
        request.user.profiles,
        originLead.leadType === LeadType.after_sale
          ? SalePermission.afterSalesLead
          : SalePermission.telesales,
        originLead.leadType === LeadType.after_sale
          ? AfterSalesLeadPermission.manualDistribute
          : TelesalesPermission.manualDistribute,
      ) || request?.user?.isAdmin;
    if (!hasPermCancel && lead?.userId !== userId)
      throw new BadRequestException({
        code: ErrorCode.LS_0004,
        message: 'User không có quyền Chia lead thao tác hủy lead của TLS khác',
      });
    if (!lead) throw new NotFoundException(`Not found lead`);

    if (
      [CareState.awaiting_stock, CareState.reconfirm, CareState.confirmed, CareState.temp].includes(
        lead.state as CareState,
      )
    )
      throw new BadRequestException(`Bạn ko thể hủy lead ở trạng thái confirm`);

    if (!originLead) throw new NotFoundException(`Not found origin lead`);
    if (lead.state === CareState.junk) throw new BadRequestException(`Lead đã bị hủy`);

    const cancelReason = await this.careReasonRepo.findOne({
      where: {
        companyId,
        reasonKey,
        countryId: IsNull(),
        leadType: lead.leadType === LeadType.after_sale ? LeadType.after_sale : LeadType.normal,
      },
    });
    if (!cancelReason) throw new BadRequestException(`Not found reason ${reasonKey}`);

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      let leadCare: LeadCare;
      // Kiểm tra lead có user đang chăm hay không
      if (!lead.currentCareId) {
        // Tạo lead care
        leadCare = plainToInstance(LeadCare, {
          userId: null,
          leadId: lead.id,
          shiftId: null,
          updatedBy: userId,
        });
        leadCare = await queryRunner.manager.save(leadCare);
      }

      const currentCareId = lead.currentCareId ? lead.currentCareId : leadCare.id;
      // Tạo lead care item
      const leadItem = plainToInstance(LeadCareItem, {
        leadId: lead.id,
        leadCareId: currentCareId,
        reasonId: cancelReason.id,
        note: note,
        creatorId: request.user.id,
      });
      const result = await queryRunner.manager.save(leadItem);

      // Cập nhật logic
      const [, , , removeDuplicateOrder] = await Promise.all([
        // Update trạng thái lead
        queryRunner.manager.update(
          Lead,
          {
            id: lead.id,
            state: Not(CareState.confirmed),
          },
          {
            state: CareState.junk,
            updatedBy: request.user.id,
            updatedAt: result.createdAt,
            lastCareItemId: result.id,
            lastCareReasonId: cancelReason.id,
          },
        ),
        // Update trạng thái lead care
        queryRunner.manager.update(
          LeadCare,
          { id: lead.currentCareId },
          {
            state: CareState.junk,
            updatedBy: request.user.id,
            updatedAt: result.createdAt,
          },
        ),
        // Update trạng thái order
        queryRunner.manager
          .createQueryBuilder()
          .update(Order)
          .set({
            status: OrderStatus.Canceled,
            cancelReasonText: `Order canceled due to lead cancellation.`,
          })
          .where('id = :orderId', { orderId: lead.orderId })
          .andWhere('status NOT IN (:...statuses)', {
            statuses: NOT_SCAN_FOR_POSSIBLE_DUPLICATES_STATUSES,
          })
          .execute(),
        // Gỡ trùng 2 order
        queryRunner.manager
          .createQueryBuilder()
          .delete()
          .from(PossibleDuplicateOrder)
          .where('orderId = :orderId and possibleDuplicateOrderId = :possibleOrderId', {
            orderId: lead.orderId,
            possibleOrderId: originLead.orderId,
          })
          .orWhere('orderId = :orderId and possibleDuplicateOrderId = :possibleOrderId', {
            orderId: originLead.orderId,
            possibleOrderId: lead.orderId,
          })
          .execute(),
        queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(RemovedDuplicateOrders)
          .values([
            {
              orderId: originLead.orderId,
              duplicateOrderId: lead.orderId,
            },
            {
              orderId: lead.orderId,
              duplicateOrderId: originLead.orderId,
            },
          ])
          .execute(),
      ]);

      if (removeDuplicateOrder.affected > 0) {
        // thêm logs gỡ trùng order
        await queryRunner.manager.save([
          plainToInstance(SystemLog, {
            tableName: 'orders',
            action: 'REMOVE_DUPLICATE',
            recordId: lead.orderId,
            changes: [
              'order_id',
              lead?.order?.displayId,
              'order_duplicate_display_ids',
              originLead?.order?.displayId,
            ],
            creatorId: userId,
          }),
          plainToInstance(SystemLog, {
            tableName: 'orders',
            action: 'REMOVE_DUPLICATE',
            recordId: originLead.orderId,
            changes: [
              'order_id',
              originLead?.order?.displayId,
              'order_duplicate_display_ids',
              lead?.order?.displayId,
            ],
            creatorId: userId,
          }),
        ]);
      }

      // Lấy danh sách các duplicate leads trước khi xóa để ghi log
      const duplicateLeads = await queryRunner.manager
        .getRepository(DuplicateLead)
        .createQueryBuilder('dl')
        .where('dl.duplicateLeadId = :id')
        .setParameters({ id: lead.id })
        .getMany();

      const systemLogs = [];

      for (const dupLead of duplicateLeads) {
        systemLogs.push(
          ...[
            plainToInstance(SystemLog, {
              tableName: 'leads',
              action: 'REMOVE_DUPLICATE',
              recordId: dupLead.leadId,
              changes: [
                'lead_id',
                dupLead.id,
                dupLead.displayId,
                'from_id',
                dupLead.duplicateLeadId,
                dupLead.duplicateDisplayId,
                request.user.id,
              ],
              creatorId: request.user.id,
            }),
            plainToInstance(SystemLog, {
              tableName: 'leads',
              action: 'REMOVE_DUPLICATE',
              recordId: dupLead.duplicateLeadId,
              changes: [
                'lead_id',
                dupLead.duplicateLeadId,
                dupLead.duplicateDisplayId,
                'from_id',
                dupLead.id,
                dupLead.displayId,
                request.user.id,
              ],
              creatorId: request.user.id,
            }),
          ],
        );
      }

      // Xóa duplicate lead
      await Promise.all([
        // Xóa các duplicate leads
        queryRunner.manager
          .createQueryBuilder()
          .delete()
          .from(DuplicateLead)
          .where('leadId = :leadId', { leadId: lead.id })
          .orWhere('duplicateLeadId = :leadId', { leadId: lead.id })
          .execute(),

        // Thêm system logs
        systemLogs.length > 0
          ? queryRunner.manager
              .createQueryBuilder()
              .insert()
              .into(SystemLog)
              .values(systemLogs)
              .execute()
          : Promise.resolve(),
      ]);

      await queryRunner.commitTransaction();

      const leadsDupWithOriginalLead = await this.duplicateLeadsRepo
        .createQueryBuilder('dl')
        .where('dl.leadId = :leadId', { leadId: originLead.id })
        .orWhere('dl.duplicateLeadId = :leadId', { leadId: originLead.id })
        .getCount();
      if (
        leadsDupWithOriginalLead === 0 &&
        originLead.currentCareId &&
        originLead.leadType !== LeadType.after_sale
      ) {
        await this.amqpConnection.publish('order-service', 'schedule-gather-lead', {
          leadId: originLead.id,
          state: originLead.state,
          updatedAt: new Date(),
          currentCareId: originLead.currentCareId,
        });
        const shifts = await this.shiftsService.getUserCurrentShiftByUserId({
          userId: originLead.currentCare.userId,
        });
        const shift = shifts?.[0];
        if (shift) {
          await queryRunner.manager
            .getRepository(LeadCare)
            .update({ id: originLead.currentCareId }, { shiftId: shift.id });
        }
      }

      // send socket to client
      await this.amqpConnection.publish('message-service', 'after-lead-updated', {
        leadId: lead.id,
      });

      return { result: true };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.log(`bulkCancelDuplicateLeadsV2 ERROR`, error);
    } finally {
      await queryRunner.release();
    }
    return { result: false };
  }

  async takeCareLeads(
    userId: number,
    companyId: number,
    countryId: string | number,
    projectIds: (string | number)[],
    request: Record<string, any>,
  ) {
    if (!countryId) throw new BadRequestException('country-ids is required');

    const timezone = 'Asia/Ho_Chi_Minh';
    const current = moment();

    // get projectIds of user
    const scopes = flatMap(request?.user?.profiles, p => p[4]);
    const projectIdsActive = scopes.map(s => String(s[1])); // get list Ids of project ['1', '2', '3', ...]
    projectIds = projectIdsActive.filter(item => projectIds.includes(item));

    const { data: user } = await this.amqpConnection.request<{ data: User }>({
      exchange: 'identity-service-roles',
      routingKey: 'get-user',
      payload: { id: userId },
      timeout: 5000,
    });
    if (!user.isOnline)
      throw new BadRequestException({
        code: ErrorCode.TLS_0001,
        message: 'Bạn đang offline!',
      });

    const [dConfig, rConfig] = await Promise.all([
      this.distributeConfigsService.getConfigByCompanyId(
        companyId,
        Number(countryId),
        LeadConfigType.distribute,
      ),
      this.distributeConfigsService.getConfigByCompanyId(
        companyId,
        Number(countryId),
        LeadConfigType.revoke,
      ),
    ]);
    if (!dConfig || !rConfig)
      throw new BadRequestException('Thị trường chưa thiết lập quy định phân phối data');

    // const leadsCount = await this.countLeads(
    //   { state: LEAD_STATES_CAN_BE_CONSIDERED_AS_ASSIGNED, userIds: [userId] },
    //   ['state'],
    //   headers,
    //   request,
    // );
    const qb = await this.getLeadsQueryBuilder(
      { state: LEAD_STATES_CAN_BE_CONSIDERED_AS_ASSIGNED, userIds: [userId] },
      undefined,
      [countryId],
      companyId,
    );

    const leadsCount = isNil(qb)
      ? []
      : await qb
          .select('COUNT(DISTINCT lead.id)', 'count')
          .addGroupBy(`lead.state`)
          .addSelect(`lead.state`, 'state')
          .getRawMany();
    const { assignedCount, noAttemptsCount } = leadsCount.reduce(
      (prev, it) => {
        if (it.state === CareState.no_attempt) prev.noAttemptsCount += Number(it.count);
        prev.assignedCount += Number(it.count);
        return prev;
      },
      { assignedCount: 0, noAttemptsCount: 0 },
    );

    // Get maximum amount of data a user can have
    const maxAssignedLeads =
      rConfig.rules
        .find(rule => rule.type === LeadDistributeRuleType.assigned_leads)
        ?.value.find(v => v.condition === LeadDistributeCondition.maxLeads)?.value || Infinity;

    const assignedAvailable = Math.max(maxAssignedLeads - assignedCount, 0);
    if (!assignedAvailable)
      throw new BadRequestException({
        code: ErrorCode.TLS_0002,
        message: 'You have reached the maximum amount of lead assignments a user can have',
      });

    const maxProcessingData =
      rConfig.rules
        .find(r => r.type === LeadDistributeRuleType.processing_leads)
        ?.value.find(v => v.condition === LeadDistributeCondition.maxLeads).value || Infinity;

    const processingAvailable = Math.max(maxProcessingData - noAttemptsCount, 0);

    const maxLimitData = Math.min(assignedAvailable, processingAvailable);

    const avgProceed = dConfig.rules.find(r => r.type === LeadDistributeRuleType.average_proceed)
      ?.numberOfTimes;
    const minLimitData = dConfig.rules.find(r => r.type === LeadDistributeRuleType.min_limit_data)
      ?.numberOfTimes;
    const coefficientNew = dConfig.rules.find(
      r => r.type === LeadDistributeRuleType.coefficient_new,
    )?.numberOfTimes;
    if (!avgProceed || !minLimitData || !coefficientNew)
      throw new BadRequestException(`Cấu hình phân phối data không hợp lệ. Vui lòng kiểm tra lại.`);

    // Thời gian còn lại ca làm việc (hr)
    let tShift = 1;

    const shifts = await this.shiftsService.getUserCurrentShiftByUserId({ userId });
    const shift = shifts?.[0];
    if (shift?.endAt) {
      const [hour, minute, second] = String(shift.endAt).split(':');

      tShift = current
        .clone()
        .tz(timezone)
        .set({
          hour: Number(hour),
          minute: Number(minute),
          second: Number(second),
        })
        .diff(current, 'hours', true);
    }

    // Số lần get care trong ngày
    const reqCount = await this.leadsRepo.query(`SELECT daily_care_sequences($1) as no`, [userId]);
    const noReq = reqCount?.[0]?.no;

    // Số data có thể xử lý
    const processable = (tShift * 60) / avgProceed;
    const percentNew = noReq * coefficientNew;
    const totalEligible = Math.floor(Math.min(Math.max(processable, minLimitData), maxLimitData));
    const newEligible = Math.ceil(totalEligible * Math.min(percentNew, 1));
    const revokedEligible = Math.max(totalEligible - newEligible, 0);

    const leadsFilter = plainToInstance(LeadsFilter, { projectIds });
    const leadsCollectTypes = dConfig.rules.find(
      r => r.type === LeadDistributeRuleType.collect_type,
    )?.value;
    if (!isEmpty(leadsCollectTypes))
      leadsFilter.collectType = leadsCollectTypes.map(t => LeadCollectType[t]);

    // eslint-disable-next-line prefer-const
    let [newLeads, revokedLeads] = await Promise.all([
      newEligible
        ? this.handleGetCares(
            Number(countryId),
            companyId,
            userId,
            newEligible,
            CareState.new,
            shift?.id,
            leadsFilter,
          )
        : 0,
      revokedEligible
        ? this.handleGetCares(
            Number(countryId),
            companyId,
            userId,
            revokedEligible,
            CareState.unassign_attempted,
            shift?.id,
            leadsFilter,
          )
        : 0,
    ]);

    const newMissing = newEligible - newLeads;
    const revokedMissing = revokedEligible - revokedLeads;
    if (newMissing && !revokedMissing) {
      revokedLeads += await this.handleGetCares(
        Number(countryId),
        companyId,
        userId,
        newMissing,
        CareState.unassign_attempted,
        shift?.id,
        leadsFilter,
      );
    }

    return {
      noReq,
      tShift,
      maxLimitData,
      minLimitData,
      processable,
      totalEligible,
      newEligible,
      revokedEligible,
      newLeads,
      revokedLeads,
    };
  }

  async handleGetCares(
    countryId: number,
    companyId: number,
    userId: number,
    limit: number,
    state: CareState = CareState.new,
    shiftId?: number,
    leadsFilter?: LeadsFilter,
  ) {
    if (![CareState.new, CareState.unassign_attempted].includes(state))
      throw new BadRequestException();
    const qbNewLead = await this.getLeadsToDistributeQueryBuilderV2(
      {
        ...leadsFilter,
        state: [state],
      },
      [countryId],
      companyId,
    );
    const qbOldLead = await this.getLeadsToDistributeQueryBuilderV2(
      {
        ...leadsFilter,
        state: [state],
      },
      [countryId],
      companyId,
    );
    const startOfDay = moment()
      .startOf('day')
      .toISOString();
    const endOfDay = moment()
      .endOf('day')
      .toISOString();
    const newLeads = isNil(qbNewLead)
      ? []
      : await qbNewLead
          .select('lead.id', 'id')
          .addSelect('lead.last_care_reason_id', 'last_care_reason_id')
          .addSelect('lead.state', 'state')
          .addSelect('lead.created_at', 'created_at')
          .addSelect(
            `
              CASE
                WHEN lead.form_captured_at IS NOT NULL THEN 'B Capture Form'
                WHEN lead.form_captured_at IS NULL AND order.fb_scoped_user_id IS NOT NULL THEN 'C Facebook Conversion'
                ELSE 'A Manual Keying'
              END AS collectType
            `,
          )
          .andWhere('lead.created_at >= :startOfDay AND lead.created_at <= :endOfDay', {
            startOfDay,
            endOfDay,
          })
          .limit(limit)
          .orderBy('collectType', 'ASC')
          // .addOrderBy('lead.last_care_reason_id', 'ASC', 'NULLS FIRST')
          .addOrderBy('lead.created_at', 'DESC')
          .getRawMany();
    let oldLeads = [];
    if (newLeads.length < limit) {
      oldLeads = isNil(qbOldLead)
        ? []
        : await qbOldLead
            .select('lead.id', 'id')
            .addSelect('lead.last_care_reason_id', 'last_care_reason_id')
            .addSelect('lead.state', 'state')
            .addSelect('lead.created_at', 'created_at')
            .addSelect(
              `
                CASE
                  WHEN lead.form_captured_at IS NOT NULL THEN 'B Capture Form'
                  WHEN lead.form_captured_at IS NULL AND order.fb_scoped_user_id IS NOT NULL THEN 'C Facebook Conversion'
                  ELSE 'A Manual Keying'
                END AS collectType
              `,
            )
            .andWhere('lead.created_at <= :startOfDay', {
              startOfDay,
            })
            .limit(limit - newLeads.length)
            .orderBy('collectType', 'ASC')
            // .addOrderBy('lead.last_care_reason_id', 'ASC', 'NULLS FIRST')
            .addOrderBy('lead.created_at', 'DESC')
            .getRawMany();
    }

    const newResults = await Promise.all(
      newLeads.map(lead => this.handleAssign(lead.id, userId, state, shiftId, leadsFilter)),
    );

    const oldResults = await Promise.all(
      oldLeads.map(lead => this.handleAssign(lead.id, userId, state, shiftId, leadsFilter)),
    );
    const newCount = newResults.reduce((prev, result) => {
      if (!result?.affected) return prev;
      return (prev += result.affected);
    }, 0);
    const oldCount = oldResults.reduce((prev, result) => {
      if (!result?.affected) return prev;
      return (prev += result.affected);
    }, 0);

    return (newCount | 0) + (oldCount | 0);
  }

  async handleAssignV2(
    leadId: number,
    userId: number,
    currentState: CareState,
    shiftId?: number,
  ): Promise<UpdateResult | undefined> {
    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const [lead, care] = await Promise.all([
        queryRunner.manager.findOne(Lead, { id: leadId }),
        queryRunner.manager.save(LeadCare, {
          userId,
          leadId,
          shiftId,
          updatedBy: userId,
        }),
      ]);
      if (!lead) {
        console.log('handleAssignV2() Error:Không tìm thấy lead');
        return;
      }
      const updatedAt = care.createdAt;

      const [result, updateOrder] = await Promise.all([
        queryRunner.manager.update(
          Lead,
          { id: leadId, userId: IsNull() },
          {
            userId,
            currentCareId: care.id,
            lastCareId: care.id,
            state: currentState,
            updatedBy: userId,
            updatedAt,
          },
        ),
        queryRunner.manager.update(
          Order,
          { id: lead.orderId },
          {
            saleId: userId,
          },
        ),
      ]);
      console.log(`handleAssignV2 result`, leadId, result, updateOrder);
      if (!result.affected || !updateOrder.affected) {
        await queryRunner.rollbackTransaction();
        return result;
      }
      await queryRunner.commitTransaction();

      await Promise.all([
        this.onLeadUpdated({
          id: leadId,
          state: currentState,
          userId,
          currentCareId: care.id,
          updatedBy: userId,
          updatedAt,
        }),
      ]);
      return result;
    } catch (error) {
      console.log(`handleAssignV2() error: `, JSON.stringify(error));
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
    return;
  }

  async handleAssign(
    leadId: number,
    userId: number,
    currentState: CareState,
    shiftId?: number,
    leadsFilter?: LeadsFilter,
  ): Promise<UpdateResult | undefined> {
    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const [lead, care] = await Promise.all([
        queryRunner.manager.findOne(Lead, { id: leadId }),
        queryRunner.manager.save(LeadCare, {
          userId,
          leadId,
          shiftId,
          updatedBy: userId,
        }),
      ]);
      if (!lead) {
        console.log('handleAssign() Error: Không tìm thấy lead');
        return;
      }
      const order = await queryRunner.manager.findOne(Order, { id: lead.orderId });
      let metadata = JSON.parse(order.metadata);
      if (!metadata || (typeof metadata === 'object' && Object.keys.length === 0)) metadata = {};
      metadata = {
        ...metadata,
        repsId: userId,
      };
      const updatedAt = care.createdAt;

      const [result, updateOrder] = await Promise.all([
        queryRunner.manager.update(
          Lead,
          { id: leadId, userId: IsNull(), state: currentState },
          {
            userId,
            currentCareId: care.id,
            lastCareId: care.id,
            state: CareState.no_attempt,
            updatedBy: userId,
            updatedAt,
          },
        ),
        queryRunner.manager.update(
          Order,
          { id: lead.orderId },
          {
            saleId: userId,
            metadata: JSON.stringify(metadata),
            lastUpdatedBy: userId,
          },
        ),
      ]);

      console.log(`handleAssign result`, leadId, result, updateOrder);
      if (!result.affected || !updateOrder.affected) {
        await queryRunner.rollbackTransaction();
        return result;
      }
      await queryRunner.commitTransaction();

      await Promise.all([
        this.onLeadUpdated({
          id: leadId,
          state: CareState.no_attempt,
          userId,
          currentCareId: care.id,
          updatedBy: userId,
          updatedAt,
        }),
        this.amqpConnection.publish(
          'order-service',
          'assign-duplicate-leads-according-to-original-lead',
          { leadId, updatedBy: userId, state: CareState.no_attempt, filter: leadsFilter },
        ),
      ]);
      return result;
    } catch (error) {
      console.log(`handleAssign() error: `, JSON.stringify(error));
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
    return;
  }

  async takeAssignedLead(
    leadId: number,
    headers: Record<string, string>,
    request: Record<string, any>,
  ): Promise<Lead> {
    const qb = this.leadsRepo
      .createQueryBuilder('leads')
      .where('leads.id = :leadId', { leadId })
      .leftJoin('leads.order', 'order')
      .addSelect(['order.id', 'order.companyId', 'order.countryId']);
    const lead = await qb.getOne();
    if (!lead) throw new NotFoundException('Không tìm thấy lead');
    if (lead.userId !== request.user.id)
      throw new ForbiddenException('Đơn hàng không ở trạng thái đã được chia cho bạn');
    if (lead.state !== CareState.assigned) return lead;

    const companyId = lead.order.companyId;
    const countryId = lead.order.countryId;
    const config = await this.distributeConfigsService.getConfigByCompanyId(companyId, countryId);
    if (config) {
      const maxProcessingData =
        config.rules
          .find(r => r.type === LeadDistributeRuleType.processing_leads)
          ?.value.find(v => v.condition === LeadDistributeCondition.maxLeads).value || Infinity;

      const processingLeads = await this.countLeads(
        { state: [CareState.no_attempt], userIds: [request.user.id] },
        ['userId'],
        headers,
        request,
      );
      const count = processingLeads.length === 0 ? 0 : processingLeads[0].count;
      if (maxProcessingData - count <= 0)
        throw new BadRequestException({
          code: ErrorCode.TLS_0003,
          message: 'Không thể nhận thêm lead vì đã đạt giới hạn lead Đang Xử Lý (Chưa chăm sóc)',
        });
    }

    const result = await this.leadsRepo.save({
      id: leadId,
      state: CareState.no_attempt,
      updatedBy: request.user.id,
    });
    console.log(`take assigned lead result`, result);
    await this.onLeadUpdated({
      id: leadId,
      state: result.state,
      currentCareId: lead.currentCareId,
      updatedBy: request.user.id,
      updatedAt: result.updatedAt,
    });
    return lead;
  }

  async createLeadsManualFilter(
    { raw, ...body }: CreateOrdersFilterDto,
    user: AuthUser,
    headers?: Record<string, string>,
  ): Promise<FilterCollection> {
    const { id: userId, companyId } = user;
    const countryId = headers['country-ids'];
    if (!countryId) throw new BadRequestException(`country-ids is required`);
    const filter = plainToInstance(FilterCollection, {
      ...body,
      raw,
      companyId,
      countryId,
      userId,
      type: FilterCollectionType.FilterFreshLead,
    });
    const result = await this.filterRepo.save(filter).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });
    return result;
  }

  async getLeadsManualFilter(
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<FilterCollection[]> {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];
    if (!countryId) throw new BadRequestException(`country-ids is required`);
    const qb = this.filterRepo
      .createQueryBuilder('f')
      .where('f.countryId = :countryId')
      .andWhere('f.companyId = :companyId')
      .andWhere('f.type IN (:...type)', { type: [FilterCollectionType.FilterFreshLead] })
      .setParameters({ companyId, countryId });

    return qb.getMany();
  }

  async deleteLeadsManualFilter(id: number): Promise<boolean> {
    const filter = await this.filterRepo.findOne({ id });
    if (!filter) throw new NotFoundException('Không tìm thấy bộ lọc yêu cầu');

    const result = await this.filterRepo.softDelete(filter.id);
    return result.affected > 0;
  }

  /**
   * Lên lịch thu hồi lead theo cấu hình sau khi lead được phân công hoặc thay đổi trạng thái
   * @param payload
   * @returns
   */
  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'schedule-gather-lead',
    queue: 'order-service-schedule-gather-lead',
    errorHandler: rmqErrorsHandler,
  })
  async scheduleGatherLead(payload: {
    leadId: number;
    state: CareState;
    updatedAt: Date;
    currentCareId: number;
  }) {
    const { leadId, state, currentCareId, updatedAt } = payload;
    if (!leadId) return new Nack(false);
    const lead = await this.leadsRepo
      .createQueryBuilder('l')
      .leftJoin('l.order', 'o')
      .addSelect(['o.id', 'o.status', 'o.companyId', 'o.countryId'])
      .leftJoin('l.currentCare', 'cc')
      .addSelect(['cc.id', 'cc.createdAt', 'cc.shiftId', 'cc.userId'])
      .where('l.id = :leadId', { leadId })
      .andWhere('l.currentCareId = :currentCareId', { currentCareId })
      .getOne();
    if (!lead) {
      console.log(`not found lead ${leadId} with ${currentCareId}`);
      return new Nack(false);
    }

    if (!GATHERABLE_STATES.includes(state)) {
      console.log(`Cannot gather lead ${leadId} because state is `, state);
      return new Nack(false);
    }

    const hasDuplicates = await this.duplicateLeadsRepo.findOne({
      where: { leadId },
      select: ['id', 'leadId', 'duplicateLeadId'],
    });
    if (hasDuplicates) {
      console.log(`scheduleGatherLead() hasDuplicates: `, JSON.stringify(hasDuplicates));
      console.log(`no need to schedule gather duplicate lead ${leadId}`);
      return new Nack(false);
    }

    const { order, currentCare } = lead;

    const timezone = 'Asia/Ho_Chi_Minh';
    let gatherTime: Moment | undefined = undefined;
    let revokeReason: string;

    const assignTime = moment(updatedAt);
    // search current shift
    const shifts = await this.shiftsService.getUserCurrentShiftByUserId({
      userId: currentCare.userId,
    });
    const shift = shifts?.[0];
    console.log(`sheduleGatherLead(): payload ${JSON.stringify(payload)}`, currentCare, shift);
    // if (!shift) return new Nack(false);

    // Thu hồi vào cuối ca hoặc nếu nhận chăm sóc ngoài giờ k có ca thì thu hồi sau 1 tiếng
    const endAt = shift?.endAt;
    let hour, minute, second;
    if (endAt) {
      [hour, minute, second] = String(endAt).split(':');
    } else {
      gatherTime = assignTime.clone().add(1, 'hours');
      revokeReason = `Thu hồi sau 1 tiếng vì care ngoài ca làm việc.`;
    }

    const companyId = order.companyId;
    const countryId = order.countryId;
    const config = await this.distributeConfigsService.getConfigByCompanyId(companyId, countryId);

    if (config && endAt) {
      switch (state) {
        case CareState.assigned: {
          const rule = config.rules.find(r => r.type === LeadDistributeRuleType.assigned_leads);
          const val = rule?.value?.find(v => v.condition === LeadDistributeCondition.returnLeadsAt);
          if (!val?.value) {
            gatherTime = assignTime
              .clone()
              .tz(timezone)
              .set({
                hour: Number(hour),
                minute: Number(minute),
                second: Number(second),
              });
            revokeReason = `Thu hồi vào cuối ca làm việc.`;
          } else {
            gatherTime = assignTime.clone().add(val.value, 'minutes');
            revokeReason = `Revoke according to condition when status is ${PlainCareState[state]} with config time: ${val.value} hours`;
          }
          break;
        }
        case CareState.no_attempt:
        case CareState.attempted:
        case CareState.potential: {
          const rule = config.rules.find(r => r.type === LeadDistributeRuleType.processing_leads);
          if (state === CareState.no_attempt) {
            const val = rule?.value?.find(
              v => v.condition === LeadDistributeCondition.returnNoAttemptedAt,
            );
            if (!val?.value) {
              gatherTime = assignTime
                .clone()
                .tz(timezone)
                .set({
                  hour: Number(hour),
                  minute: Number(minute),
                  second: Number(second),
                });
              revokeReason = `Thu hồi vào cuối ca làm việc.`;
            } else {
              gatherTime = assignTime.clone().add(val.value, 'minutes');
              revokeReason = `Revoke according to condition when status is ${PlainCareState[state]} with config time: ${val.value} hours`;
            }
            break;
          }
          if (state === CareState.attempted) {
            const val = rule?.value?.find(
              v => v.condition === LeadDistributeCondition.returnAttemptedAt,
            );
            if (!val?.value) {
              gatherTime = assignTime
                .clone()
                .tz(timezone)
                .set({
                  hour: Number(hour),
                  minute: Number(minute),
                  second: Number(second),
                });
              revokeReason = `Thu hồi vào cuối ca làm việc.`;
            } else {
              gatherTime = assignTime.clone().add(val.value, 'minutes');
              revokeReason = `Revoke according to condition when status is ${PlainCareState[state]} with config time: ${val.value} hours`;
            }
            break;
          }
          if (state === CareState.potential) {
            const val = rule?.value?.find(
              v => v.condition === LeadDistributeCondition.returnPotentialAt,
            );
            if (!val?.value) {
              gatherTime = assignTime
                .clone()
                .tz(timezone)
                .set({
                  hour: Number(hour),
                  minute: Number(minute),
                  second: Number(second),
                });
              revokeReason = `Thu hồi vào cuối ca làm việc.`;
            } else {
              gatherTime = assignTime.clone().add(val.value, 'minutes');
              revokeReason = `Revoke according to condition when status is ${PlainCareState[state]} with config time: ${val.value} hours`;
            }
            break;
          }
        }
        case CareState.failed: {
          const rule = config.rules.find(r => r.type === LeadDistributeRuleType.failed_leads);
          const val = rule?.value?.find(v => v.condition === LeadDistributeCondition.returnLeadsAt);
          if (!val?.value) {
            gatherTime = assignTime
              .clone()
              .tz(timezone)
              .set({
                hour: Number(hour),
                minute: Number(minute),
                second: Number(second),
              });
            revokeReason = `Thu hồi vào cuối ca làm việc.`;
          } else {
            gatherTime = assignTime.clone().add(val.value, 'minutes');
            revokeReason = `Revoke according to condition when status is ${PlainCareState[state]} with config time: ${val.value} hours`;
          }
          break;
        }
        default:
          break;
      }
    }
    const jobId = `leaId-${leadId}-${currentCareId}`;
    // if exist job out of shift -> no need add more job
    const job = await this.orderQueue.getJob(jobId);
    if (job && !currentCare.shiftId) {
      console.log(
        `no need to schedule gather lead ${leadId} because job(out of shift) is already exist`,
      );
      return new Nack(false);
    }

    console.log(
      `gatherTime for lead id ${leadId} at lead state ${lead.state}`,
      gatherTime.toDate(),
      `reason`,
      revokeReason,
    );
    if (!gatherTime) return new Nack(false);

    const jobName = 'gather-lead';
    const data = {
      leadId,
      state,
      currentCareId,
    };

    await (await this.orderQueue.getJob(jobId))?.remove();
    await this.orderQueue.add(jobName, data, {
      jobId,
      timestamp: assignTime.valueOf(),
      delay: gatherTime.valueOf() - assignTime.valueOf(),
      removeOnComplete: true,
      removeOnFail: false,
    });
    return new Nack(true);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'schedule-gather-lead-after-sale',
    queue: 'order-service-schedule-gather-lead-after-sale',
    errorHandler: rmqErrorsHandler,
  })
  async scheduleGatherLeadAfterSale(payload: {
    leadId: number;
    state: CareStateAfterSales;
    updatedAt: Date;
    currentCareId: number;
  }) {
    const { leadId, state, currentCareId, updatedAt } = payload;
    if (!leadId) return new Nack(false);
    const lead = await this.leadsRepo
      .createQueryBuilder('l')
      .leftJoin('l.order', 'o')
      .addSelect(['o.id', 'o.status', 'o.companyId', 'o.countryId'])
      .leftJoin('l.currentCare', 'cc')
      .addSelect(['cc.id', 'cc.createdAt', 'cc.shiftId'])
      .where('l.id = :leadId', { leadId })
      .andWhere('l.currentCareId = :currentCareId', { currentCareId })
      .getOne();
    if (!lead) {
      console.log(`not found lead ${leadId} with ${currentCareId}`);
      return new Nack(false);
    }

    if (!GATHERABLE_STATES_AFTER_SALES.includes(state)) {
      console.log(`Cannot gather lead ${leadId} because state is `, state);
      return new Nack(false);
    }

    const hasDuplicates = await this.duplicateLeadsRepo.findOne({
      where: { leadId },
      select: ['id'],
    });
    if (hasDuplicates) {
      console.log(`no need to schedule gather duplicate lead ${leadId}`);
      return new Nack(false);
    }

    const { order, currentCare } = lead;

    const timezone = 'Asia/Ho_Chi_Minh';
    let gatherTime: Moment | undefined;
    let revokeReason: string;

    const assignTime = moment(updatedAt);
    const shift = await this.shiftsService.findShiftById(currentCare.shiftId);
    // if (!shift) return new Nack(false);

    // Thu hồi vào cuối ca hoặc nếu nhận chăm sóc ngoài giờ k có ca thì thu hồi sau 1 tiếng
    const endAt = shift?.endAt;
    if (endAt) {
      const [hour, minute, second] = String(endAt).split(':');
      gatherTime = assignTime
        .clone()
        .tz(timezone)
        .set({
          hour: Number(hour),
          minute: Number(minute),
          second: Number(second),
        });
      revokeReason = `Thu hồi vào cuối ca làm việc.`;
    } else {
      gatherTime = assignTime.clone().add(1, 'hours');
      revokeReason = `Thu hồi sau 1 tiếng vì care ngoài ca làm việc.`;
    }

    const companyId = order.companyId;
    const countryId = order.countryId;
    const config = await this.leadAfterSaleDistributeConfigsService.getConfigByCompanyId(
      companyId,
      countryId,
    );

    if (config) {
      switch (state) {
        case CareStateAfterSales.assigned: {
          const rule = config.rules.find(r => r.type === LeadASDistributeRuleType.assigned_leads);
          const val = rule?.value?.find(
            v => v.condition === LeadASDistributeCondition.returnLeadsAt,
          );
          if (typeof val?.value === 'boolean' && !val?.value) gatherTime = undefined;
          else if (typeof val?.value === 'boolean' && val?.value) break;
          else gatherTime = assignTime.clone().add(val.value, 'minutes');
          revokeReason = `Revoke according to condition when status is ${PlainCareState[state]} with config time: ${val.value} hours`;
          break;
        }
        case CareStateAfterSales.no_attempt:
        case CareStateAfterSales.not_connected:
        case CareStateAfterSales.connected:
        case CareStateAfterSales.potential: {
          const rule = config.rules.find(r => r.type === LeadASDistributeRuleType.processing_leads);
          if (state === CareStateAfterSales.no_attempt) {
            const val = rule?.value?.find(
              v => v.condition === LeadASDistributeCondition.returnNoAttemptedAt,
            );
            if (typeof val?.value === 'boolean' && !val?.value) gatherTime = undefined;
            else if (typeof val?.value === 'boolean' && val?.value) break;
            else gatherTime = assignTime.clone().add(val.value, 'minutes');
            revokeReason = `Revoke according to condition when status is ${PlainCareState[state]} with config time: ${val.value} hours`;
            break;
          }
          if (
            state === CareStateAfterSales.not_connected ||
            state === CareStateAfterSales.connected
          ) {
            const val = rule?.value?.find(
              v => v.condition === LeadASDistributeCondition.returnAttemptedAt,
            );
            if (typeof val?.value === 'boolean' && !val?.value) gatherTime = undefined;
            else if (typeof val?.value === 'boolean' && val?.value) break;
            else gatherTime = assignTime.clone().add(val.value, 'minutes');
            revokeReason = `Revoke according to condition when status is ${PlainCareState[state]} with config time: ${val.value} hours`;
            break;
          }
          if (state === CareStateAfterSales.potential) {
            const val = rule?.value?.find(
              v => v.condition === LeadASDistributeCondition.returnPotentialAt,
            );
            if (typeof val?.value === 'boolean' && !val?.value) gatherTime = undefined;
            else if (typeof val?.value === 'boolean' && val?.value) break;
            else gatherTime = assignTime.clone().add(val.value, 'minutes');
            revokeReason = `Revoke according to condition when status is ${PlainCareState[state]} with config time: ${val.value} hours`;
            break;
          }
        }
        case CareStateAfterSales.failed: {
          const rule = config.rules.find(r => r.type === LeadASDistributeRuleType.failed_leads);
          const val = rule?.value?.find(
            v => v.condition === LeadASDistributeCondition.returnLeadsAt,
          );
          if (typeof val?.value === 'boolean' && !val?.value) gatherTime = undefined;
          else if (typeof val?.value === 'boolean' && val?.value) break;
          else gatherTime = assignTime.clone().add(val.value, 'minutes');
          revokeReason = `Revoke according to condition when status is ${PlainCareState[state]} with config time: ${val.value} hours`;
          break;
        }
        default:
          break;
      }
    }

    console.log(
      `gatherTime for lead id ${leadId} at lead state ${lead.state}`,
      gatherTime ? gatherTime.toDate() : '',
      `reason`,
      revokeReason,
    );
    if (!gatherTime || gatherTime === undefined) return new Nack(false);

    const jobName = 'gather-lead-after-sale';
    const data = {
      leadId,
      state,
      currentCareId,
    };
    const jobId = `leaId-${leadId}-${currentCareId}`;
    await (await this.orderQueue.getJob(jobId))?.remove();
    await this.orderQueue.add(jobName, data, {
      jobId,
      timestamp: assignTime.valueOf(),
      delay: gatherTime.valueOf() - assignTime.valueOf(),
      removeOnComplete: true,
      removeOnFail: false,
    });
    return new Nack(true);
  }

  /**
   * Function thực hiện thu hồi lead
   * @param data
   * @returns
   */
  async gatherLead(data) {
    const { leadId, state, currentCareId } = data;
    const lead = await this.leadsRepo.findOne({
      where: { id: leadId },
      select: ['id', 'state'],
    });
    console.log(`execute gather lead id ${data.leadId}`, data, lead);
    if (!GATHERABLE_STATES.includes(lead.state as any)) {
      console.log(`Not allow to gather lead because state is `, state);
      return new Nack(false);
    }

    const updatedAt = new Date();
    const result = await this.leadsRepo
      .createQueryBuilder('l')
      .update()
      .set({
        state: CareState.unassign_attempted,
        userId: null,
        currentCareId: null,
        updatedBy: null,
        updatedAt,
      })
      .where({ id: leadId, currentCareId })
      .andWhere(`NOT EXISTS (SELECT lead_id FROM duplicate_leads WHERE lead_id = ${leadId})`)
      .execute();
    console.log(`result of execute gather lead id ${data.leadId}`, result);
    if (result.affected) {
      await this.onLeadUpdated({
        id: leadId,
        state: CareState.unassign_attempted,
        userId: null,
        currentCareId: null,
        updatedBy: null,
        updatedAt,
      });
    }
    return result;
  }

  async gatherLeadAfterSale(data) {
    console.log(`execute gather lead after sale id ${data.leadId}`, data);
    const { leadId, state, currentCareId } = data;

    if (!GATHERABLE_STATES_AFTER_SALES.includes(state)) {
      console.log(`Not allow to gather lead after sale because state is `, state);
      return new Nack(false);
    }

    const updatedAt = new Date();
    const result = await this.leadsRepo
      .createQueryBuilder('l')
      .update()
      .set({
        state: CareStateAfterSales.unassign_attempted,
        userId: null,
        currentCareId: null,
        updatedBy: null,
        updatedAt,
      })
      .where({ id: leadId, state, currentCareId })
      .andWhere(`NOT EXISTS (SELECT lead_id FROM duplicate_leads WHERE lead_id = ${leadId})`)
      .execute();
    console.log(`result of execute gather lead after sale id ${data.leadId}`, result);
    if (result.affected) {
      await this.onLeadUpdated({
        id: leadId,
        state: CareStateAfterSales.unassign_attempted,
        userId: null,
        currentCareId: null,
        updatedBy: null,
        updatedAt,
      });
    }
    return result;
  }

  /**
   * Đồng bộ người chăm sóc của lead sang người chăm sóc đơn khi người chăm sóc lead thay đổi
   * @returns
   */
  async syncLeadUserIdToOrderInCharge() {
    const qb = this.leadsRepo
      .createQueryBuilder('l')
      .leftJoin('l.order', 'o')
      .where('(o.saleId != l.userId OR (o.saleId IS NULL AND l.userId IS NOT NULL))')
      .andWhere('(l.lead_type = :leadType OR l.lead_type IS NULL)', {
        leadType: LeadType.normal,
      })
      .select(['l.id', 'l.orderId', 'l.userId', 'l.currentCareId'])
      .addSelect(['o.id', 'o.saleId']);
    const leads = await qb.getMany();

    for (const lead of leads) {
      await this.amqpConnection.publish('order-service', 'after-lead-user-id-updated', {
        leadId: lead.id,
        currentCareId: lead.currentCareId,
        orderId: lead.orderId,
        userId: lead.userId,
      });
    }
    return leads;
  }

  async findOriginalLeadById(leadId: number, getEntity?: boolean) {
    const connection = getConnection(orderConnection);
    const subQb = connection
      .createQueryBuilder()
      .select(
        'DISTINCT (CASE WHEN pdl.lead_id < pdl.min_dup_lead_id THEN pdl.lead_id ELSE pdl.min_dup_lead_id END)',
        'origin_id',
      )
      .from(sqb => {
        return sqb
          .select('lead_id')
          .addSelect('MIN(duplicate_lead_id)', 'min_dup_lead_id')
          .from('duplicate_leads', 'pdl')
          .where('lead_id = :leadId')
          .groupBy('lead_id')
          .setParameters({ leadId });
      }, 'pdl');
    if (getEntity) {
      return this.leadsRepo
        .createQueryBuilder('l')
        .where(`l.id IN (${subQb.getQuery()})`)
        .setParameters(subQb.getParameters())
        .getOne();
    }

    return subQb.getRawOne();
  }

  /**
   * Manual function để scan và xóa những lead trùng không đúng
   * @returns
   */
  async deleteIncorrectPossibleDuplicates() {
    const qb = this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('l.order', 'o')
      .innerJoin('l.duplicateLeads', 'dupLeads')
      .where('(l.ignoreDuplicateWarning = TRUE OR o.status IN (:...status))', {
        status: NOT_SCAN_FOR_POSSIBLE_DUPLICATES_STATUSES,
      })
      .andWhere('(l.lead_type = :leadType OR l.lead_type IS NULL)', {
        leadType: LeadType.normal,
      })
      .addSelect(['o.id', 'o.status', 'dupLeads.id']);

    const leads = await qb.getMany();
    const now = new Date();
    for (const lead of leads) {
      await this.amqpConnection.publish('order-service', 'ignore-duplicate-leads-warning', {
        orderId: lead.orderId,
        updatedAt: now,
      });
    }
    return leads;
  }

  /**
   * Scan các lead có thể trùng sau khi lead được tạo
   * @param param0
   * @returns
   */
  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'scan-possible-duplicate-leads',
    queue: 'order-service-scan-possible-duplicate-leads',
    errorHandler: rmqErrorsHandler,
  })
  async scanPossibleDuplicateLeads({ orderId, userId }: { orderId: number; userId?: number }) {
    if (!orderId) return new Nack(false);
    const uid = uuid();

    const lead = await this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('l.order', 'o')
      .leftJoin('l.duplicateLeads', 'dupLeads')
      .addSelect([
        'o.id',
        'o.displayId',
        'o.customerPhone',
        'o.countryId',
        'o.companyId',
        'o.projectId',
        'o.status',
        'o.crossCare',
        'o.fbScopedUserId',
      ])
      .addSelect(['dupLeads.id', 'dupLeads.duplicateLeadId', 'dupLeads.displayId'])
      .where('l.orderId = :orderId', { orderId })
      .andWhere('(l.lead_type = :leadType OR l.lead_type IS NULL)', {
        leadType: LeadType.normal,
      })
      .getOne();

    console.log(`${uid} find lead result with order ID ${orderId}`, lead);
    if (!lead || NOT_SCAN_FOR_POSSIBLE_DUPLICATES_STATUSES.includes(lead.order?.status))
      return new Nack(false);

    console.log(
      `${uid} lead is going to scan ${lead?.id} order ${lead?.orderId} status:`,
      lead?.order?.status,
    );

    const scanPhones = [lead.order.customerPhone, lead.order.customerPhone.replace(/^0+/, '')];

    const qb = this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin(
        'l.order',
        'o',
        'o.companyId = :companyId AND o.countryId = :countryId AND o.projectId = :projectId',
      )
      .leftJoin('l.duplicateLeads', 'dupLeads')
      .where('l.orderId != :orderId')
      .andWhere('(l.lead_type = :leadType OR l.lead_type IS NULL)', {
        leadType: LeadType.normal,
      })
      .andWhere('l.ignoreDuplicateWarning = FALSE')
      .andWhere('o.status NOT IN (:...statuses)')
      .andWhere(
        new Brackets(sqb => {
          sqb.where(
            `(o.customerPhone IS NOT NULL AND o.customerPhone != '' AND o.customerPhone IN (:...phones))`,
          );
          if (lead.order?.crossCare && lead.order?.fbScopedUserId)
            sqb.orWhere('(o.crossCare = TRUE AND o.fbScopedUserId = :fbScopedUserId)', {
              fbScopedUserId: lead.order.fbScopedUserId,
            });
        }),
      )
      .andWhere(
        'l.id NOT IN (SELECT rdl.duplicate_lead_id FROM removed_duplicate_leads rdl WHERE rdl.lead_id = :leadId)',
        { leadId: lead.id },
      )
      .select(['l.id', 'l.userId', 'l.createdAt', 'l.currentCareId'])
      .addSelect(['o.id', 'o.displayId', 'o.customerPhone', 'o.companyId', 'o.projectId'])
      .addSelect(['dupLeads.id', 'dupLeads.duplicateLeadId', 'dupLeads.duplicateDisplayId'])
      .setParameters({
        orderId,
        statuses: NOT_SCAN_FOR_POSSIBLE_DUPLICATES_STATUSES,
        companyId: lead.order.companyId,
        countryId: lead.order.countryId,
        projectId: lead.order.projectId,
        phones: scanPhones,
      });
    const possibleDuplicateLeads = await qb.getMany();
    console.log(`scanPossibleDuplicateLeads() possibleDuplicateLeads: `, possibleDuplicateLeads);
    if (isEmpty(possibleDuplicateLeads)) {
      // assign lead to user
      if (userId) {
        const shifts = await this.shiftsService.getUserCurrentShiftByUserId({ userId });
        const shift = shifts?.[0];

        const procedureConfig = await this.distributeConfigsService.getConfigByCompanyId(
          lead.order.companyId,
          Number(lead.order.countryId),
          LeadConfigType.processing_procedure,
        );

        const assignedRule = procedureConfig?.rules?.find(
          r => r.type === LeadDistributeRuleType.assigned_leads,
        );
        const skipAssigned = (assignedRule?.value as LeadDistributeRuleValue[])?.find(
          v => v.condition === LeadDistributeCondition.changeCareStateToInProcessAutomatically,
        )?.value;

        await this.handleAssignV2(
          lead.id,
          userId,
          skipAssigned ? CareState.no_attempt : CareState.assigned,
          shift?.id,
        );
      }
      return new Nack(false);
    }
    const leadsNeedRemoveJobGather: {
      leadId: number;
      currentCareId: number;
    }[] = [];
    const dupRecords: DuplicateLead[] = possibleDuplicateLeads.reduce((prev, dupLead) => {
      // Check and add possible duplicates to current scanning lead
      const index = lead.duplicateLeads.findIndex(it => it.duplicateLeadId === dupLead.id);
      if (index === -1) {
        prev.push(
          plainToInstance(DuplicateLead, {
            leadId: lead.id,
            orderId: lead.order.id,
            displayId: lead.order.displayId,
            duplicateLeadId: dupLead.id,
            duplicateDisplayId: dupLead.order.displayId,
          }),
        );
        leadsNeedRemoveJobGather.push({
          leadId: lead.id,
          currentCareId: lead.currentCareId,
        });
      }

      // Check and add current scanning lead to other possible duplicates
      const idx = dupLead.duplicateLeads.findIndex(it => it.duplicateLeadId === lead.id);
      if (idx !== -1) return prev;
      prev.push(
        plainToInstance(DuplicateLead, {
          leadId: dupLead.id,
          orderId: dupLead.order.id,
          displayId: dupLead.order.displayId,
          duplicateLeadId: lead.id,
          duplicateDisplayId: lead.order.displayId,
        }),
      );
      leadsNeedRemoveJobGather.push({
        leadId: dupLead.id,
        currentCareId: dupLead.currentCareId,
      });
      return prev;
    }, []);
    // const result = await this.duplicateLeadsRepo.upsert(dupRecords, ['leadId', 'duplicateLeadId']);
    console.log(`scanPossibleDuplicateLeads() dupRecords: `, dupRecords);
    const result = await this.duplicateLeadsRepo
      .createQueryBuilder()
      .insert()
      .values(dupRecords)
      .orIgnore()
      .returning(['id', 'leadId'])
      .execute();

    for (const l of leadsNeedRemoveJobGather) {
      console.log(`remove job gather for lead ${l.leadId} currentCareId ${l.currentCareId}`);
      await (await this.orderQueue.getJob(`leaId-${l.leadId}-${l.currentCareId}`))?.remove();
    }

    // gán lead cho user
    if (userId) {
      const shifts = await this.shiftsService.getUserCurrentShiftByUserId({ userId });
      const shift = shifts?.[0];
      await this.handleAssignV2(lead.id, userId, CareState.no_attempt, shift?.id);
    } else {
      const userIdAssign = this.getUserIdWithMaxCreatedAt(possibleDuplicateLeads);
      if (userIdAssign) {
        const shifts = await this.shiftsService.getUserCurrentShiftByUserId({
          userId: userIdAssign,
        });
        const shift = shifts?.[0];
        await this.handleAssignV2(lead.id, userIdAssign, CareState.no_attempt, shift?.id);
      }
    }
    // const originalLead: Lead = await this.findOriginalLeadById(lead.id, true);
    // console.log(`${uid} originalLead`, originalLead);
    // if (originalLead)
    //   await this.amqpConnection.publish(
    //     'order-service',
    //     'assign-duplicate-leads-according-to-original-lead',
    //     { leadId: originalLead.id, updatedBy: originalLead.updatedBy, state: originalLead.state },
    //   );
    await this.amqpConnection.publish('message-service', 'after-lead-updated', {
      leadId: lead?.id,
    });
    return new RawResponse(result);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'scan-possible-duplicate-leads-after-sale',
    queue: 'order-service-scan-possible-duplicate-leads-after-sale',
    errorHandler: rmqErrorsHandler,
  })
  async scanPossibleDuplicateLeadsAfterSale({
    orderId,
    userId,
  }: {
    orderId: number;
    userId?: number;
  }) {
    if (!orderId) return new Nack(false);
    const uid = uuid();
    console.log(`Starting to scan possible duplicate lead with orderId ${orderId}`);
    const lead = await this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('l.order', 'o')
      .leftJoin('l.duplicateLeads', 'dupLeads')
      .addSelect([
        'o.id',
        'o.displayId',
        'o.customerPhone',
        'o.countryId',
        'o.companyId',
        'o.projectId',
        'o.status',
        'o.crossCare',
        'o.fbScopedUserId',
      ])
      .addSelect(['dupLeads.id', 'dupLeads.duplicateLeadId', 'dupLeads.displayId'])
      .where('l.orderId = :orderId', { orderId })
      .andWhere('l.leadType = :leadType', { leadType: LeadType.after_sale })
      .getOne();

    console.log(`${uid} find lead result with order ID ${orderId}`, lead);
    if (!lead || NOT_SCAN_FOR_POSSIBLE_DUPLICATES_STATUSES.includes(lead.order?.status))
      return new Nack(false);

    console.log(
      `${uid} lead is going to scan ${lead?.id} order ${lead?.orderId} status:`,
      lead?.order?.status,
    );

    const scanPhones = [lead.order.customerPhone, lead.order.customerPhone.replace(/^0+/, '')];

    const qb = this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin(
        'l.order',
        'o',
        'o.companyId = :companyId AND o.countryId = :countryId AND o.projectId = :projectId',
      )
      .leftJoin('l.duplicateLeads', 'dupLeads')
      .where('l.orderId != :orderId')
      .andWhere('l.leadType = :leadType', { leadType: LeadType.after_sale })
      .andWhere('l.ignoreDuplicateWarning = FALSE')
      .andWhere('o.status NOT IN (:...statuses)')
      .andWhere(
        new Brackets(sqb => {
          sqb.where(
            `(o.customerPhone IS NOT NULL AND o.customerPhone != '' AND o.customerPhone IN (:...phones))`,
          );
          if (lead.order?.crossCare && lead.order?.fbScopedUserId)
            sqb.orWhere('(o.crossCare = TRUE AND o.fbScopedUserId = :fbScopedUserId)', {
              fbScopedUserId: lead.order.fbScopedUserId,
            });
        }),
      )
      .andWhere(
        'l.id NOT IN (SELECT rdl.duplicate_lead_id FROM removed_duplicate_leads rdl WHERE rdl.lead_id = :leadId)',
        { leadId: lead.id },
      )
      .select(['l.id', 'l.userId', 'l.createdAt'])
      .addSelect(['o.id', 'o.displayId', 'o.customerPhone', 'o.companyId', 'o.projectId'])
      .addSelect(['dupLeads.id', 'dupLeads.duplicateLeadId', 'dupLeads.duplicateDisplayId']);
    if (lead.projectId) {
      qb.setParameters({
        orderId,
        statuses: NOT_SCAN_FOR_POSSIBLE_DUPLICATES_STATUSES,
        companyId: lead.order.companyId,
        countryId: lead.order.countryId,
        projectId: lead.order.projectId,
        phones: scanPhones,
      });
    } else {
      qb.setParameters({
        orderId,
        statuses: NOT_SCAN_FOR_POSSIBLE_DUPLICATES_STATUSES,
        companyId: lead.order.companyId,
        countryId: lead.order.countryId,
        phones: scanPhones,
      });
    }
    // console.log(`qb`, qb.getQueryAndParameters());
    const possibleDuplicateLeads = await qb.getMany();
    if (isEmpty(possibleDuplicateLeads)) {
      // assign lead to user
      if (userId) {
        const shifts = await this.shiftsService.getUserCurrentShiftByUserId({ userId });
        const shift = shifts?.[0];

        const procedureConfig = await this.distributeConfigsService.getConfigByCompanyId(
          lead.order.companyId,
          Number(lead.order.countryId),
          LeadConfigType.processing_procedure,
        );

        const assignedRule = procedureConfig?.rules?.find(
          r => r.type === LeadDistributeRuleType.assigned_leads,
        );
        const skipAssigned = (assignedRule?.value as LeadDistributeRuleValue[])?.find(
          v => v.condition === LeadDistributeCondition.changeCareStateToInProcessAutomatically,
        )?.value;

        await this.handleAssignV2(
          lead.id,
          userId,
          skipAssigned ? CareState.no_attempt : CareState.assigned,
          shift?.id,
        );
      }
      return new Nack(false);
    }
    const dupRecords: DuplicateLead[] = possibleDuplicateLeads.reduce((prev, dupLead) => {
      // Check and add possible duplicates to current scanning lead
      const index = lead.duplicateLeads.findIndex(it => it.duplicateLeadId === dupLead.id);
      if (index === -1) {
        prev.push(
          plainToInstance(DuplicateLead, {
            leadId: lead.id,
            orderId: lead.order.id,
            displayId: lead.order.displayId,
            duplicateLeadId: dupLead.id,
            duplicateDisplayId: dupLead.order.displayId,
          }),
        );
      }

      // Check and add current scanning lead to other possible duplicates
      const idx = dupLead.duplicateLeads.findIndex(it => it.duplicateLeadId === lead.id);
      if (idx !== -1) return prev;
      prev.push(
        plainToInstance(DuplicateLead, {
          leadId: dupLead.id,
          orderId: dupLead.order.id,
          displayId: dupLead.order.displayId,
          duplicateLeadId: lead.id,
          duplicateDisplayId: lead.order.displayId,
        }),
      );
      return prev;
    }, []);
    console.log(dupRecords);
    // const result = await this.duplicateLeadsRepo.upsert(dupRecords, ['leadId', 'duplicateLeadId']);
    const result = await this.duplicateLeadsRepo
      .createQueryBuilder()
      .insert()
      .values(dupRecords)
      .orIgnore()
      .returning(['id', 'leadId'])
      .execute();

    // gán lead cho user
    if (userId) {
      await this.handleAssignV2(lead.id, userId, CareState.no_attempt, null);
    } else {
      const userIdAssign = this.getUserIdWithMaxCreatedAt(possibleDuplicateLeads);
      if (userIdAssign)
        await this.handleAssignV2(lead.id, userIdAssign, CareState.no_attempt, null);
    }
    // const originalLead: Lead = await this.findOriginalLeadById(lead.id, true);
    // console.log(`${uid} originalLead`, originalLead);
    // if (originalLead)
    //   await this.amqpConnection.publish(
    //     'order-service',
    //     'assign-duplicate-leads-according-to-original-lead',
    //     { leadId: originalLead.id, updatedBy: originalLead.updatedBy, state: originalLead.state },
    //   );
    await this.amqpConnection.publish('message-service', 'after-lead-updated', {
      leadId: lead?.id,
    });
    return new RawResponse(result);
  }

  getUserIdWithMaxCreatedAt(array) {
    if (!array || array.length === 0) {
      return null; // Return null if the array is empty or invalid
    }

    // Filter the array for elements where userId is not null
    const filteredArray = array.filter(item => item.userId !== null);

    if (filteredArray.length === 0) {
      return null; // Return null if no elements meet the criteria
    }

    // Find the element with the maximum createdAt
    const maxCreatedAtElement = filteredArray.reduce((max, item) =>
      new Date(item.createdAt) > new Date(max.createdAt) ? item : max,
    );
    return maxCreatedAtElement ? maxCreatedAtElement.userId : null;
  }

  /**
   * Xử lý xóa các bản ghi trong bảng duplicate_leads sau khi lead được đánh dấu là bỏ qua cảnh báo trùng
   * @param param0
   * @returns
   */
  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'ignore-duplicate-leads-warning',
    queue: 'order-service-handling-ignore-duplicate-leads-warning',
    errorHandler: rmqErrorsHandler,
  })
  async handleIgnorePossibleDuplicateWarning(payload: {
    orderId: number;
    updatedBy?: number;
    updatedAt: Date;
  }) {
    const { orderId, updatedBy, updatedAt } = payload;
    console.log(`handleIgnorePossibleDuplicateWarning() orderId: ${orderId}`);
    if (!orderId) return new Nack();
    const lead = await this.leadsRepo.findOne({ where: { orderId } });
    if (!lead) return new Nack();

    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Lấy danh sách các duplicate leads trước khi xóa để ghi log
      const duplicateLeads = await this.duplicateLeadsRepo
        .createQueryBuilder('dl')
        .where('dl.leadId = :id')
        .orWhere('dl.duplicateLeadId = :id')
        .setParameters({ id: lead.id })
        .getMany();

      const systemLogs = [];
      const leadTargetIds = [];
      for (const dupLead of duplicateLeads) {
        if (dupLead.leadId !== lead.id && !leadTargetIds.includes(dupLead.leadId)) {
          leadTargetIds.push(dupLead.leadId);
        }
        if (
          dupLead.duplicateLeadId !== lead.id &&
          !leadTargetIds.includes(dupLead.duplicateLeadId)
        ) {
          leadTargetIds.push(dupLead.duplicateLeadId);
        }
        systemLogs.push(
          plainToInstance(SystemLog, {
            tableName: 'leads',
            action: 'REMOVE_DUPLICATE',
            recordId: dupLead.leadId,
            changes: [
              'lead_id',
              dupLead.id,
              dupLead.displayId,
              'from_id',
              dupLead.duplicateLeadId,
              dupLead.duplicateDisplayId,
              updatedBy,
            ],
            creatorId: updatedBy,
          }),
        );
      }

      const [result] = await Promise.all([
        // Xóa các duplicate leads
        queryRunner.manager
          .createQueryBuilder(DuplicateLead, 'dl')
          .delete()
          .where('leadId = :id')
          .orWhere('duplicateLeadId = :id')
          .setParameters({ id: lead.id })
          .execute(),

        // Thêm system logs
        systemLogs.length > 0
          ? queryRunner.manager
              .createQueryBuilder()
              .insert()
              .into(SystemLog)
              .values(systemLogs)
              .execute()
          : Promise.resolve(),
      ]);

      await queryRunner.commitTransaction();
      // Gửi thông báo để cập nhật lead
      console.log(`delete possible duplicate leads result`, result);
      if (result.affected > 0) {
        await this.amqpConnection.publish('order-service', 'schedule-gather-lead', {
          leadId: lead.id,
          state: lead.state,
          updatedAt,
          currentCareId: lead.currentCareId,
        });
      }

      if (result.affected > 0 && leadTargetIds.length > 0) {
        const leadsTarget = await this.leadsRepo
          .createQueryBuilder('l')
          .leftJoinAndSelect('l.currentCare', 'currentCare')
          .where('l.id IN (:...leadTargetIds)', { leadTargetIds })
          .getMany();
        const promise = [];
        for (const it of leadsTarget) {
          if (it.currentCareId && it.leadType !== LeadType.after_sale) {
            const shift = await this.shiftsService.getUserCurrentShiftByUserId({
              userId: it.currentCare.userId,
            });
            if (shift?.[0]) {
              promise.push(
                this.leadCaresRepo.update({ id: it.currentCareId }, { shiftId: shift[0].id }),
              );
            }
            promise.push(
              this.amqpConnection.publish('order-service', 'schedule-gather-lead', {
                leadId: it.id,
                state: it.state,
                updatedAt: new Date(),
                currentCareId: it.currentCareId,
              }),
            );
          }
        }
        if (promise.length > 0) await Promise.all(promise);
      }
      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('handleIgnorePossibleDuplicateWarning() ERROR: ', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Quét và cập nhật người chăm sóc của các đơn trùng theo người và phiên chăm sóc của đơn gốc.
   * Đầu vào là đơn gốc (đơn gốc có thế là bất cứ đơn nào, là đơn trùng có id bé nhất trong danh sách chia hiện tại)
   * Kết quả đầu ra là kết quả cập nhật các đơn trùng.
   * @param param0
   * @returns
   */
  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'assign-duplicate-leads-according-to-original-lead',
    queue: 'order-handling-assign-duplicate-leads-according-to-original-lead',
    errorHandler: rmqErrorsHandler,
  })
  async assignDuplicateLeadsAccordingToOriginalLead({
    leadId,
    updatedBy,
    state,
    filter,
  }: {
    leadId: number;
    updatedBy?: number;
    state: CareState;
    filter?: ManualDistributeLeadsFilter;
  }) {
    if (!leadId) return new Nack(false);

    const lead = await this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('l.order', 'o')
      .where('l.id = :leadId', { leadId })
      .innerJoinAndSelect('l.currentCare', 'currentCare')
      .select([
        'l.id',
        'o.customerPhone',
        'l.userId',
        'currentCare.shiftId',
        'o.projectId',
        'l.leadType',
      ])
      .getOne();

    if (!lead) return new Nack(false);

    const qb = await this.duplicateLeadsRepo
      .createQueryBuilder('dl')
      .innerJoin('leads', 'l', 'l.id = dl.leadId')
      .innerJoin('l.order', 'o')
      .where('l.id != :leadId', { leadId })
      .andWhere('o.customerPhone = :phone', { phone: lead?.order?.customerPhone })
      .andWhere('l.userId IS NULL')
      .andWhere('l.state IN (:...states)', {
        states: [CareState.new, CareState.unassign_attempted],
      })
      .andWhere('o.projectId = :projectId', { projectId: lead?.order?.projectId })
      .select('DISTINCT dl.leadId', 'id');
    if (!isEmpty(filter?.ids)) qb.andWhere('l.id IN (:...ids)', { ids: filter?.ids });

    const dupLeads = await qb.execute();
    if (isEmpty(dupLeads)) return new Nack(false);

    // const dupLeadIds = lead.duplicateLeads.map(l => l.duplicateLeadId);
    // const isOriginal = leadId <= min(dupLeadIds);

    // if (!isOriginal) return new Nack(false);

    const results = [];
    results.push(
      await Promise.all(
        dupLeads.map(it =>
          this.attachUserIdToLead(
            it.id,
            lead.userId,
            state === CareState.no_attempt ? CareState.no_attempt : CareState.assigned,
            lead.leadType,
            lead?.currentCare?.shiftId,
            updatedBy,
          ),
        ),
      ),
    );

    console.log(`assign duplicate results`, results);

    return new Nack(true);
  }

  @RabbitRPC({
    exchange: 'order-service',
    routingKey: 'assign-duplicate-leads-according-to-original-lead-after-sale',
    queue: 'order-handling-assign-duplicate-leads-according-to-original-lead-after-sale',
    errorHandler: rmqErrorsHandler,
  })
  async assignDuplicateLeadsAccordingToOriginalLeadAfterSale({
    leadId,
    updatedBy,
    state,
    filter,
  }: {
    leadId: number;
    updatedBy?: number;
    state: CareState;
    filter?: ManualDistributeLeadsFilter;
  }) {
    if (!leadId) return new Nack(false);

    const lead = await this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('l.order', 'o')

      .where('l.id = :leadId', { leadId })
      .andWhere('l.leadType = :leadType', { leadType: LeadType.after_sale })
      .innerJoinAndSelect('l.currentCare', 'currentCare')
      .select(['l.id', 'o.customerPhone', 'l.userId', 'currentCare.shiftId', 'l.leadType'])
      .getOne();

    if (!lead) return new Nack(false);

    const qb = await this.duplicateLeadsRepo
      .createQueryBuilder('dl')
      .innerJoin('leads', 'l', 'l.id = dl.leadId')
      .innerJoin('l.order', 'o')
      .where('l.id != :leadId', { leadId })
      .andWhere('o.customerPhone = :phone', { phone: lead?.order?.customerPhone })
      .andWhere('l.userId IS NULL')
      .andWhere('l.state IN (:...states)', {
        states: [CareState.new, CareState.unassign_attempted],
      })
      .select('DISTINCT dl.leadId', 'id');
    if (!isEmpty(filter?.ids)) qb.andWhere('l.id IN (:...ids)', { ids: filter?.ids });
    if (!isEmpty(filter?.projectIds))
      qb.andWhere('o.projectId IN (:...projectIds)', { projectIds: filter.projectIds });
    const dupLeads = await qb.execute();
    if (isEmpty(dupLeads)) return new Nack(false);

    // const dupLeadIds = lead.duplicateLeads.map(l => l.duplicateLeadId);
    // const isOriginal = leadId <= min(dupLeadIds);

    // if (!isOriginal) return new Nack(false);

    const results = [];
    results.push(
      await Promise.all(
        dupLeads.map(it =>
          this.attachUserIdToLead(
            it.id,
            lead.userId,
            state === CareState.no_attempt ? CareState.no_attempt : CareState.assigned,
            lead.leadType,
            lead?.currentCare?.shiftId,
            updatedBy,
          ),
        ),
      ),
    );

    console.log(`assign duplicate results`, results);

    return new Nack(true);
  }

  /**
   * Gắn người chăm sóc cho data lead
   * 1. bắt đầu sẽ tạo phiên chăm sóc với người chăm sóc là userId
   * 2. Gắn phiên chăm sóc và người chăm sóc cho data lead, nếu không có kết quả nào được cập nhật thì rollback
   * @param leadId id của lead
   * @param userId id người chăm sóc
   * @param state trạng thái chăm sóc của lead
   * @param shiftId id ca làm việc cùng với ca làm việc mà lead đang được chăm sóc
   * @param updatedBy id của người thực hiện thao tác
   * @returns
   */
  async attachUserIdToLead(
    leadId: number,
    userId: number,
    state: CareState = CareState.assigned,
    leadType: number = LeadType.normal,
    shiftId?: number,
    updatedBy?: number,
  ): Promise<UpdateResult | Lead> {
    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const care = await queryRunner.manager.save(
        plainToInstance(LeadCare, {
          leadId,
          userId,
          shiftId,
          updatedBy,
        }),
      );
      const updatedAt = care.createdAt;
      const result = await queryRunner.manager.update(
        Lead,
        {
          id: leadId,
          userId: IsNull(),
        },
        {
          userId: care.userId,
          state,
          currentCareId: care.id,
          lastCareId: care.id,
          updatedBy,
          updatedAt,
        },
      );
      if (!result.affected) {
        await queryRunner.rollbackTransaction();
        return result;
      }

      await queryRunner.commitTransaction();
      const newLead = plainToInstance(Lead, {
        id: care.leadId,
        userId: care.userId,
        state,
        currentCareId: care.id,
        lastCareId: care.id,
        currentCare: care,
        updatedBy,
        updatedAt,
      });

      if (leadType === LeadType.after_sale) await this.leadAfterSaleService.onLeadUpdated(newLead);
      else await this.onLeadUpdated(newLead);
      return newLead;
    } catch (error) {
      console.log(`error when attach user id to lead`, error);
      await queryRunner.rollbackTransaction();
      if (error?.driverError) throw new BadRequestException(error?.driverError?.detail);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async onLeadUpdated(payload: Partial<Lead>, currentState?: CareState) {
    const { id, state, userId, currentCareId, updatedAt, updatedBy } = payload;

    if (!isNil(state) && !isNil(currentCareId)) {
      console.log(`lead state changed to ${PlainCareState[state]}`, payload);
      try {
        await Promise.all([
          this.amqpConnection.publish('order-service', 'after-lead-state-updated', {
            leadId: id,
            state,
            updatedAt,
            currentCareId,
            currentState,
          }),
          this.amqpConnection.publish('order-service', 'schedule-gather-lead', {
            leadId: id,
            state,
            updatedAt,
            currentCareId,
          }),
        ]);
      } catch (error) {
        console.error(`onLeadUpdated() ERROR1: `, error);
        // Không throw error để tiếp tục xử lý các phần khác
      }
    }

    if (userId === null || !isNil(userId)) {
      try {
        await this.amqpConnection.publish('order-service', 'after-lead-user-id-updated', {
          leadId: id,
          updatedAt,
          userId,
          updatedBy,
        });
      } catch (error) {
        console.error(`onLeadUpdated() ERROR2: `, error);
        // Không throw error để tiếp tục xử lý
      }
    }

    await this.amqpConnection.publish('message-service', 'after-lead-updated', {
      leadId: id,
    });
    return true;
  }

  async getLeadCallHistoriesV2(leadId: number) {
    const qb = this.callHistoryRepo
      .createQueryBuilder('c1')
      .select([
        'c1.id',
        'c1.lead_id',
        'c1.outgoing_number',
        'c1.incoming_number',
        'c1.device_id',
        'c1.record_url',
        'c1.start_at',
        'c1.end_at',
        'c1.created_by',
        'c1.bill_sec',
        'c1.type',
        'c1.status',
      ])
      .where('(c1.lead_id = :leadId)', { leadId });
    return qb.getRawMany();
  }

  async getLeadCallHistories(leadId: number) {
    const qb = this.callHistoryRepo
      .createQueryBuilder('c1')
      .select([
        'c1.id',
        'c1.lead_id',
        'c1.outgoing_number',
        'c1.incoming_number',
        'c1.device_id',
        'c1.record_url',
        'c1.start_at',
        'c1.end_at',
        'c1.created_by',
        'c1.bill_sec',
        'c1.type',
      ])
      .leftJoin(
        CallHistory,
        'c2',
        'c1.incoming_number IS NOT NULL AND c1.incoming_number = c2.outgoing_number',
      )
      .where('(c1.lead_id = :leadId OR c2.lead_id = :leadId)', { leadId })
      .andWhere('c1.record_url IS NOT NUll');
    return qb.getRawMany();
  }

  async getPerformanceByUserIds(
    filter: LeadPerformanceByUserIdsDto,
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const timezone = headers?.['timezone'] ?? 'Asia/Ho_Chi_Minh';

    const companyId = request?.user?.companyId;
    if (!companyId) throw new ForbiddenException();

    const countryId = headers['country-ids'];
    if (!countryId) throw new BadRequestException(`country-ids is required`);

    const projectIds = FilterUtils.getProjectIds(headers['project-ids']);
    if (projectIds?.length === 0) return;

    const { userIds } = filter;
    const { fromDate, toDate } = this._getFromDateAndToDateFromOneMonthAgoToNow();

    const sqb = this.leadsRepo
      .createQueryBuilder('l')
      .innerJoin('l.order', 'o')
      .andWhere(`o.company_id = ${companyId}`)
      .andWhere(`o.country_id = ${countryId}`)
      .setParameters({
        companyId,
        countryId,
      })
      .innerJoin('lead_cares', 'latest_care', 'latest_care.id = l.last_care_id');
    if (projectIds) sqb.andWhere(`o.project_id IN (${projectIds.join(',')})`);
    sqb.andWhere('l.last_care_item_id IS NOT NULL');
    sqb
      .select('l.id', 'l_id')
      .addSelect(`DATE ( l.created_at AT TIME ZONE '${timezone}' )`, 'date')
      .groupBy('l_id')
      .addGroupBy('date')
      .setParameters({ from: fromDate, to: toDate })
      .innerJoin(
        LeadCareItem,
        'ci',
        'l.id = ci.lead_id AND ci.created_at >= :from AND ci.created_at <= :to',
      )
      .innerJoin(CareReason, 'cr', `ci.reason_id = cr.id`)
      .addSelect(`DATE ( ci.created_at AT TIME ZONE '${timezone}' )`, 'process_date')
      .addSelect(
        `CASE WHEN cr.state IN (${CONFIRMED_STATES.join(',')}) THEN o.id END`,
        'confirmed_order_id',
      )
      .addSelect(
        `CASE WHEN o.status IN (:...returnedStatus) AND cr.id IS NOT NULL THEN o.id END`,
        'returned_order_id',
      )
      .addSelect(
        `CASE WHEN o.status IN (:...deliveredStatus) AND cr.id IS NOT NULL THEN o.id END`,
        'delivered_order_id',
      )
      .addGroupBy('process_date')
      .addGroupBy(`"confirmed_order_id"`)
      .addGroupBy(`"returned_order_id"`)
      .addGroupBy(`"delivered_order_id"`)
      .setParameters({
        returnedStatus: RETURNED_SALES_STATUSES,
        deliveredStatus: ACTUAL_REVENUE_STATUSES,
      });

    const user: AuthUser = request.user;
    for (const profile of user.profiles) {
      const [dataAccessLevel, moduleInCharge] = profile;
      if (
        dataAccessLevel === DataAccessLevel.personal &&
        moduleInCharge?.includes(ModuleInCharge.telesale)
      ) {
        sqb.unScope('o');
        sqb.andWhere('ci.creator_id = :uid', { uid: user.id });
      }
    }
    sqb.addSelect('ci.creator_id', 'u_id').addGroupBy('u_id');
    const qb = getConnection(orderConnection)
      .createQueryBuilder()
      .from(`(${sqb.getQuery()})`, 'sub')
      // .leftJoin('orders', 'orders', 'orders.id = sub.confirmed_order_id')
      // .unScope('orders')
      .setParameters(sqb.getParameters())
      .select(
        'COUNT ( DISTINCT CASE WHEN sub.date = sub.process_date THEN (sub.l_id) END )',
        'new_processed',
      )
      .addSelect(
        'COUNT ( DISTINCT CASE WHEN sub.date = sub.process_date THEN (sub.confirmed_order_id) END )',
        'new_confirmed',
      )
      .leftJoin('orders', 'orders', 'orders.id = sub.confirmed_order_id')
      .unScope('orders')
      .andWhere('sub.u_id IN (:...userIds)', {
        userIds,
      })
      .addSelect('sub.u_id', 'userId')
      .addGroupBy('sub.u_id');
    const data = await qb.getRawMany();
    for (const item of data) {
      if (item.new_processed) item.new_processed = Number(item.new_processed);
      if (item.new_confirmed) item.new_confirmed = Number(item.new_confirmed);
      if (item.new_processed && item.new_confirmed)
        item.new_leads_conversation_rate = item.new_confirmed / item.new_processed;
      else item.new_leads_conversation_rate = 0;
    }
    data.sort((a, b) => {
      if (a.new_leads_conversation_rate < b.new_leads_conversation_rate) return 1;
      else if (a.new_leads_conversation_rate > b.new_leads_conversation_rate) return -1;
      return 0;
    });
    return data;
  }

  _getFromDateAndToDateFromOneMonthAgoToNow() {
    const now = moment().toISOString();
    const oneMonthAgoStart = moment()
      .subtract(1, 'months')
      .startOf('month')
      .toISOString();
    return {
      fromDate: oneMonthAgoStart,
      toDate: now,
    };
  }

  async getCallCenterStatus(name: string, user: AuthUser, headers: Record<string, string>) {
    const countryId = Number(headers['country-ids']);
    const companyId = Number(user.companyId);

    try {
      const callCenter = await this.callCenterRepo.findOne({
        where: { name: name.trim(), companyId, countryId },
        select: ['id', 'companyId', 'countryId', 'isActive'],
      });
      if (!callCenter) throw new NotFoundException(`Không tìm thấy call center name = ${name}.`);

      return { isActive: callCenter.isActive };
    } catch (e) {
      this.logger.error(`getCallCenterStatus ERROR: ${JSON.stringify(e)}`);
      throw new BadRequestException(e.message);
    }
  }

  async returnCallcenterExtension(
    name: string,
    extensionId: string,
    user: AuthUser,
    headers: Record<string, string>,
    filter: ReturnCallCenterExtensionsDto,
  ) {
    const countryId = Number(headers['country-ids']);
    const companyId = Number(user.companyId);
    const { isInvalid } = filter;

    try {
      if (isInvalid) {
        this.logger.error(`returnCallcenterExtension ERROR: extensionId=${extensionId} is invalid`);
        return { result: true };
      }

      const [callCenter, extension] = await Promise.all([
        this.callCenterRepo.findOne({
          where: {
            name: name.trim(),
            companyId,
            countryId,
          },
          select: ['id', 'countryId', 'companyId'],
        }),
        this.callCenterExtensionRepo.findOne({ where: { id: Number(extensionId) } }),
      ]);
      if (!callCenter) throw new NotFoundException(`Không tìm thấy call center name = ${name}.`);
      if (!extension)
        throw new NotFoundException(`Không tìm thấy call center extension id = ${extensionId}.`);

      const key = `-${CALLCENTER}-${callCenter.id}-${callCenter.countryId}-${callCenter.companyId}`;
      const extensionIds = await this.redis.lrange(key, 0, -1);
      extension.isAvailable = true;

      await Promise.all([
        this.callCenterExtensionRepo.save(extension),
        !extensionIds.includes(extensionId)
          ? this.redis.lpush(key, extensionId)
          : Promise.resolve(),
      ]);
      return { result: true };
    } catch (e) {
      this.logger.error(`returnCallcenterExtension ERROR: ${JSON.stringify(e)}`);
      throw new BadRequestException(e.message);
    }
  }

  async getCallcenterExtension(name: string, user: AuthUser, headers: Record<string, string>) {
    const countryId = Number(headers['country-ids']);
    const companyId = Number(user.companyId);
    const userId = user.id;

    let extensionId;
    let key;

    try {
      const callCenter = await this.callCenterRepo.findOne({
        where: {
          name: name.trim(),
          companyId,
          countryId,
          isActive: true,
        },
        select: ['id', 'companyId', 'countryId', 'customerId'],
      });
      if (!callCenter) throw new NotFoundException(`Không tìm thấy call center name = ${name}.`);

      key = `-${CALLCENTER}-${callCenter.id}-${callCenter.countryId}-${callCenter.companyId}`;
      extensionId = await this.redis.lpop(key);
      if (!extensionId)
        throw new BadRequestException({
          code: ErrorCode.CALL_0001,
          message: 'Các đầu số đều đang sử dụng khi sales thực hiện cuộc gọi',
        });

      const extension = await this.callCenterExtensionRepo.findOne({
        where: { id: extensionId },
        relations: ['callCenter'],
      });

      // Create draft call history
      let callHistory = plainToInstance(CallHistory, {
        extensionId,
        status: CallHistoryStatus.draft,
        type: CallHistoryType.ycall,
        createdBy: userId,
      });

      // Update call center extension available status to false
      extension.isAvailable = false;

      [callHistory] = await Promise.all([
        this.callHistoryRepo.save(callHistory),
        this.callCenterExtensionRepo.save(extension),
      ]);

      return {
        wssUrl: extension.callCenter.sipWssUrl,
        domain: extension.callCenter.sipDomain,
        extensionId: extension.id,
        extensionNumber: extension.extensionNumber,
        extensionPassword: extension.extensionPassword,
        callHistoryId: callHistory.id,
        customerId: extension.callCenter.customerId,
      };
    } catch (e) {
      if (extensionId) await this.redis.lpush(key, extensionId);
      this.logger.error(`getCallcenterExtension ERROR: ${JSON.stringify(e)}`);
      throw new BadRequestException(e.message);
    }
  }

  async deActiveCallCenterExtension(
    name: string,
    data: DeActiveCallCenterDto,
    user: AuthUser,
    headers: Record<string, string>,
  ) {
    const countryId = Number(headers['country-ids']);
    const companyId = Number(user.companyId);
    const userId = user.id;

    try {
      const callCenter = await this.callCenterRepo.findOne({
        where: { name: name.trim(), companyId, countryId },
      });
      if (!callCenter) throw new NotFoundException(`Không tìm thấy call center name = ${name}.`);
      if (callCenter.isActive === data.isActive) return callCenter;

      callCenter.isActive = data.isActive;
      callCenter.updatedBy = userId;
      await this.callCenterRepo.save(callCenter);

      return callCenter;
    } catch (e) {
      this.logger.error(`deActiveCallcenterExtension ERROR: ${JSON.stringify(e)}`);
      throw new BadRequestException(e.message);
    }
  }

  async createCallCenterExtension(
    name: string,
    data: CreateCallCenterExtensionDto,
    user: AuthUser,
    headers: Record<string, string>,
  ) {
    const countryId = Number(headers['country-ids']);
    const companyId = Number(user.companyId);
    const userId = user.id;

    try {
      const callCenter = await this.callCenterRepo.findOne({
        where: { name: name.trim(), companyId, countryId },
        select: ['id', 'companyId', 'countryId', 'customerId'],
      });
      if (!callCenter) throw new NotFoundException(`Không tìm thấy call center name = ${name}.`);

      const customerSuffix = callCenter.customerId.slice(1);
      const extensionRegex = new RegExp(`^${customerSuffix}\\w+$`);
      if (!extensionRegex.test(data.extensionNumber.trim()))
        throw new BadRequestException('Đầu số đã thêm không thuộc biz.');

      // Check for duplicate extension by company and country
      const duplicateExtension = await this.callCenterExtensionRepo.findOne({
        where: {
          extensionNumber: data.extensionNumber,
          callCenterId: callCenter.id,
          companyId: callCenter.companyId,
          countryId: callCenter.countryId,
        },
        select: ['id'],
      });
      if (duplicateExtension) {
        throw new BadRequestException('Đầu số đã tồn tại.');
      }

      const extension = plainToInstance(CallCenterExtension, {
        extensionNumber: data.extensionNumber,
        extensionPassword: data.extensionPassword,
        callCenterId: callCenter.id,
        companyId: callCenter.companyId,
        countryId: callCenter.countryId,
        createdBy: userId,
        isActive: true,
      });
      await this.callCenterExtensionRepo.save(extension);

      // add extension to redis
      const key = `-${CALLCENTER}-${callCenter.id}-${callCenter.countryId}-${callCenter.companyId}`;
      await this.redis.rpush(key, extension.id);

      delete extension.extensionPassword;
      return extension;
    } catch (e) {
      this.logger.error(`createCallcenterExtension ERROR: ${JSON.stringify(e)}`);
      throw new BadRequestException(e.message);
    }
  }

  async updateCallCenterExtension(
    id: number,
    data: UpdateCallCenterExtensionDto,
    user: AuthUser,
    headers: Record<string, string>,
  ) {
    const countryId = Number(headers['country-ids']);
    const companyId = Number(user.companyId);
    const userId = user.id;

    try {
      const extension = await this.callCenterExtensionRepo.findOne({
        id,
        companyId,
        countryId,
      });
      if (!extension) throw new NotFoundException('Không tìm thấy call center extension.');

      extension.extensionPassword = data.extensionPassword;
      extension.updatedBy = userId;
      extension.updatedAtManual = new Date();
      await this.callCenterExtensionRepo.save(extension);

      delete extension.extensionPassword;
      return extension;
    } catch (e) {
      this.logger.error(`updateCallcenterExtension ERROR: ${JSON.stringify(e)}`);
      throw new BadRequestException(e.message);
    }
  }

  async deleteCallCenterExtension(
    name: string,
    user: AuthUser,
    headers: Record<string, string>,
    data: CallCenterExtensionsDeleteDto,
  ) {
    const countryId = Number(headers['country-ids']);
    const companyId = Number(user.companyId);
    const extensionIds = data.extensionIds;
    let listExtensionIds = [];
    let key = '';

    try {
      const callCenter = await this.callCenterRepo.findOne({
        where: { name: name.trim(), companyId, countryId },
        select: ['id', 'companyId', 'countryId'],
      });
      if (!callCenter) throw new NotFoundException(`Không tìm thấy call center name = ${name}.`);

      const deleted = await this.callCenterExtensionRepo.delete({
        id: In(extensionIds),
        countryId,
        companyId,
      });

      if (deleted.affected) {
        key = `-${CALLCENTER}-${callCenter.id}-${callCenter.countryId}-${callCenter.companyId}`;

        // Start Redis transaction
        const multi = this.redis.multi();

        // Get the list of current extension IDs in Redis
        multi.lrange(key, 0, -1);

        // Remove each extension ID from the list in Redis
        extensionIds.forEach(extensionId => {
          multi.lrem(key, 0, extensionId.toString());
        });

        // Execute transaction
        const results = await multi.exec();
        // Check for any errors in the lrem commands
        const lremError = results.some(([err]) => err);
        if (lremError) {
          throw new BadRequestException('Failed to update Redis with deleted extensions');
        }
      }

      // Retrieve the updated list of extension IDs after deletion
      listExtensionIds = await this.redis.lrange(key, 0, -1);
      return { result: true, listExtensionIds };
    } catch (e) {
      this.logger.error(`deleteCallcenterExtension ERROR: ${JSON.stringify(e)}`);
      throw new BadRequestException(e.message);
    }
  }

  async getListCallCenterExtension(
    name: string,
    pagination: PaginationOptions,
    filter: CallCenterExtensionsFilter,
    user: AuthUser,
    headers: Record<string, string>,
  ) {
    const countryId = Number(headers['country-ids']);
    const companyId = Number(user.companyId);

    try {
      const sort = filter.sort || Sort.DESC;
      const orderBy = filter.orderBy ? `extension.${filter.orderBy}` : 'extension.lastUsedAt';

      const queryBuilder = this.callCenterExtensionRepo.createQueryBuilder('extension');
      queryBuilder.leftJoinAndSelect('extension.callCenter', 'callCenter');
      queryBuilder.andWhere('callCenter.name = :name', { name });
      queryBuilder.andWhere('callCenter.companyId = :companyId', { companyId });
      queryBuilder.andWhere('callCenter.countryId = :countryId', { countryId });

      // Apply extension number filter
      if (filter?.extensionNumber) {
        const extensionNumbers = filter.extensionNumber.split(' ').map(num => String(num));
        queryBuilder.andWhere(`extension.extensionNumber IN (:...extensionNumbers)`, {
          extensionNumbers,
        });
      }

      if (!filter?.isDownload) {
        if (pagination) {
          queryBuilder.take(pagination.limit).skip(pagination.skip);
        }
      }

      // Select only CallCenterExtension fields, excluding callCenter fields
      queryBuilder.select([
        'extension.id',
        'extension.extensionNumber',
        'extension.extensionPassword',
        'extension.callCenterId',
        'extension.lastUsedAt',
        'extension.createdBy',
        'extension.updatedBy',
        'extension.createdAt',
        'extension.updatedAt',
        'extension.updatedAtManual',
        'callCenter.customerId',
      ]);
      queryBuilder.orderBy(orderBy, sort);

      // Execute the query and return results
      const [data, total] = await queryBuilder.getManyAndCount();

      return {
        data,
        count: total,
        ...(filter.isDownload ? {} : { limit: pagination.limit, skip: pagination.skip }),
      };
    } catch (e) {
      this.logger.error(`getListCallCenterExtension ERROR: ${JSON.stringify(e)}`);
      throw new BadRequestException(e.message);
    }
  }

  async ycallWebhook(body: YcallRequestWebhookDto) {
    let callHistoryId, extensionId, leadId, key;
    let callHistory: CallHistory, extension: CallCenterExtension, lead: Lead;
    this.logger.log(`ycallWebhook body: ${JSON.stringify(body)}`);

    try {
      const sha1Signature = SignatureUtils.gen(process.env.YCALL_API_KEY, body.userfield);
      if (sha1Signature !== body.signature) {
        throw new BadRequestException(`ycallWebhook ERROR: Signature invalid`);
      }

      if (!body?.userfield) {
        throw new BadRequestException(`ycallWebhook ERROR: userfield không đủ thông tin (1)`);
      }

      [callHistoryId, extensionId, leadId] = body?.userfield?.split(',');
      if (!callHistoryId || !extensionId || !leadId) {
        throw new BadRequestException(`ycallWebhook ERROR: userfield không đủ thông tin (2)`);
      }

      [callHistory, extension, lead] = await Promise.all([
        this.callHistoryRepo.findOne({
          id: Number(callHistoryId),
        }),
        this.callCenterExtensionRepo.findOne({ id: Number(extensionId) }),
        this.leadsRepo.findOne({
          where: { id: Number(leadId) },
          select: ['id'],
        }),
      ]);
      key = `-${CALLCENTER}-${extension.callCenterId}-${extension.countryId}-${extension.companyId}`;

      if (!callHistory) {
        throw new BadRequestException(`ycallWebhook ERROR: Không tìm thấy call history`);
      }
      if (callHistory.callCenterId) {
        throw new BadRequestException(
          `ycallWebhook: callHistory id=${callHistoryId} đã được cập nhật`,
        );
      }

      if (!extension) {
        throw new BadRequestException(`ycallWebhook ERROR: Không tìm thấy extension`);
      }
      if (!lead) {
        throw new BadRequestException(`ycallWebhook ERROR: Không tìm thấy lead`);
      }

      // Add to queue
      const jobName = 'process-webhook';
      const data = {
        callHistoryId,
        extensionId,
        leadId,
        body,
        callCenterType: CallHistoryType.ycall,
      };
      await this.callCenterQueue.add(jobName, data, {
        ...QUEUE_SETTING,
        backoff: 5000, // custom backoff for this job
      });

      return { result: true };
    } catch (e) {
      this.logger.error(`ycallWebhook ERROR: ${JSON.stringify(e)}`);
      if (extension) {
        const extensionIds = await this.redis.lrange(key, 0, -1);
        extension.isAvailable = true;

        this.logger.log(
          `ycallWebhook add extensionId=[${extensionId}] redis, update extensionId=[${extensionId}] isAvailable to true `,
        );

        await Promise.all([
          this.callCenterExtensionRepo.save(extension),
          !extensionIds.includes(extensionId)
            ? this.redis.lpush(key, extensionId)
            : Promise.resolve(),
        ]);
      }
      throw e;
    }
  }

  async processWebhookYcall(job: Job<JobDataCallCenter>) {
    const { callHistoryId, extensionId, leadId, body } = job.data;
    let callHistory: CallHistory, extension: CallCenterExtension;
    let key, extensionIds;

    try {
      [callHistory, extension] = await Promise.all([
        this.callHistoryRepo.findOne({
          id: Number(callHistoryId),
        }),
        this.callCenterExtensionRepo.findOne({ id: Number(extensionId) }),
      ]);

      // Update call history
      callHistory.status = convertCallHistoryStatusToEnum(body.status);
      callHistory.duration = body.duration;
      callHistory.billSec = body.billsec;
      callHistory.callCenterId = body.callid;
      callHistory.startAt = moment(body.calldate, 'YYYY-MM-DD HH:mm:ss')
        .utcOffset(7, true)
        .utc()
        .toDate();

      callHistory.endAt = this.callHistoryCalculateEndAt(body.calldate, body.duration);
      callHistory.outgoingNumber = body.phone;
      callHistory.extensionId = extensionId;
      callHistory.leadId = leadId;
      callHistory.incomingNumber = String(extensionId);

      if (callHistory.status === CallHistoryStatus.answered) {
        // Fetch the recording as a buffer
        const response = await axios.get(body.recording, { responseType: 'arraybuffer' });
        const buffer = Buffer.from(response.data, 'binary');

        const mimetype = 'audio/mpeg';
        const filename = `${process.env.SERVICE_NAME}/leads/${leadId}/${body.callid}.mp3`;

        // Upload to S3
        const mp3Url = await AwsUtils.uploadS3(buffer, mimetype, filename);
        callHistory.recordUrl = mp3Url;
      }

      // Update extension
      extension.isAvailable = true;
      extension.lastUsedAt = new Date();

      key = `-${CALLCENTER}-${extension.callCenterId}-${extension.countryId}-${extension.companyId}`;

      const connection = getConnection(orderConnection);
      const queryRunner = connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        extensionIds = await this.redis.lrange(key, 0, -1);

        await Promise.all([
          queryRunner.manager.save(callHistory),
          queryRunner.manager.save(extension),
          !extensionIds.includes(String(extensionId))
            ? this.redis.lpush(key, extensionId)
            : Promise.resolve(),
        ]);
        await queryRunner.commitTransaction();
      } catch (e) {
        this.logger.error(`processWebhookYcall ERROR: ${e.message}`);
        await queryRunner.rollbackTransaction();
      } finally {
        await queryRunner.release();
      }
    } catch (e) {
      this.logger.error(`processWebhookYcall ERROR: ${e.message}`);
      if (key && extensionIds) {
        if (!extensionIds.includes(String(extensionId))) {
          await this.redis.lpush(key, extensionId);
        }
      }
      throw e;
    }
  }

  callHistoryCalculateEndAt(calldate: string, duration: number): Date {
    // Parse the calldate as if it's in UTC+7
    const startAt = moment(calldate, 'YYYY-MM-DD HH:mm:ss')
      .utcOffset(7, true) // Interpret as UTC+7 without shifting
      .utc(); // Convert to UTC

    // Add the duration in seconds
    const endAt = startAt.clone().add(duration, 'seconds');

    // Return as a JavaScript Date object
    return endAt.toDate();
  }
  async importCallcenterExtension(
    buffer: Buffer,
    name: string,
    user: AuthUser,
    headers: Record<string, string>,
  ) {
    const countryId = 84;
    const companyId = Number(user.companyId);
    const userId = user.id;

    try {
      const callCenter = await this.callCenterRepo.findOne({
        where: { name: name.trim(), companyId, countryId },
        select: ['id', 'companyId', 'countryId', 'customerId'],
      });

      const data = ExcelUtils.read(buffer, 0);
      const colNames = {
        index: 'STT',
        extensionNumber: 'Mã đầu số',
        extensionPassword: 'Mật khẩu',
      };

      let count = 0;
      let countError = 0;
      const customerSuffix = callCenter.customerId.slice(1);
      const extensionRegex = new RegExp(`^${customerSuffix}\\w+$`);

      const listExtensionRaw = await this.callCenterExtensionRepo.find({
        select: ['extensionNumber'],
      });
      const listExtensionOrigin = listExtensionRaw.map(it => it.extensionNumber);
      const listExtension = listExtensionRaw.map(it => it.extensionNumber);
      const listErr = [];

      const bulkExtensions = data
        .map((item, idx) => {
          if (
            !item[colNames.extensionNumber] ||
            !item[colNames.extensionPassword] ||
            !extensionRegex.test(item[colNames.extensionNumber]) ||
            listExtension.includes(String(item[colNames.extensionNumber]))
          ) {
            const errors = [];
            if (!item[colNames.index])
              errors.push({ field: 'STT', errorCode: ErrorCode.CALL_0010 });
            if (!item[colNames.extensionNumber])
              errors.push({ field: 'extensionNumber', errorCode: ErrorCode.CALL_0004 });
            if (!item[colNames.extensionPassword])
              errors.push({
                field: 'extensionPassword',
                errorCode: ErrorCode.CALL_0005,
              });
            if (!extensionRegex.test(item[colNames.extensionNumber]))
              errors.push({
                field: 'extensionNumber',
                errorCode: ErrorCode.CALL_0004,
              });
            if (listExtension.includes(String(item[colNames.extensionNumber]))) {
              // Check đầu số trùng trong DB
              if (listExtensionOrigin.includes(String(item[colNames.extensionNumber]))) {
                errors.push({
                  field: 'extensionNumber',
                  errorCode: ErrorCode.CALL_0009,
                });
              } else {
                // kiểm tra đầu số trùng với đầu số khác trong file import, trả về idx của dòng trùng đầu tiên
                // Check đầu số trùng với đầu số khác trong file import
                const duplicateIndex = data.findIndex(
                  (row, index) =>
                    index < idx && row[colNames.extensionNumber] === item[colNames.extensionNumber],
                );
                if (duplicateIndex !== -1) {
                  errors.push({
                    field: 'extensionNumber',
                    errorCode: ErrorCode.CALL_0011, // Use a new error code for duplicate in the import file
                    duplicateIndex: duplicateIndex + 1, // Return a 1-based index for user clarity
                  });
                }
              }
            }

            countError++;
            listErr.push({
              STT: item[colNames.index],
              [colNames.extensionNumber]: item[colNames.extensionNumber],
              [colNames.extensionPassword]: item[colNames.extensionPassword],
              errors,
            });
            return;
          }

          count++;
          listExtension.push(String(item[colNames.extensionNumber]));

          return plainToInstance(CallCenterExtension, {
            callCenterId: callCenter.id,
            extensionNumber: item[colNames.extensionNumber],
            extensionPassword: item[colNames.extensionPassword],
            createdBy: userId,
            companyId,
            countryId,
            isAvailable: true,
          });
        })
        .filter(item => item !== undefined);

      if (bulkExtensions.length === 0)
        return { resule: true, countSuccess: 0, countError, listErr };

      // Split into chunks of 100
      const chunks = Utils.chunkArray(bulkExtensions, 100);
      const insertedIds: number[] = [];

      const connection = getConnection(orderConnection);
      const queryRunner = connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();
      try {
        for (const chunk of chunks) {
          const savedEntities = await queryRunner.manager.save(CallCenterExtension, chunk);
          const ids = savedEntities.map(entity => entity.id);
          insertedIds.push(...ids);
        }

        // Insert extension into redis
        const key = `-${CALLCENTER}-${callCenter.id}-${callCenter.countryId}-${callCenter.companyId}`;
        await this.redis.rpush(key, ...insertedIds);

        await queryRunner.commitTransaction();
        return { resule: true, countSuccess: count, countError: countError, listErr };
      } catch (e) {
        await queryRunner.rollbackTransaction();
        if (e?.driverError) throw new BadRequestException(e?.driverError?.detail);
        throw e;
      } finally {
        await queryRunner.release();
      }
    } catch (e) {
      console.log(`importCallcenterExtension ERROR: ${e}`);
      throw new BadRequestException(e.message);
    }
  }

  async getNextStepOfLead(leadId: number) {
    const lead = await this.leadsRepo.findOne({ where: { id: leadId } });
    if (!lead) throw new BadRequestException('Not found lead');
    return NEXT_CARE_STATES_FE[lead.state].map(state => CareState[state]);
  }

  async countReasonsForSpecialStateAfterSales(leadId: number, filter) {
    const { state, reasonKey, excludeReasonKeys } = filter || {};
    const log = await this.logsRepo
      .createQueryBuilder('l')
      .where('l.tableName = :tableName', { tableName: 'leads' })
      .andWhere('l.recordId = :leadId', { leadId })
      .andWhere('l.action = :action', { action: 'STATUS' })
      .andWhere(`'${state}' = ANY(l.changes)`)
      .orderBy('l.createdAt', 'DESC')
      .limit(1)
      .getOne();

    const qb = this.leadCareItemsRepo
      .createQueryBuilder('items')
      .leftJoin('items.leadCare', 'care')
      .leftJoin('items.reason', 'reason')
      .where('care.leadId = :leadId', { leadId })
      .andWhere('items.createdAt >= :createdAt', { createdAt: log?.createdAt })
      .andWhere('reason.state IN (:...state)', {
        state: [state, CareStateAfterSales.not_connected, CareStateAfterSales.connected],
      })
      .select('items.reasonId', 'reasonId')
      .addSelect('COUNT (DISTINCT items.id)', 'count')
      .groupBy('items.reasonId');

    const result = await qb.getRawMany();
    return result;
  }

  async countReasonForNotConnectedAndConnectedStateAfterSales(leadId: number) {
    const log = await this.logsRepo
      .createQueryBuilder('l')
      .where('l.tableName = :tableName', { tableName: 'leads' })
      .andWhere('l.recordId = :leadId', { leadId })
      .andWhere('l.action = :action', { action: 'STATUS' })
      .andWhere(`NOT('${CareStateAfterSales.not_connected}' = ANY(l.beforeChanges))`)
      .andWhere(`NOT('${CareStateAfterSales.connected}' = ANY(l.beforeChanges))`)
      .andWhere(
        `('${CareStateAfterSales.not_connected}' = ANY(l.changes) OR '${CareStateAfterSales.connected}' = ANY(l.changes))`,
      )
      .orderBy('l.createdAt', 'DESC')
      .limit(1)
      .getOne();

    const qb = this.leadCareItemsRepo
      .createQueryBuilder('items')
      .leftJoin('items.leadCare', 'care')
      .leftJoin('items.reason', 'reason')
      .where('care.leadId = :leadId', { leadId })
      .andWhere('items.createdAt >= :createdAt', { createdAt: log?.createdAt })
      .andWhere('reason.state IN (:...state)', {
        state: [CareStateAfterSales.not_connected, CareStateAfterSales.connected],
      })
      .select('items.reasonId', 'reasonId')
      .addSelect('COUNT (DISTINCT items.id)', 'count')
      .groupBy('items.reasonId');

    const result = await qb.getRawMany();
    return result;
  }

  async countReasonsForFailedStateAfterSales(leadId: number, filter) {
    const { state, reasonKey, excludeReasonKeys } = filter || {};
    const qb = this.leadCareItemsRepo
      .createQueryBuilder('items')
      .leftJoin('items.leadCare', 'care')
      .leftJoinAndSelect('items.reason', 'reason')
      .where('care.leadId = :leadId', { leadId })
      .andWhere('reason.state IN (:...state)', {
        state: [
          state,
          CareStateAfterSales.not_connected,
          CareStateAfterSales.connected,
          CareStateAfterSales.potential,
        ],
      })
      .orderBy('items.createdAt', 'ASC');

    const careItems = await qb.getMany();
    if (careItems.length === 0) return [];
    let count = 0,
      lastLeadCareId = careItems[0]?.leadCareId,
      isFailedBefore = false;
    for (const item of careItems) {
      if (
        lastLeadCareId !== item.leadCareId ||
        item.reason.state === CareStateAfterSales.potential
      ) {
        isFailedBefore = false;
        lastLeadCareId = item.leadCareId;
      }
      if (item.reason.state === CareStateAfterSales.failed) isFailedBefore = true;
      if (
        item.reason.state === CareStateAfterSales.failed ||
        (item.reason.state === CareStateAfterSales.not_connected && isFailedBefore) ||
        (item.reason.state === CareStateAfterSales.connected && isFailedBefore)
      )
        count++;
    }
    return [
      {
        count,
      },
    ];
  }

  async countReasonsForFailedStateFreshLead(leadId: number, filter) {
    const { state, reasonKey, excludeReasonKeys } = filter || {};

    const qb = this.leadCareItemsRepo
      .createQueryBuilder('items')
      .leftJoin('items.leadCare', 'care')
      .leftJoinAndSelect('items.reason', 'reason')
      .where('care.leadId = :leadId', { leadId })
      .andWhere('reason.state IN (:...state)', {
        state: [state, CareState.attempted, CareState.potential],
      })
      .orderBy('items.createdAt', 'ASC');
    const careItems = await qb.getMany();
    if (careItems.length === 0) return [];
    let count = 0,
      lastLeadCareId = careItems[0]?.leadCareId,
      isFailedBefore = false;
    for (const item of careItems) {
      if (lastLeadCareId !== item.leadCareId || item.reason.state === CareState.potential) {
        isFailedBefore = false;
        lastLeadCareId = item.leadCareId;
      }
      if (item.reason.state === CareState.failed) isFailedBefore = true;
      if (
        item.reason.state === CareState.failed ||
        (item.reason.state === CareState.attempted && isFailedBefore)
      )
        count++;
    }
    return [
      {
        count,
      },
    ];
  }

  async countReasonsForPotentialStateAfterSale(leadId: number, filter) {
    const { state, reasonKey, excludeReasonKeys } = filter || {};

    const qb = this.leadCareItemsRepo
      .createQueryBuilder('items')
      .leftJoin('items.leadCare', 'care')
      .leftJoinAndSelect('items.reason', 'reason')
      .where('care.leadId = :leadId', { leadId })
      .andWhere('reason.state IN (:...state)', {
        state: [
          state,
          CareStateAfterSales.not_connected,
          CareStateAfterSales.connected,
          CareStateAfterSales.failed,
        ],
      })
      .orderBy('items.createdAt', 'ASC');
    const careItems = await qb.getMany();
    if (careItems.length === 0) return [];
    let count = 0,
      lastLeadCareId = careItems[0]?.leadCareId,
      isPotentialBefore = false;
    for (const item of careItems) {
      if (lastLeadCareId !== item.leadCareId || item.reason.state === CareStateAfterSales.failed) {
        count = 0;
        isPotentialBefore = false;
        lastLeadCareId = item.leadCareId;
      }
      if (item.reason.state === CareStateAfterSales.potential) isPotentialBefore = true;
      if (
        item.reason.state === CareStateAfterSales.potential ||
        (item.reason.state === CareStateAfterSales.not_connected && isPotentialBefore) ||
        (item.reason.state === CareStateAfterSales.connected && isPotentialBefore)
      )
        count++;
    }
    return [
      {
        count,
      },
    ];
  }
}
