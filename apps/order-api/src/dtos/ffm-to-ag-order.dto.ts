import { FfmOrderStatus } from '../enums/ffm-order-status.enum';
import { OrderType } from '../enums/order-type.enum';
import { FfmTag } from '../read-entities/ffm-order/ffm-tag.entity';

export class FfmToAgOrderStatusDto {
  displayId: string;
  externalId: string;
  status: FfmOrderStatus;
  reason: string;
  description: string;
  actor: string;
  updatedAt: number;
  clientId: number;
}

export class FfmToAgOrderCarrierDto {
  displayId: string;
  externalId: string;
  carrierCode: string;
  waybillNumber: string;
  actor: string;
}

export class FfmToAgOrderInfoDto {
  displayId: string;
  externalId: string;
  clientId: string;
  recipientName?: string;
  recipientPhone?: string;
  recipientAddress?: string;
  recipientAddressNote?: string;
  recipientWardId?: string;
  recipientDistrictId?: string;
  recipientProvinceId?: string;
  recipientPostCode?: string;
  recipientCountryId?: string;
  discount?: number;
  surcharge?: number;
  shippingFee?: number;
  paid?: number;
  totalPrice?: number;
  serviceTLS?: number;
  serviceCS?: number;
  serviceFFM?: number;
  serviceInsurance?: number;
  subTotal?: number;
  creator?: string;
  updatedAt: number;
  waybillNote?: string;
  products?: FfmToAgOrderProductDto[];
  type?: OrderType;
}

export class FfmToAgOrderProductDto {
  productSKU: string;
  productName: string;
  quantity: number;
  weight: number;
}

export class FfmToAgOrderTagsDto {
  displayId: string;
  externalId: string;
  tags: FfmTag[];
  actor: string;
  updatedAt: number;
}

export class ValidateSyncOrderStatusFromFFM {
  updatedAt: number;
  id: number;
  status: number;
  externalId: string;
  displayId: string;
  lastUpdateStatus: number;
}
