import { InjectConnection } from '@nestjs/typeorm';
import { messageConnection } from 'core/constants/database-connection.constant';
import { Connection, EntitySubscriberInterface, InsertEvent } from 'typeorm';
import { Conversation } from '../../entities/conversation.entity';
import { PageScopedUser } from '../../entities/page-scoped-user.entity';

export class ConversationSubscriber implements EntitySubscriberInterface<Conversation> {
  constructor(
    @InjectConnection(messageConnection)
    private connection: Connection,
  ) {
    this.connection.subscribers.push(this);
  }

  // Chỉ định entity mà subscriber lắng nghe
  listenTo() {
    return Conversation;
  }

  /**
   * Called after entity insertion.
   */
  async afterInsert(event: InsertEvent<Conversation>) {
    const conversation = event.entity; // Bản ghi vừa được thêm vào conversations
    const entityManager = event.manager;

    if (!conversation.pageId || !conversation.scopedUserId || !entityManager) return;

    // <PERSON><PERSON><PERSON> bả<PERSON> ghi tương ứng trong page_scoped_users
    const pageScopedUser = await entityManager.findOne(PageScopedUser, {
      where: {
        pageId: conversation.pageId,
        scopedUserId: conversation.scopedUserId,
      },
    });
    // console.log(
    //   `afterInsert() pageScopedUser ${conversation.pageId}_${conversation.scopedUserId}`,
    //   pageScopedUser,
    // );
    if (pageScopedUser) {
      conversation.status = pageScopedUser.careStatus;
      conversation.careUserId = pageScopedUser.careUserId;

      // Lưu thay đổi vào conversations
      await entityManager.save(conversation);
    }
  }
}
