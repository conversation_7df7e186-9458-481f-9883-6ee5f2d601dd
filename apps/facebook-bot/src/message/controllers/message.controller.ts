import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  InternalServerErrorException,
  Param,
  ParseArrayPipe,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Request,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { Auth } from 'core/auth/decorators/auth/new-auth.decorator';
import { Pagination } from 'core/decorators/pagination/pagination.decorator';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { isEmpty, isNil } from 'lodash';
import { Campaign } from '../../entities/campaign.entity';
import { FanPage } from '../../entities/fanpage.entity';
import { User } from '../../entities/user.entity';
import { CampaignDto, CampaignUpdateDto } from '../dtos/campaign.dto';
import { FilterDefault } from '../filters/filterDefault.filter';
import { MessageService } from '../services/message.service';
import { verify2FA } from '../../facebook-api';
import { UserVerifyDto } from '../dtos/user-verify.dto';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import { FanPagesService } from '../services/fanpage.service';
import { UpdatePageDto } from '../dtos/fanpage.dto';
import { PagesFilter } from '../filters/pages.filter';
import { ConversationTagsService } from '../services/conversation-tags.service';
import { TagsFilter } from '../filters/tags.filter';
import { Tag } from '../../entities/tag.entity';
import { UpdateUserDto, UserSaveDto } from '../dtos/user-save.dto';
import { UserLoginDto } from '../dtos/user-login.dto';
import { CampaignsService } from '../services/campaigns.service';
import { CampaignsFilter } from '../filters/campaigns.filter';
import { ApiBody, ApiConsumes } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { AwsUtils } from 'core/utils/AwsUtils';

const ACTION_DELETE = 'delete';
const ACTION_LINK_FANPAGE = 'link_fanpage';
const ACTION_DELETE_VIA = 'delete_via';
const ACTION_UPDATE_VIA = 'update_via';

const TAB_LINKED = 'linked';
const TAB_UNLINKED = 'unlinked';
const TAB_ALL = 'all';

@Controller('message')
export class MessageController {
  constructor(
    private fanpageService: FanPagesService,
    private messService: MessageService,
    private conversationTagsService: ConversationTagsService,
    private redisService: RedisCacheService,
    private campaignService: CampaignsService,
  ) {}

  @Get('connected-pages')
  // @Auth()
  async getConnectedPages(@Query('from') from) {
    if (from) {
      from = new Date(Number(from));
    }
    return this.messService.getConnectedPages(from);
  }

  @Get('users')
  @Auth()
  async fetchUser(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: FilterDefault,
    @Request() request: Record<string, any>,
  ): Promise<[User[], number]> {
    return this.messService.findUsers(filter, pagination, request.user.companyId, request.user.id);
  }

  @Put('users/:id/take')
  @Auth()
  async takeUser(@Param('id') id: string, @Request() request: Record<string, any>) {
    return this.messService.takeUser(id, request.user);
  }

  @Get('pages')
  @Auth()
  async fetchPage(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: PagesFilter,
    @Request() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
  ): Promise<[FanPage[], number] | FanPage[]> {
    filter.companyId = request.user.companyId;

    let countryIds: string[] = [];
    if (!isNil(headers)) {
      if (headers['country-ids']) countryIds = headers['country-ids']?.split(',');
    }
    if (isEmpty(filter.countryIds)) filter.countryIds = countryIds;
    if (filter.getAllName) return this.messService.findAllPageName(filter, headers);
    return this.messService.findPages(filter, pagination, headers);
  }

  @Get('v2/pages')
  @Auth()
  async findPagesV2(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: PagesFilter,
    @Request() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
  ): Promise<[FanPage[], number] | FanPage[]> {
    filter.companyId = request.user.companyId;
    if (isNil(filter.tab)) {
      filter.tab = TAB_ALL;
    }

    return this.messService.findPagesV2(filter, pagination, headers);
  }

  // bulk update pages
  @Put('pages')
  @Auth()
  async updatePageByIds(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: PagesFilter,
    @Body() data: UpdatePageDto,
    @Headers() headers: Record<string, string>,
    @Request() request: Record<string, any>,
  ): Promise<any> {
    if (!filter.pageIds || filter.pageIds.length === 0) {
      throw new BadRequestException('pageIds is required');
    }

    // double check permissions, todo optimize query
    filter.tab = TAB_ALL;
    const pages = await this.messService.findPagesV2(filter, pagination, headers);

    if (!pages || !pages[0]) {
      throw new BadRequestException('pages not found');
    }

    const validPages = pages[0];
    let pageIds = [];
    if (validPages instanceof Array) {
      pageIds = validPages.map(p => p.id);
    }

    if (pageIds.length === 0) {
      throw new BadRequestException('pages not found');
    }

    switch (filter.action) {
      case ACTION_DELETE_VIA:
        if (!filter.viaIds || filter.viaIds.length === 0) {
          throw new BadRequestException('viaIds is required');
        }
        return this.fanpageService.softDeleteVia(pageIds[0], filter.viaIds);
      case ACTION_UPDATE_VIA:
        if (!filter.viaIds || filter.viaIds.length === 0) {
          throw new BadRequestException('viaIds is required');
        }

        return this.fanpageService.updateViaByIds(pageIds[0], filter.viaIds, data);
      case ACTION_DELETE:
        return this.fanpageService.softDeletePage(pageIds);
      case ACTION_LINK_FANPAGE:
        if (
          [data.productId, data.countryId, data.projectId, data.marketerId].every(
            v => v === undefined,
          )
        ) {
          throw new BadRequestException('productId is required');
        }
        const updateData: UpdatePageDto = {
          productId: data.productId,
          countryId: data.countryId,
          projectId: data.projectId,
          marketerId: data.marketerId,
          orderType: data.orderType,
        };
        return this.fanpageService.updatePageByIds(pageIds, updateData);
      default:
        throw new BadRequestException('action is required');
    }
  }

  @Get('pages/:id')
  @Auth()
  async getPage(
    @Param('id') id: string,
    @Headers() headers: Record<string, string>,
    @Request() request: Record<string, any>,
  ): Promise<User[]> {
    return this.messService.getPage(id);
  }

  @Put('pages/:id')
  @Auth()
  async updatePage(
    @Param('id') id: string,
    @Query() filter: PagesFilter,
    @Body() data: UpdatePageDto,
    @Headers() headers: Record<string, string>,
    @Request() request: Record<string, any>,
  ): Promise<FanPage> {
    const res = await this.fanpageService.updatePage(id, data);
    if (res) {
      if (filter.tab === TAB_ALL && data.productId && data.productId > 0) {
        // todo: combine into a transaction
        // update product id for all available post
        // it should be run in a transaction to ensure consistency and should be run in consumer for long running task
        await this.fanpageService.updateProductForFeedInPage(id, data.productId);
      }
    }
    return res;
  }

  @Post('users')
  @Auth()
  async saveUser(
    @Body() data: UserSaveDto,
    @Request() request: Record<string, any>,
  ): Promise<User | { ticket: string }> {
    return this.messService.saveUser(data, request.user);
  }

  @Put('users/:id')
  @Auth()
  async updateUser(
    @Param('id') userId: string,
    @Body() body: UpdateUserDto,
    @Request() request: Record<string, any>,
  ) {
    return this.messService.updateUser(userId, body, request.user);
  }

  @Delete('users/:id')
  @Auth()
  async deleteUser(@Param('id') userId: string, @Request() request: Record<string, any>) {
    return this.messService.deleteUser(userId, request.user);
  }

  @Post('login')
  @Auth()
  async login(
    @Body() data: UserLoginDto,
    @Request() request: Record<string, any>,
  ): Promise<User | { ticket: string }> {
    return this.messService.loginBot(data, request.user);
  }

  @Post('instagram')
  async getInstagramToken(@Body() data: UserLoginDto, @Request() request: Record<string, any>) {
    return this.messService.getInstaToken(data);
  }

  @Post('verify')
  async verify(@Request() request, @Body() data: UserVerifyDto): Promise<any> {
    let loginData: string;
    try {
      loginData = await this.redisService.get(data.ticket);
    } catch (e) {
      throw new InternalServerErrorException('Có lỗi xảy ra');
    }
    if (!loginData) {
      throw 'Ticket không hợp lệ';
    }
    try {
      const { cookies, password, companyId } = JSON.parse(loginData);
      const api = await verify2FA(data.code, cookies, {});
      return this.messService.handleLoginUser(api, password, companyId);
    } catch (e) {
      throw new BadRequestException('Không thể xác thực thông tin đăng nhập');
    }
  }

  @Put('user/fetch/:id')
  @Auth()
  async fetchDataUser(
    @Param('id') id: string,
    @Headers() headers: Record<string, string>,
    @Request() request: Record<string, any>,
    @Body('refreshToken') refreshToken: boolean,
  ): Promise<User> {
    refreshToken = typeof refreshToken === 'string' ? refreshToken === 'true' : !!refreshToken;
    return this.messService.fetchDataUser(id, request, refreshToken);
  }

  @Get('tags')
  @Auth()
  async fetchTags(
    @Pagination() pagination: PaginationOptions,
    @Query() query: TagsFilter,
    @Request() req: Record<string, any>,
  ): Promise<Tag[]> {
    return this.conversationTagsService.getTags(query, {}, req);
  }

  // @Post('tags')
  // @CmsAuth()
  // async createTags(@Request() request, @Body() data: TagsDto): Promise<Tags[]> {
  //   return this.messService.createTags(data);
  // }

  @Get('campaign')
  @Auth()
  async fetchCampaign(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: CampaignsFilter,
    @Request() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
  ): Promise<[Campaign[], number]> {
    return this.campaignService.findCampaigns(filter, pagination, headers);
  }

  @Get('campaign/count')
  @Auth()
  async countCampaign(
    @Pagination() pagination: PaginationOptions,
    @Query() filter: CampaignsFilter,
    @Request() request: Record<string, any>,
    @Headers() headers: Record<string, string>,
    @Query('groupBy', new ParseArrayPipe({ separator: ',', optional: true, items: String }))
    groupBy: string[],
  ): Promise<any> {
    return this.campaignService.countCampaigns(filter, groupBy, headers);
  }

  @Get('campaign/:id')
  @Auth()
  async fetchOneCampaign(
    @Request() request,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Campaign> {
    return this.campaignService.findOneCampaign(id);
  }

  @Post('campaign')
  @Auth()
  async createCampaign(
    @Request() request,
    @Body() data: CampaignDto,
    @Headers() headers,
  ): Promise<Campaign> {
    return this.messService.createCampaign(data, headers, request);
  }

  @Put('campaign/status/:id')
  @Auth()
  async updateCampaign(
    @Request() request,
    @Body() data: CampaignUpdateDto,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Campaign> {
    return this.messService.updateCampaign(data, id, request);
  }

  @Post('/pre-signed-url')
  @ApiConsumes('multipart/form-data')
  @Auth()
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async getPreSignedUrl(@UploadedFile('file') file) {
    if (!file) throw new BadRequestException('file is required');
    try {
      return await AwsUtils.getPreSignUrl(file);
    } catch (e) {
      throw new BadRequestException(`getPreSignedUrl ERROR: ${e.message}`);
    }
  }
}
