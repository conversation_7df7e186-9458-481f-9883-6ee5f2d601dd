import { AmqpConnection, Nack, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { messageConnection } from 'core/constants/database-connection.constant';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { groupBy, isEmpty, isNil, last, omit, omitBy, pick } from 'lodash';
import { LessThan, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { FACEBOOK_API_ENDPOINT } from '../../constants/fb-api-endpoints.constant';
import { Conversation } from '../../entities/conversation.entity';
import { ConversationsPhones } from '../../entities/conversations-phones.entity';
import { FanPage } from '../../entities/fanpage.entity';
import { Message } from '../../entities/message.entity';
import { IFbThread } from '../../facebook-api/functions/getThreadList';
import { BotApiService } from '../../facebook-chat/services/bot.service';
import { SocketService } from '../../socket/socket.service';
import { UpdateConversationDto } from '../dtos/conversation.dto';
import { CareScenarioService } from './care-scenario.service';
import { CareStatus } from '../../enums/care-status.enum';
import { PageScopedUser } from '../../entities/page-scoped-user.entity';
import * as moment from 'moment';

export const CONVERSATION_TAGS: (
  | 'INBOX'
  | 'ARCHIVED'
  | 'PENDING'
  | 'OTHER'
  | 'PAGE_BACKGROUND'
)[] = ['INBOX', 'OTHER'];

interface IFbComment {
  message: string;
  from: {
    name: string;
    id: string;
  };
  comment_count: number;
  created_time: string;
  id: string;
  comments: {
    data: IFbComment[];
  };
}

@Injectable()
export class ConversationsService {
  constructor(
    @InjectRepository(Conversation, messageConnection)
    private conversationRepo: Repository<Conversation>,
    @InjectRepository(FanPage, messageConnection)
    private pageRepo: Repository<FanPage>,
    @InjectRepository(Message, messageConnection)
    private messageRepository: Repository<Message>,
    @InjectRepository(PageScopedUser, messageConnection)
    private suRepo: Repository<PageScopedUser>,
    private careScenarioService: CareScenarioService,
    private amqpConnection: AmqpConnection,
    private socketGateway: SocketService,
    private botApiService: BotApiService,
  ) {}

  async getFullConversation(pageId: string, userId: string, feedId = '') {
    // const queryRunner = this.mesConnection.createQueryRunner("master");
    const qb = this.conversationRepo
      .createQueryBuilder('c')
      // .setQueryRunner(queryRunner)
      .andWhere('c.pageId = :pageId', { pageId })
      .andWhere('c.scopedUserId = :userId', { userId })
      .andWhere('c.feedId = :feedId', { feedId })
      .leftJoinAndMapMany(
        'c.phones',
        ConversationsPhones,
        'phones',
        `"phones"."page_id" = "c"."page_id"
            AND "phones"."scoped_user_id" = "c"."scoped_user_id"
            AND ("phones"."feed_id" = "c"."feed_id" OR  ("phones"."feed_id" IS NULL AND "c"."feed_id" = ''))`,
      )
      .leftJoinAndMapOne('c.page', FanPage, 'p', 'p.id = c.pageId')
      .leftJoinAndMapOne(
        'c.user',
        PageScopedUser,
        'u',
        'u.pageId = c.pageId AND u.scopedUserId = c.scopedUserId',
      )
      .leftJoinAndSelect('u.tags', 'tags')
      .leftJoin('u.cares', 'cares')
      .addSelect(['cares.id', 'cares.status', 'cares.createdAt', 'cares.scopedUserId'])
      .leftJoin('cares.items', 'ci')
      .addSelect(['ci.id', 'ci.reasonId', 'ci.createdAt'])
      .leftJoin('ci.reason', 'reason')
      .addSelect(['reason.id', 'reason.key']);

    const conversation = await qb.getOne();
    return conversation;
  }

  async getConversation(
    pageId: string,
    userId: string,
    feedId = '',
    getCareReason = false,
  ): Promise<Conversation> {
    const query = this.conversationRepo
      .createQueryBuilder('c')
      .leftJoinAndMapOne(
        'c.user',
        PageScopedUser,
        'u',
        'u.pageId = c.pageId AND u.scopedUserId = c.scopedUserId',
      )
      .leftJoinAndSelect('u.tags', 'tags')
      .where('c.pageId = :pageId')
      .andWhere('c.scopedUserId = :scopedUserId')
      .andWhere('c.feedId = :feedId')
      .setParameters({ pageId, scopedUserId: userId, feedId });
    if (getCareReason) {
      query.leftJoinAndSelect('u.currentCare', 'uc').leftJoinAndSelect('uc.items', 'uci');
    }
    return query.getOne();
  }

  async updateConversation(
    pageId: string,
    scopedUserId: string,
    body: UpdateConversationDto,
    updatedBy?: number,
    feedId?: string,
    source: Record<string, any> = {},
  ): Promise<Conversation> {
    const [conversation, pageScopedUser] = await Promise.all([
      this.getFullConversation(pageId, scopedUserId, feedId),
      this.suRepo.findOne({
        scopedUserId,
        pageId,
      }),
    ]);

    if (!conversation)
      throw new NotFoundException(`Conversation ${pageId}/${scopedUserId}/${feedId} not found`);

    const updateData: Partial<Conversation> = {};
    updateData.status = pageScopedUser ? pageScopedUser.careStatus : CareStatus.new;
    const { scenarioId, isBotEnabled } = body;

    if (scenarioId) {
      if (scenarioId < 0) {
        if (scenarioId === conversation.careScenarioId * -1) updateData.careScenarioId = null;
      } else {
        const scenario = await this.careScenarioService.getScenario(scenarioId);
        if (scenario) {
          updateData.careScenarioId = scenarioId;
        }
      }
    }

    if (!isNil(isBotEnabled)) {
      updateData.isBotEnabled = isBotEnabled;
    }

    const mConversation = await this.conversationRepo.save(
      plainToInstance(Conversation, {
        ...omit(conversation, ['phones']),
        ...updateData,
        lastUpdatedBy: updatedBy || null,
      }),
    );

    console.log(`conversation after updated`, mConversation);

    await this.socketGateway.emitConversations([
      plainToInstance(Conversation, {
        ...conversation,
        ...updateData,
        lastUpdatedBy: updatedBy || null,
      }),
    ]);

    if (mConversation?.careScenarioId !== conversation.careScenarioId)
      await this.amqpConnection.publish(
        'facebook-conversation-event',
        'after-updated-conversation-with-care-scenario-id',
        {
          oldCareScenarioId: conversation.careScenarioId,
          conversation: mConversation,
          source,
        },
      );
    return mConversation;
  }

  async save(
    data: Partial<Conversation>,
    user?: PageScopedUser,
    readWatermark?: Date,
  ): Promise<Conversation> {
    const { pageId, scopedUserId, updatedAt } = data;
    const feedId = data.feedId || '';
    const conversation = await this.getFullConversation(pageId, scopedUserId, feedId);
    let threadId;
    if (feedId === '' && !conversation?.threadId) {
      try {
        const tokenApi = await this.botApiService.getPageTokenApi(`${pageId}`);
        const response = await tokenApi({
          method: 'GET',
          url: FACEBOOK_API_ENDPOINT.CONVERSATIONS('me'),
          params: {
            user_id: scopedUserId,
            fields: 'link',
          },
        });
        const item = response.data[0];
        const temp = item?.link?.split('/')[3];
        if (isNaN(temp)) {
          // threadId = utils.getFrom(item.link, '?threadid=', '&')
        } else {
          threadId = temp;
        }
        console.log(`found thread id ${scopedUserId} => ${threadId}`, item.link);
      } catch (e) {
        console.log(`find global id ${scopedUserId} save thread id error`, e);
      }
    }
    // if (readWatermark) {
    //   await this.amqpConnection.publish('facebook-conversation-event', 'fetch-conversation-by-id', {
    //     pageId,
    //     scopedUserId,
    //     readWatermark,
    //   });
    // }
    if (conversation) {
      const updateFields = [
        'userGlobalId',
        'conversationThreadId',
        'lastDefaultMessageSent',
        'snippet',
        'lastSentByPage',
        'userReadAt',
        'unread',
        'isSentStartMessage',
      ];
      if (!isEmpty(data.feedId)) updateFields.push('feedId');
      if (data.snippet) updateFields.push('updatedAt');

      const updateData = pick(data, updateFields) as Conversation;

      // if (!conversation.createdAt) {
      //   const firstMessage = await this.messageRepository
      //     .createQueryBuilder('m')
      //     .where('m.scopedUserId = :scopedUserId', { scopedUserId })
      //     .andWhere('m.timestamp IS NOT NULL')
      //     .select('MIN(m.timestamp)', 'timestamp')
      //     .getRawOne();

      //   if (firstMessage.timestamp) updateData.createdAt = firstMessage.timestamp;
      // }

      if (!conversation.createdAt || conversation.createdAt.getTime() === 0) {
        const firstMessages = await this.messageRepository
          .createQueryBuilder('m')
          .where('m.scopedUserId = :scopedUserId', { scopedUserId })
          .select('m.timestamp', 'timestamp')
          .limit(100)
          .getRawMany();
        const firstMessage = firstMessages.sort(
          (p, n) => moment(p.timestamp).valueOf() - moment(n.timestamp).valueOf(),
        )[0];
        console.log('firstMessage', firstMessage?.timestamp);
        let createdAt = firstMessage?.timestamp;
        if (data.updatedAt && (!createdAt || createdAt > data.updatedAt)) {
          createdAt = data.updatedAt;
        }
        if (createdAt) updateData.createdAt = createdAt;
      }
      if (isEmpty(updateData)) return conversation;

      if (updateData.userReadAt) {
        const result = await this.conversationRepo
          .createQueryBuilder('c')
          .andWhere('pageId = :pageId', { pageId })
          .andWhere('scopedUserId = :scopedUserId', {
            scopedUserId,
          })
          .andWhere('(userReadAt IS NULL OR userReadAt < :userReadAt)', {
            userReadAt: updateData.userReadAt,
          })
          .update({ userReadAt: updateData.userReadAt })
          .execute();
        if (result.affected)
          await this.socketGateway.emitUserReadConversation(
            pageId,
            scopedUserId,
            updateData.userReadAt,
          );
        delete updateData.userReadAt;
      }
      if (threadId) {
        updateData.threadId = threadId;
      }
      const result = await this.conversationRepo.update(
        { pageId, scopedUserId, updatedAt: LessThan(updatedAt), feedId },
        updateData,
      );
      // console.log(`update conversation data`, updateData);
      // console.log(`update conversation ${pageId}/${scopedUserId} result`, result);

      const upConversation = plainToInstance(Conversation, {
        ...conversation,
        ...updateData,
      });

      // this.socketGateway.emitConversation(pageId, scopedUserId);
      if (!upConversation.user) upConversation.user = user;
      if (result.affected) {
        if (!isNil(data.snippet)) upConversation.isMessageReceived = true;
        await this.socketGateway.emitConversations([upConversation]);
      }

      return upConversation;
    }
    if (threadId) {
      data.threadId = threadId;
    }

    if (!data.updatedAt) data.updatedAt = new Date();
    else data.createdAt = data.updatedAt;

    const mConversation = await this.conversationRepo.save(plainToInstance(Conversation, data));

    // await this.socketGateway.emitConversation(pageId, scopedUserId, feedId);

    if (!mConversation.user) {
      if (user && isNil(user.careStatus)) user.careStatus = CareStatus.new;
      mConversation.user = user;
    }
    if (!mConversation.userReadAt) {
      if (!isNil(data.snippet)) mConversation.isMessageReceived = true;
      await this.socketGateway.emitConversations([mConversation]);
    }
    return mConversation;
  }

  @RabbitRPC({
    exchange: 'facebook-conversation-event',
    routingKey: 'fetch-conversation-by-id',
    queue: 'ag-facebook-fetch-conversation-by-id',
    errorHandler: rmqErrorsHandler,
    allowNonJsonMessages: true,
  })
  async fetchConversation({ pageId, scopedUserId, readWatermark, force }) {
    const conversation = await this.getFullConversation(pageId, scopedUserId);
    if (conversation && !force && conversation.snippet && !readWatermark) {
      return new Nack(false);
    }
    if (readWatermark) {
      const message = await this.messageRepository.findOne({
        where: {
          timestamp: new Date(readWatermark),
          scopedUserId,
        },
        select: ['id'],
      });
      if (message) {
        return new Nack(false);
      }
    }
    let api;
    try {
      api = await this.botApiService.getPageTokenApi(pageId);
    } catch (e) {
      return new Nack(false);
    }
    if (!api) {
      return new Nack(false);
    }
    try {
      let response;
      try {
        response = await api({
          method: 'GET',
          url: FACEBOOK_API_ENDPOINT.CONVERSATIONS(pageId),
          params: {
            fields: 'participants,id,updated_time,link,snippet,messages.limit(1){from}',
            user_id: scopedUserId,
            limit: 25,
          },
        });
      } catch (e) {
        if (e.code === 190) return new Nack(false);
      }
      if (!response?.data?.[0]) {
        return new Nack(false);
      }
      const con = response.data[0];
      const temp = con.link.split('/')[3];
      const updateData = {
        updatedAt: new Date(con.updated_time),
        snippet: con.snippet,
        conversationThreadId: con.id,
      } as QueryDeepPartialEntity<Conversation>;
      if (!con.updated_time) {
        delete updateData.updatedAt;
      }
      if (isNaN(temp)) {
        // item.threadId = utils.getFrom(e?.link, '?threadid=', '&')
      } else updateData.threadId = temp;
      try {
        const lastSent = con.messages?.data[0]?.from?.id;
        updateData.lastSentByPage = lastSent == pageId;
        updateData.unread = lastSent != pageId;
      } catch (e) {}
      await this.conversationRepo.update({ scopedUserId, pageId, feedId: '' }, updateData);
      await this.socketGateway.emitConversations([
        plainToInstance(Conversation, {
          ...conversation,
          ...updateData,
        }),
      ]);
      await this.amqpConnection.publish(
        'facebook-conversation-event',
        'fetch-page-conversation-message',
        {
          pageId,
          scopedUserId,
          threadId: con.id,
          onePage: !!readWatermark,
        },
      );
    } catch (error) {
      console.log(`sync fb conversation one page ${pageId} error`, error);
      throw error;
    }
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'facebook-conversation-event',
    routingKey: 'fetch-page-conversation',
    queue: 'ag-facebook-fetch-conversation',
    errorHandler: rmqErrorsHandler,
    allowNonJsonMessages: true,
  })
  async fetchPageConversation(payload) {
    const { id, after, skipMessage, force, scopedUserId, afterTime, skipComment } = payload;
    const page = await this.pageRepo.findOne(id, {
      select: ['isSyncConversation', 'isSubscribed'],
    });
    console.log('page', page);
    if (!after) {
      if (!page || (page.isSyncConversation && !skipMessage && !force)) {
        console.log('conversation synced');
        return new Nack(false);
      }
      if (!page.isSubscribed) {
        throw { message: 'page is not subscribed' };
      }
      if (!skipMessage && !scopedUserId && !skipComment) {
        await this.amqpConnection.publish('facebook-conversation-event', 'fetch-page-feed', {
          id,
          afterTime,
        });
      }
      if (!page.isSyncConversation) {
        await this.pageRepo.update(
          { id },
          {
            isSyncConversation: true,
          },
        );
      }
    }

    const api = await this.botApiService.getPageTokenApi(id);
    try {
      const response = await api({
        method: 'GET',
        url: FACEBOOK_API_ENDPOINT.CONVERSATIONS(id),
        params: {
          fields: 'participants,id,updated_time,link,snippet,messages.limit(1){from}',
          after,
          limit: 25,
          user_id: scopedUserId,
        },
      });

      const params: Conversation[] = response?.data
        .map(e => {
          const item = new Conversation();

          item.isSentStartMessage = true;
          item.pageId =
            e?.participants?.data[0]?.id == id
              ? e?.participants?.data[0]?.id
              : e?.participants?.data[1]?.id;
          item.scopedUserId =
            e?.participants?.data[0]?.id != id
              ? e?.participants?.data[0]?.id
              : e?.participants?.data[1]?.id;
          const scopedUser =
            e?.participants?.data[0]?.id != id
              ? e?.participants?.data[0]
              : e?.participants?.data[1];
          if (!scopedUser) {
            return null;
          }
          item.user = plainToInstance(PageScopedUser, {
            pageId: item.pageId,
            scopedUserId: item.scopedUserId,
            name: scopedUser.name,
            globalId: item.userGlobalId,
            id: item.pageId + '_' + item.scopedUserId,
          });
          try {
            const lastSent = e?.messages?.data[0]?.from?.id;
            item.lastSentByPage = lastSent == item.pageId;
            item.unread = lastSent != item.pageId;
          } catch (e) {}
          item.conversationThreadId = e?.id;
          item.updatedAt = new Date(e?.updated_time);
          item.snippet = e?.snippet;
          item.feedId = '';
          const temp = e?.link.split('/')[3];
          if (isNaN(temp)) {
            // item.threadId = utils.getFrom(e?.link, '?threadid=', '&')
          } else item.threadId = temp;
          return item;
        })
        .filter(i => i);
      if (params.length > 0) {
        const conversations = [];
        await Promise.all(
          params.map(async it => {
            if (!it.user) return;

            try {
              console.log('Inserting PageScopedUser:', it.user.constructor.name, it.user.id);
              await this.suRepo
                .createQueryBuilder()
                .insert()
                .values(it.user)
                .orIgnore()
                .execute();
            } catch (error) {
              console.log('Error inserting PageScopedUser:', error.message);
              console.log('User data:', JSON.stringify(it.user, null, 2));
              throw error;
            }

            try {
              // Create a clean conversation entity without null/undefined values
              const conversationEntity = new Conversation();
              Object.keys(it).forEach(key => {
                if (it[key] !== null && it[key] !== undefined && key !== 'user') {
                  conversationEntity[key] = it[key];
                }
              });

              console.log('Inserting Conversation:', conversationEntity.constructor.name);
              await this.conversationRepo
                .createQueryBuilder()
                .insert()
                .values(conversationEntity)
                .orIgnore()
                .execute();
            } catch (error) {
              console.log('Error inserting Conversation:', error.message);
              throw error;
            }

            if (!skipMessage) {
              await this.amqpConnection.publish(
                'facebook-conversation-event',
                'fetch-page-conversation-message',
                {
                  pageId: it?.pageId,
                  scopedUserId: it?.scopedUserId,
                  threadId: it?.conversationThreadId,
                  afterTime,
                },
              );
            }

            conversations.push(it);
          }),
        );

        console.log(`scan conversation of page ${id}`, conversations);
        if (!isEmpty(conversations)) await this.syncGlobalIdConversation(conversations, id);
      }

      if (!!response?.paging?.next && !scopedUserId) {
        if (afterTime && last(params)?.createdAt?.getTime() < new Date(afterTime).getTime()) {
          return params;
        }
        await this.amqpConnection.publish(
          'facebook-conversation-event',
          'fetch-page-conversation',
          {
            id,
            after: response?.paging?.cursors?.after,
            skipMessage,
            afterTime,
          },
        );
      } else {
      }
      return params;
    } catch (error) {
      console.log(`sync fb conversation page ${id} error`, error);
      throw error;
    }
    return new Nack(false);
  }

  async syncGlobalIdConversation(data: Conversation[], pageId: string, tag = 0, attempts = 5) {
    if (attempts < 1) {
      return;
    }
    let threads: IFbThread[] = [];
    const params: Conversation[] = [];
    const updateData = data.filter(i => !i?.userGlobalId);
    console.log(`scan conversation ${pageId} start`, updateData);
    if (isEmpty(updateData) || !CONVERSATION_TAGS[tag]) {
      return false;
    }

    try {
      const api = await this.botApiService.getApi(pageId);
      try {
        threads = await api.getThreadList(
          updateData.length + 5,
          updateData[0].updatedAt.getTime() + 1000,
          [CONVERSATION_TAGS[tag]],
        );
      } catch (e) {
        console.log(`scan conversation ${pageId} error`, e);
      }
      console.log(`scan conversation ${pageId} found ${threads.length}`, threads);
      if (isEmpty(threads)) {
        return this.syncGlobalIdConversation(params, pageId, tag + 1);
      }
      const lastTime = Number(threads[threads.length - 1].timestamp);
      console.log(`scan conversation ${pageId} lastTime`, lastTime);
      const threadLookup = groupBy(
        threads.filter(i => i.threadKey),
        'threadKey',
      );

      const notUpdatedData = [];
      const otherTagData = [];
      const users = [];
      for (const item of data) {
        if (!item?.threadId) {
          continue;
        }
        const thread = (threadLookup[item?.threadId] || [])[0];
        if (!thread) {
          if (item.updatedAt.getTime() < lastTime) notUpdatedData.push(item);
          else {
            otherTagData.push(item);
          }
          continue;
        }
        const user = thread?.participants.find(i => (i.userID = thread?.threadID));
        console.log(`scan conversation ${pageId} user`, user);
        item.userGlobalId = thread?.threadID;
        users.push({
          id: `${item.pageId}_${item.scopedUserId}`,
          pageId: item.pageId,
          scopedUserId: item.scopedUserId,
          name: thread?.name,
          globalId: thread?.threadID,
          gender: user?.gender,
          shortName: user?.shortName,
          avatar: user?.profilePicture,
        });
        params.push(item);
      }

      if (params.length > 0) {
        try {
          console.log(`scan conversation ${pageId} save conversation`, params);
          await this.conversationRepo.save(params.map(i => omitBy(i, isNil)));
          await this.suRepo.save(users);
        } catch (e) {
          console.log(`scan conversation ${pageId} save conversation error`, e);
        }
      }
      if (!isEmpty(otherTagData)) {
        console.log('other tag', otherTagData.length);
        await this.syncGlobalIdConversation(notUpdatedData, pageId, tag + 1);
      }
      if (!isEmpty(notUpdatedData)) {
        console.log('not updated full', notUpdatedData.length);
        return await this.syncGlobalIdConversation(notUpdatedData, pageId, attempts--);
      }
    } catch (e) {
      console.log(e);
    }
    return false;
  }
}
